import { test, expect } from '@playwright/test';

test.describe('Teachers Management System', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page
    await page.goto('/login');
    
    // Fill in login credentials (assuming test credentials exist)
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.click('button[type="submit"]');
    
    // Wait for redirect to admin dashboard
    await page.waitForURL('/admin');
    
    // Navigate to teachers page
    await page.goto('/admin/teachers');
  });

  test('should display teachers list page correctly', async ({ page }) => {
    // Check page title and header
    await expect(page.locator('h1:has-text("Gerenciar Professores")')).toBeVisible();
    
    // Check for "Novo Professor" button
    await expect(page.locator('button:has-text("Novo Professor")')).toBeVisible();
    
    // Check for search input
    await expect(page.locator('input[placeholder*="<PERSON><PERSON> professores"]')).toBeVisible();
    
    // Check for teachers table or empty state
    const teachersTable = page.locator('table, div:has-text("Nenhum professor encontrado")');
    await expect(teachersTable).toBeVisible();
  });

  test('should create a new teacher successfully', async ({ page }) => {
    // Click "Novo Professor" button
    await page.click('button:has-text("Novo Professor")');
    
    // Wait for the new teacher form to load
    await page.waitForSelector('form', { timeout: 10000 });
    
    // Fill in teacher details
    const teacherName = `Professor Teste ${Date.now()}`;
    await page.fill('input[placeholder*="Nome completo"]', teacherName);
    await page.fill('input[placeholder*="<EMAIL>"]', `professor${Date.now()}@test.com`);
    await page.fill('input[placeholder*="99999-9999"]', '(11) 98765-4321');
    await page.fill('input[placeholder*="Matemática"]', 'Matemática e Física');
    await page.fill('textarea[placeholder*="Observações"]', 'Professor especialista em exatas');
    
    // Submit the form
    await page.click('button:has-text("Salvar Professor")');
    
    // Wait for redirect to list page
    await page.waitForURL('/admin/teachers');
    
    // Verify success message
    await expect(page.locator('div:has-text("Professor criado com sucesso")')).toBeVisible();
    
    // Verify teacher appears in the list
    await expect(page.locator(`div:has-text("${teacherName}")`)).toBeVisible();
  });

  test('should edit a teacher with autosave functionality', async ({ page }) => {
    // Click on the first edit button (assuming at least one teacher exists)
    const editButton = page.locator('button:has(svg[data-lucide="edit"])').first();
    
    if (await editButton.count() > 0) {
      await editButton.click();
      
      // Wait for the edit form to load
      await page.waitForSelector('form', { timeout: 10000 });
      
      // Verify autosave indicator is present
      await expect(page.locator('[data-testid="auto-save-indicator"]')).toBeVisible();
      
      // Get the current teacher name
      const nameInput = page.locator('input[placeholder*="Nome completo"]').first();
      const currentName = await nameInput.inputValue();
      
      // Modify the teacher name
      const newName = `${currentName} - Editado`;
      await nameInput.fill(newName);
      
      // Wait for autosave to trigger (should happen after 3 seconds)
      await page.waitForTimeout(4000);
      
      // Check that autosave indicator shows "saved"
      const autoSaveIndicator = page.locator('[data-testid="auto-save-indicator"]');
      await expect(autoSaveIndicator).toContainText('Salvo');
      
      // Navigate back to list
      await page.click('button:has-text("Voltar para Lista")');
      
      // Verify the name was updated in the list
      await expect(page.locator(`div:has-text("${newName}")`)).toBeVisible();
    } else {
      console.log('No teachers found to edit, skipping edit test');
    }
  });

  test('should search for teachers', async ({ page }) => {
    // Type in search box
    const searchInput = page.locator('input[placeholder*="Buscar professores"]');
    await searchInput.fill('Professor');
    
    // Click search button or press Enter
    await page.keyboard.press('Enter');
    
    // Wait for search results
    await page.waitForTimeout(1000);
    
    // Verify search was performed (either results or "no teachers found" message)
    const searchResults = page.locator('table, div:has-text("Nenhum professor encontrado")');
    await expect(searchResults).toBeVisible();
  });

  test('should delete a teacher', async ({ page }) => {
    // First, check if there are any teachers to delete
    const deleteButton = page.locator('button:has(svg[data-lucide="trash-2"])').first();
    
    if (await deleteButton.count() > 0) {
      // Click delete button
      await deleteButton.click();
      
      // Wait for confirmation dialog
      await expect(page.locator('div:has-text("Excluir Professor")')).toBeVisible();
      
      // Confirm deletion
      await page.click('button:has-text("Excluir")');
      
      // Verify success message
      await expect(page.locator('div:has-text("Professor excluído com sucesso")')).toBeVisible();
    } else {
      console.log('No teachers found to delete, skipping delete test');
    }
  });

  test('should validate required fields when creating teacher', async ({ page }) => {
    // Click "Novo Professor" button
    await page.click('button:has-text("Novo Professor")');
    
    // Wait for the new teacher form to load
    await page.waitForSelector('form', { timeout: 10000 });
    
    // Try to submit without filling required fields
    await page.click('button:has-text("Salvar Professor")');
    
    // Should show validation error for name
    await expect(page.locator('div:has-text("Nome é obrigatório")')).toBeVisible();
  });

  test('should display teacher statistics correctly', async ({ page }) => {
    // Check for the statistics display (number of teachers)
    const statisticsText = page.locator('p:has-text("professor"), p:has-text("encontrado")');
    await expect(statisticsText).toBeVisible();
    
    // The text should contain a number (could be 0 or more)
    const text = await statisticsText.textContent();
    expect(text).toMatch(/\d+/);
  });

  test('should show teacher contact information in list', async ({ page }) => {
    // Check if there are teachers in the list
    const teacherRows = page.locator('table tbody tr');
    const rowCount = await teacherRows.count();
    
    if (rowCount > 0) {
      // Check that contact information columns are present
      await expect(page.locator('th:has-text("Contato")')).toBeVisible();
      
      // Check for email and phone icons in the first row
      const firstRow = teacherRows.first();
      const emailIcon = firstRow.locator('svg[data-lucide="mail"]');
      const phoneIcon = firstRow.locator('svg[data-lucide="phone"]');
      
      // At least one contact method should be visible
      const hasEmail = await emailIcon.count() > 0;
      const hasPhone = await phoneIcon.count() > 0;
      
      if (!hasEmail && !hasPhone) {
        console.log('Teacher has no contact information, which is allowed');
      }
    } else {
      console.log('No teachers found to check contact information');
    }
  });

  test('should show specialization information', async ({ page }) => {
    // Check if specialization column exists
    await expect(page.locator('th:has-text("Especialização")')).toBeVisible();
    
    // Check if there are teachers in the list
    const teacherRows = page.locator('table tbody tr');
    const rowCount = await teacherRows.count();
    
    if (rowCount > 0) {
      // Check the specialization cell content
      const firstRow = teacherRows.first();
      const specializationCell = firstRow.locator('td').nth(2); // Assuming it's the 3rd column (0-indexed)
      
      // Should show either specialization or "Não informado"
      const cellText = await specializationCell.textContent();
      expect(cellText).toBeTruthy();
    } else {
      console.log('No teachers found to check specialization');
    }
  });

  test('should handle navigation correctly', async ({ page }) => {
    // Test navigation to new teacher form
    await page.click('button:has-text("Novo Professor")');
    await expect(page.locator('h1:has-text("Novo Professor")')).toBeVisible();
    
    // Test back navigation
    await page.click('button:has-text("Cancelar")');
    await expect(page.locator('h1:has-text("Gerenciar Professores")')).toBeVisible();
  });
});