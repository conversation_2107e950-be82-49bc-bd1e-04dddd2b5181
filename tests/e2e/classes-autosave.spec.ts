import { test, expect } from '@playwright/test';

test.describe('Classes Autosave', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page
    await page.goto('/login');
    
    // Fill in login credentials (assuming test credentials exist)
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.click('button[type="submit"]');
    
    // Wait for redirect to admin dashboard
    await page.waitForURL('/admin');
    
    // Navigate to classes page
    await page.goto('/admin/classes');
  });

  test('should autosave changes when editing a class', async ({ page }) => {
    // Click on the first edit button (assuming at least one class exists)
    const editButton = page.locator('button:has-text("Edit")').first();
    if (await editButton.count() > 0) {
      await editButton.click();
      
      // Wait for the edit form to load
      await page.waitForSelector('[data-testid="auto-save-indicator"]', { timeout: 10000 });
      
      // Get the current class name
      const nameInput = page.locator('input[label="Nome da Turma"], input:has-text("Nome da Turma")').first();
      const currentName = await nameInput.inputValue();
      
      // Modify the class name
      const newName = `${currentName} - Edited`;
      await nameInput.fill(newName);
      
      // Wait for autosave to trigger (should happen after 3 seconds)
      await page.waitForTimeout(4000);
      
      // Check that autosave indicator shows "saved"
      const autoSaveIndicator = page.locator('[data-testid="auto-save-indicator"]');
      await expect(autoSaveIndicator).toContainText('Salvo');
      
      // Refresh the page to verify changes were saved
      await page.reload();
      
      // Verify the name was updated
      await expect(nameInput).toHaveValue(newName);
    } else {
      console.log('No classes found to edit, skipping autosave test');
    }
  });

  test('should show autosave indicator states correctly', async ({ page }) => {
    // Go to edit mode for the first class (if exists)
    const editButton = page.locator('button:has-text("Edit")').first();
    if (await editButton.count() > 0) {
      await editButton.click();
      
      // Wait for the edit form to load
      await page.waitForSelector('[data-testid="auto-save-indicator"]', { timeout: 10000 });
      
      const nameInput = page.locator('input[label="Nome da Turma"], input:has-text("Nome da Turma")').first();
      const autoSaveIndicator = page.locator('[data-testid="auto-save-indicator"]');
      
      // Make a change
      await nameInput.fill('Test Autosave Change');
      
      // Should show "saving" state
      await expect(autoSaveIndicator).toContainText('Salvando');
      
      // Wait for save to complete
      await page.waitForTimeout(4000);
      
      // Should show "saved" state
      await expect(autoSaveIndicator).toContainText('Salvo');
    } else {
      console.log('No classes found to edit, skipping indicator test');
    }
  });

  test('should not show FormActions in edit mode', async ({ page }) => {
    // Go to edit mode for the first class (if exists)
    const editButton = page.locator('button:has-text("Edit")').first();
    if (await editButton.count() > 0) {
      await editButton.click();
      
      // Wait for the edit form to load
      await page.waitForSelector('form', { timeout: 10000 });
      
      // Verify that traditional FormActions (Save/Cancel buttons) are not present
      await expect(page.locator('button:has-text("Salvar Alterações")')).not.toBeVisible();
      await expect(page.locator('button:has-text("Cancelar")')).not.toBeVisible();
      
      // Verify that "Voltar para Lista" button is present instead
      await expect(page.locator('button:has-text("Voltar para Lista")')).toBeVisible();
    } else {
      console.log('No classes found to edit, skipping FormActions test');
    }
  });

  test('should still show FormActions in new mode', async ({ page }) => {
    // Click "Nova Turma" button
    await page.click('button:has-text("Nova Turma")');
    
    // Wait for the new form to load
    await page.waitForSelector('form', { timeout: 10000 });
    
    // Verify that FormActions are present in new mode
    await expect(page.locator('button:has-text("Salvar Turma")')).toBeVisible();
    await expect(page.locator('button:has-text("Cancelar")')).toBeVisible();
    
    // Verify that autosave indicator is NOT present in new mode
    await expect(page.locator('[data-testid="auto-save-indicator"]')).not.toBeVisible();
  });
});