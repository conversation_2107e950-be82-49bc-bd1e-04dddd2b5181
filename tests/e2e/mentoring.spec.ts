import { test, expect } from '@playwright/test';

test.describe('Mentoring System', () => {
  test('Mentoring pages are accessible', async ({ page }) => {
    // Test admin mentoring agenda page
    await page.goto('/admin/mentoring/agenda');
    await expect(page.locator('h1')).toContainText('Agenda de Mentorias');

    // Test admin mentoring bookings page
    await page.goto('/admin/mentoring/bookings');
    await expect(page.locator('h1')).toContainText('Agendamentos de Mentoria');

    // Test student mentoring page
    await page.goto('/mentoring');
    await expect(page.locator('h1')).toContainText('Mentorias Disponíveis');
  });

  test('Mentoring agenda form elements are present', async ({ page }) => {
    await page.goto('/admin/mentoring/agenda?mode=new');
    
    // Check form elements are present
    await expect(page.locator('text=Professor')).toBeVisible();
    await expect(page.locator('text=Dia da <PERSON>')).toBeVisible();
    await expect(page.locator('text=Duração')).toBeVisible();
    await expect(page.locator('text=Horário de Início')).toBeVisible();
    await expect(page.locator('text=Google Calendar')).toBeVisible();
    await expect(page.locator('text=Criar Agenda')).toBeVisible();
  });

  test('Mentoring booking form elements are present', async ({ page }) => {
    await page.goto('/admin/mentoring/bookings');
    
    // Check filter elements are present
    await expect(page.locator('text=Filtros')).toBeVisible();
    await expect(page.locator('input[placeholder*="Buscar"]')).toBeVisible();
    await expect(page.locator('text=Agendamentos')).toBeVisible();
  });

  test('Student mentoring page has search functionality', async ({ page }) => {
    await page.goto('/mentoring');
    
    // Check search elements
    await expect(page.locator('input[placeholder*="Buscar"]')).toBeVisible();
    await expect(page.locator('text=Buscar')).toBeVisible();
    await expect(page.locator('text=Professores Disponíveis')).toBeVisible();
  });

  test('Mentoring pages are responsive', async ({ page }) => {
    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto('/admin/mentoring/agenda');
    await expect(page.locator('h1')).toContainText('Agenda de Mentorias');
    
    await page.goto('/mentoring');
    await expect(page.locator('h1')).toContainText('Mentorias Disponíveis');
  });
});