import { test, expect } from '@playwright/test';
import { prisma } from '../../src/lib/prisma';
import { recoveryService } from '../../src/lib/services/recovery.service';
import { addDays, addHours } from 'date-fns';

/**
 * QA Test Suite for Lesson Recovery System
 * 
 * This comprehensive test suite validates the complete lesson recovery workflow
 * from absence notification to successful makeup lesson booking.
 */
test.describe('Recovery System QA Tests', () => {
  let qaTestData: {
    student: any;
    course: any;
    originalClass: any;
    makeupClass: any;
    missedLesson: any;
    makeupLesson: any;
    attendance: any;
    otp: any;
  };

  test.beforeAll(async () => {
    // Clean up any existing QA test data
    await cleanupQATestData();

    // Create comprehensive test data
    qaTestData = await createQATestData();
  });

  test.afterAll(async () => {
    // Clean up QA test data
    await cleanupQATestData();
  });

  test('QA-001: Complete recovery workflow from absence to booking', async ({ page }) => {
    const { otp, makeupLesson, student, course, missedLesson } = qaTestData;

    // Step 1: Access recovery page with OTP
    await page.goto(`/recovery?token=${otp.token}`);
    
    // Step 2: Verify page loads correctly
    await expect(page.locator('h1')).toContainText('Reposição de Aula', { timeout: 10000 });
    
    // Step 3: Verify student information
    await expect(page.locator(`text=${student.name}`)).toBeVisible();
    await expect(page.locator(`text=${course.name}`)).toBeVisible();
    await expect(page.locator(`text=${missedLesson.title}`)).toBeVisible();

    // Step 4: Verify available lessons section
    await expect(page.locator('text=Aulas Disponíveis para Reposição')).toBeVisible();
    await expect(page.locator(`text=${makeupLesson.title}`)).toBeVisible();
    
    // Step 5: Verify lesson number badge is displayed
    await expect(page.locator('text=Aula 1')).toBeVisible();

    // Step 6: Select makeup lesson
    await page.click(`button:has-text("${makeupLesson.title}")`);
    await expect(page.locator('text=Selecionada')).toBeVisible();

    // Step 7: Book the lesson
    const bookButton = page.locator('button:has-text("Agendar Reposição")');
    await expect(bookButton).toBeEnabled();
    await bookButton.click();

    // Step 8: Verify loading state
    await expect(page.locator('text=Agendando...')).toBeVisible();

    // Step 9: Verify success message
    await expect(page.locator('text=Sucesso!')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('text=Reposição agendada com sucesso')).toBeVisible();

    // Step 10: Verify database changes
    const booking = await prisma.recoveryBooking.findFirst({
      where: {
        studentId: student.id,
        originalLessonId: missedLesson.id,
        makeupLessonId: makeupLesson.id
      }
    });
    expect(booking).toBeTruthy();
    expect(booking?.status).toBe('booked');

    // Step 11: Verify attendance record created
    const makeupAttendance = await prisma.attendance.findFirst({
      where: {
        studentId: student.id,
        lessonId: makeupLesson.id,
        originalLessonId: missedLesson.id
      }
    });
    expect(makeupAttendance).toBeTruthy();
    expect(makeupAttendance?.status).toBe('present');

    // Step 12: Verify OTP is marked as used
    const usedOtp = await prisma.recoveryOtp.findUnique({
      where: { id: otp.id }
    });
    expect(usedOtp?.usedAt).toBeTruthy();
  });

  test('QA-002: Security validation - Invalid token handling', async ({ page }) => {
    // Test various invalid token scenarios
    const invalidTokens = [
      'invalid-token',
      '',
      'expired-token-12345',
      '../../etc/passwd',
      '<script>alert("xss")</script>',
      'null',
      'undefined'
    ];

    for (const token of invalidTokens) {
      await page.goto(`/recovery?token=${token}`);
      await expect(page.locator('text=Erro de Acesso')).toBeVisible();
      await expect(page.locator('text=Invalid or expired OTP token')).toBeVisible();
    }
  });

  test('QA-003: Data integrity - Prevent duplicate bookings', async ({ page }) => {
    const { otp, makeupLesson, student, missedLesson } = qaTestData;

    // First booking
    await page.goto(`/recovery?token=${otp.token}`);
    await page.click(`button:has-text("${makeupLesson.title}")`);
    await page.click('button:has-text("Agendar Reposição")');
    await expect(page.locator('text=Sucesso!')).toBeVisible();

    // Generate new OTP for same absence
    const newOtp = await recoveryService.generateRecoveryOtp(
      student.id,
      qaTestData.attendance.id
    );

    // Attempt second booking
    await page.goto(`/recovery?token=${newOtp.token}`);
    await page.click(`button:has-text("${makeupLesson.title}")`);
    await page.click('button:has-text("Agendar Reposição")');

    // Should show error
    await expect(page.locator('text=already booked')).toBeVisible();

    // Verify only one booking exists
    const bookings = await prisma.recoveryBooking.findMany({
      where: {
        studentId: student.id,
        originalLessonId: missedLesson.id
      }
    });
    expect(bookings).toHaveLength(1);
  });

  test('QA-004: Business logic - Lesson number filtering', async ({ page }) => {
    const { course, student, attendance } = qaTestData;

    // Create additional makeup class with lessons of different numbers
    const extraMakeupClass = await prisma.class.create({
      data: {
        courseId: course.id,
        name: 'Extra QA Makeup Class',
        startDate: new Date(),
        endDate: addDays(new Date(), 90),
        isActive: true
      }
    });

    // Create lessons with same and different lesson numbers
    const sameLessonNumber = await prisma.lesson.create({
      data: {
        classId: extraMakeupClass.id,
        title: 'Same Lesson Number',
        lessonNumber: 1, // Same as missed lesson
        scheduledDate: addDays(new Date(), 10),
        isCompleted: false
      }
    });

    const differentLessonNumber = await prisma.lesson.create({
      data: {
        classId: extraMakeupClass.id,
        title: 'Different Lesson Number',
        lessonNumber: 3, // Different from missed lesson
        scheduledDate: addDays(new Date(), 12),
        isCompleted: false
      }
    });

    // Generate new OTP
    const testOtp = await recoveryService.generateRecoveryOtp(
      student.id,
      attendance.id
    );

    await page.goto(`/recovery?token=${testOtp.token}`);

    // Should show lessons with same lesson number only
    await expect(page.locator(`text=${sameLessonNumber.title}`)).toBeVisible();
    await expect(page.locator(`text=${differentLessonNumber.title}`)).not.toBeVisible();

    // Clean up
    await prisma.lesson.deleteMany({
      where: { classId: extraMakeupClass.id }
    });
    await prisma.class.delete({ where: { id: extraMakeupClass.id } });
  });

  test('QA-005: Performance - Page load times', async ({ page }) => {
    const { otp } = qaTestData;

    const startTime = Date.now();
    await page.goto(`/recovery?token=${otp.token}`);
    
    // Page should load within 3 seconds
    await expect(page.locator('h1')).toContainText('Reposição de Aula', { timeout: 3000 });
    
    const loadTime = Date.now() - startTime;
    expect(loadTime).toBeLessThan(3000);

    // Available lessons should load within 2 seconds
    const lessonsStartTime = Date.now();
    await expect(page.locator('text=Aulas Disponíveis para Reposição')).toBeVisible();
    
    const lessonsLoadTime = Date.now() - lessonsStartTime;
    expect(lessonsLoadTime).toBeLessThan(2000);
  });

  test('QA-006: Accessibility - ARIA and keyboard navigation', async ({ page }) => {
    const { otp, makeupLesson } = qaTestData;

    await page.goto(`/recovery?token=${otp.token}`);
    await expect(page.locator('h1')).toBeVisible();

    // Check ARIA attributes on lesson selection buttons
    const lessonButton = page.locator(`button:has-text("${makeupLesson.title}")`);
    await expect(lessonButton).toHaveAttribute('role', 'radio');
    await expect(lessonButton).toHaveAttribute('aria-checked', 'false');

    // Test keyboard navigation
    await lessonButton.focus();
    await page.keyboard.press('Space');
    await expect(lessonButton).toHaveAttribute('aria-checked', 'true');

    // Test tab navigation
    await page.keyboard.press('Tab');
    await expect(page.locator('button:has-text("Cancelar")')).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(page.locator('button:has-text("Agendar Reposição")')).toBeFocused();
  });

  test('QA-007: Responsive design - Mobile compatibility', async ({ page }) => {
    const { otp } = qaTestData;

    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    await page.goto(`/recovery?token=${otp.token}`);
    await expect(page.locator('h1')).toBeVisible();

    // Check layout adapts to mobile
    const container = page.locator('.max-w-4xl');
    await expect(container).toBeVisible();

    // Cards should stack on mobile
    const cards = page.locator('.grid-cols-1');
    await expect(cards).toBeVisible();

    // Buttons should be full width on mobile
    const bookButton = page.locator('button:has-text("Agendar Reposição")');
    await expect(bookButton).toBeVisible();
  });

  test('QA-008: Error handling - Network failures', async ({ page }) => {
    const { otp, makeupLesson } = qaTestData;

    await page.goto(`/recovery?token=${otp.token}`);
    await page.click(`button:has-text("${makeupLesson.title}")`);

    // Simulate network failure
    await page.route('/api/recovery/book', route => {
      route.abort('failed');
    });

    await page.click('button:has-text("Agendar Reposição")');

    // Should handle network error gracefully
    await expect(page.locator('text=Erro')).toBeVisible();
  });

  test('QA-009: Data validation - Course makeup policy', async ({ page }) => {
    // Create course that doesn't allow makeup
    const noMakeupCourse = await prisma.course.create({
      data: {
        name: 'No Makeup QA Course',
        allowsMakeup: false,
        isActive: true
      }
    });

    const noMakeupClass = await prisma.class.create({
      data: {
        courseId: noMakeupCourse.id,
        name: 'No Makeup QA Class',
        startDate: new Date(),
        isActive: true
      }
    });

    const noMakeupLesson = await prisma.lesson.create({
      data: {
        classId: noMakeupClass.id,
        title: 'No Makeup Lesson',
        scheduledDate: addDays(new Date(), -1),
        isCompleted: true
      }
    });

    const noMakeupAttendance = await prisma.attendance.create({
      data: {
        studentId: qaTestData.student.id,
        lessonId: noMakeupLesson.id,
        status: 'absent'
      }
    });

    // Should reject OTP generation for course that doesn't allow makeup
    await expect(
      recoveryService.generateRecoveryOtp(qaTestData.student.id, noMakeupAttendance.id)
    ).rejects.toThrow('This course does not allow makeup classes');

    // Clean up
    await prisma.attendance.delete({ where: { id: noMakeupAttendance.id } });
    await prisma.lesson.delete({ where: { id: noMakeupLesson.id } });
    await prisma.class.delete({ where: { id: noMakeupClass.id } });
    await prisma.course.delete({ where: { id: noMakeupCourse.id } });
  });

  test('QA-010: End-to-end integration - Attendance system', async ({ page }) => {
    const { otp, makeupLesson, student, missedLesson } = qaTestData;

    // Complete booking process
    await page.goto(`/recovery?token=${otp.token}`);
    await page.click(`button:has-text("${makeupLesson.title}")`);
    await page.click('button:has-text("Agendar Reposição")');
    await expect(page.locator('text=Sucesso!')).toBeVisible();

    // Verify integration with attendance system
    const makeupAttendance = await prisma.attendance.findFirst({
      where: {
        studentId: student.id,
        lessonId: makeupLesson.id,
        originalLessonId: missedLesson.id
      }
    });

    expect(makeupAttendance).toBeTruthy();
    expect(makeupAttendance?.status).toBe('present');
    expect(makeupAttendance?.markedAt).toBeTruthy();
    expect(makeupAttendance?.notes).toContain('Makeup lesson for absence');

    // Verify original absence record is unchanged
    const originalAttendance = await prisma.attendance.findUnique({
      where: { id: qaTestData.attendance.id }
    });
    expect(originalAttendance?.status).toBe('absent');
  });
});

// Helper functions
async function createQATestData() {
  const course = await prisma.course.create({
    data: {
      name: 'QA Recovery Test Course',
      description: 'Course for QA recovery testing',
      numberOfLessons: 10,
      allowsMakeup: true,
      isActive: true
    }
  });

  const student = await prisma.student.create({
    data: {
      name: 'QA Recovery Test Student',
      email: '<EMAIL>',
      phone: '+5511777777777',
      status: 'active'
    }
  });

  const originalClass = await prisma.class.create({
    data: {
      courseId: course.id,
      name: 'QA Original Class',
      startDate: new Date(),
      endDate: addDays(new Date(), 90),
      isActive: true
    }
  });

  const makeupClass = await prisma.class.create({
    data: {
      courseId: course.id,
      name: 'QA Makeup Class',
      startDate: new Date(),
      endDate: addDays(new Date(), 90),
      isActive: true
    }
  });

  const missedLesson = await prisma.lesson.create({
    data: {
      classId: originalClass.id,
      title: 'QA Missed Lesson',
      lessonNumber: 1,
      scheduledDate: addDays(new Date(), -1),
      isCompleted: true
    }
  });

  const makeupLesson = await prisma.lesson.create({
    data: {
      classId: makeupClass.id,
      title: 'QA Makeup Lesson',
      lessonNumber: 1,
      scheduledDate: addDays(new Date(), 7),
      isCompleted: false
    }
  });

  await prisma.enrollment.create({
    data: {
      studentId: student.id,
      courseId: course.id,
      classId: originalClass.id,
      status: 'active'
    }
  });

  const attendance = await prisma.attendance.create({
    data: {
      studentId: student.id,
      lessonId: missedLesson.id,
      status: 'absent'
    }
  });

  const otp = await recoveryService.generateRecoveryOtp(student.id, attendance.id);

  return {
    student,
    course,
    originalClass,
    makeupClass,
    missedLesson,
    makeupLesson,
    attendance,
    otp
  };
}

async function cleanupQATestData() {
  // Clean up in reverse dependency order
  await prisma.recoveryBooking.deleteMany({
    where: { student: { name: { contains: 'QA Recovery Test' } } }
  });
  await prisma.recoveryOtp.deleteMany({
    where: { student: { name: { contains: 'QA Recovery Test' } } }
  });
  await prisma.recoveryReminder.deleteMany({
    where: { student: { name: { contains: 'QA Recovery Test' } } }
  });
  await prisma.attendance.deleteMany({
    where: { student: { name: { contains: 'QA Recovery Test' } } }
  });
  await prisma.enrollment.deleteMany({
    where: { student: { name: { contains: 'QA Recovery Test' } } }
  });
  await prisma.lesson.deleteMany({
    where: { class: { name: { contains: 'QA' } } }
  });
  await prisma.class.deleteMany({
    where: { name: { contains: 'QA' } }
  });
  await prisma.student.deleteMany({
    where: { name: { contains: 'QA Recovery Test' } }
  });
  await prisma.course.deleteMany({
    where: { name: { contains: 'QA Recovery Test' } }
  });
}