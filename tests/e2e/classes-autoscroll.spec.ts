import { test, expect } from '@playwright/test';

test.describe('Classes Auto-scroll to Current Lesson', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page
    await page.goto('/login');
    
    // Fill in login credentials (assuming test credentials exist)
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.click('button[type="submit"]');
    
    // Wait for redirect to admin dashboard
    await page.waitForURL('/admin');
    
    // Navigate to classes page
    await page.goto('/admin/classes');
  });

  test('should auto-scroll to current lesson when expanding class details', async ({ page }) => {
    // Find the first class expansion button
    const expandButton = page.locator('button svg:has-text("ChevronRight"), button:has(svg)').first();
    
    if (await expandButton.count() > 0) {
      // Click to expand the class details
      await expandButton.click();
      
      // Wait for lessons to load
      await page.waitForSelector('[data-lesson-id]', { timeout: 10000 });
      
      // Check if any lesson is marked as current (has "● Atual" indicator)
      const currentLessonIndicator = page.locator('span:has-text("● Atual")');
      
      if (await currentLessonIndicator.count() > 0) {
        // Get the current lesson element
        const currentLesson = currentLessonIndicator.locator('..').locator('..');
        
        // Check if the current lesson is visible in the viewport
        await expect(currentLesson).toBeInViewport();
        
        // Verify the current lesson has the correct highlighting
        await expect(currentLesson).toHaveClass(/ring-2 ring-\[#667eea\] bg-blue-50/);
      } else {
        console.log('No current lesson found, skipping auto-scroll test');
      }
    } else {
      console.log('No classes found to expand, skipping auto-scroll test');
    }
  });

  test('should highlight the current lesson with visual indicators', async ({ page }) => {
    // Expand the first class
    const expandButton = page.locator('button svg:has-text("ChevronRight"), button:has(svg)').first();
    
    if (await expandButton.count() > 0) {
      await expandButton.click();
      
      // Wait for lessons to load
      await page.waitForSelector('[data-lesson-id]', { timeout: 10000 });
      
      // Check for current lesson indicator
      const currentLessonIndicator = page.locator('span:has-text("● Atual")');
      
      if (await currentLessonIndicator.count() > 0) {
        // Verify the current lesson has the blue ring and background
        const currentLessonElement = currentLessonIndicator.locator('..').locator('..');
        await expect(currentLessonElement).toHaveClass(/ring-2/);
        await expect(currentLessonElement).toHaveClass(/bg-blue-50/);
        
        // Verify the lesson title is colored blue
        const lessonTitle = currentLessonIndicator.locator('..');
        await expect(lessonTitle).toHaveClass(/text-\[#667eea\]/);
        
        // Verify the "● Atual" indicator is present
        await expect(currentLessonIndicator).toBeVisible();
        await expect(currentLessonIndicator).toContainText('● Atual');
      } else {
        console.log('No current lesson found, skipping highlight test');
      }
    } else {
      console.log('No classes found to expand, skipping highlight test');
    }
  });

  test('should scroll to the last lesson if all lessons are in the past', async ({ page }) => {
    // This test assumes there's at least one class with completed lessons
    const expandButton = page.locator('button svg:has-text("ChevronRight"), button:has(svg)').first();
    
    if (await expandButton.count() > 0) {
      await expandButton.click();
      
      // Wait for lessons to load
      await page.waitForSelector('[data-lesson-id]', { timeout: 10000 });
      
      // Get all lesson elements
      const lessonElements = page.locator('[data-lesson-id]');
      const lessonCount = await lessonElements.count();
      
      if (lessonCount > 0) {
        // Check if there's a current lesson indicator
        const currentLessonIndicator = page.locator('span:has-text("● Atual")');
        
        if (await currentLessonIndicator.count() > 0) {
          // If there's a current lesson, it should be visible
          const currentLesson = currentLessonIndicator.locator('..').locator('..');
          await expect(currentLesson).toBeInViewport();
        } else {
          // If no current lesson (all past), the last lesson should be highlighted
          const lastLesson = lessonElements.last();
          await expect(lastLesson).toBeInViewport();
        }
      } else {
        console.log('No lessons found, skipping scroll test');
      }
    } else {
      console.log('No classes found to expand, skipping scroll test');
    }
  });

  test('should handle multiple class expansions correctly', async ({ page }) => {
    // Get all expand buttons
    const expandButtons = page.locator('button svg:has-text("ChevronRight"), button:has(svg)');
    const buttonCount = await expandButtons.count();
    
    if (buttonCount >= 2) {
      // Expand first class
      await expandButtons.nth(0).click();
      await page.waitForTimeout(1000);
      
      // Expand second class
      await expandButtons.nth(1).click();
      await page.waitForTimeout(1000);
      
      // Check that both classes show their lessons
      const lessonContainers = page.locator('[data-lesson-id]');
      const totalLessons = await lessonContainers.count();
      
      // Should have lessons from both classes
      expect(totalLessons).toBeGreaterThan(0);
      
      // Each class should have its own current lesson highlighted (if applicable)
      const currentLessonIndicators = page.locator('span:has-text("● Atual")');
      const currentLessonCount = await currentLessonIndicators.count();
      
      // Should be at most one current lesson per class
      expect(currentLessonCount).toBeLessThanOrEqual(2);
    } else {
      console.log('Need at least 2 classes to test multiple expansions, skipping test');
    }
  });

  test('should maintain scroll position when collapsing and re-expanding', async ({ page }) => {
    const expandButton = page.locator('button svg:has-text("ChevronRight"), button:has(svg)').first();
    
    if (await expandButton.count() > 0) {
      // Expand the class
      await expandButton.click();
      await page.waitForSelector('[data-lesson-id]', { timeout: 10000 });
      
      // Get the position of a current lesson if it exists
      const currentLessonIndicator = page.locator('span:has-text("● Atual")');
      let currentLessonPosition;
      
      if (await currentLessonIndicator.count() > 0) {
        const currentLesson = currentLessonIndicator.locator('..').locator('..');
        currentLessonPosition = await currentLesson.boundingBox();
        
        // Collapse the class
        const collapseButton = page.locator('button svg:has-text("ChevronDown"), button:has(svg)').first();
        await collapseButton.click();
        await page.waitForTimeout(500);
        
        // Re-expand the class
        const reExpandButton = page.locator('button svg:has-text("ChevronRight"), button:has(svg)').first();
        await reExpandButton.click();
        await page.waitForSelector('[data-lesson-id]', { timeout: 10000 });
        
        // Check that the current lesson is still visible and highlighted
        const reloadedCurrentLesson = page.locator('span:has-text("● Atual")').locator('..').locator('..');
        await expect(reloadedCurrentLesson).toBeInViewport();
        await expect(reloadedCurrentLesson).toHaveClass(/ring-2/);
      } else {
        console.log('No current lesson found, skipping position maintenance test');
      }
    } else {
      console.log('No classes found to expand, skipping position maintenance test');
    }
  });
});