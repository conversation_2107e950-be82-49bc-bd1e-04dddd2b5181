import { test, expect, Page } from '@playwright/test';
import { prisma } from '../../src/lib/prisma';
import { recoveryService } from '../../src/lib/services/recovery.service';
import { addDays, addHours } from 'date-fns';

test.describe('Lesson Recovery System', () => {
  let testStudent: any;
  let testCourse: any;
  let testClass: any;
  let testLesson: any;
  let makeupClass: any;
  let makeupLesson: any;
  let attendanceRecord: any;
  let recoveryOtp: any;

  test.beforeEach(async () => {
    // Clean up any existing test data
    await prisma.recoveryBooking.deleteMany({
      where: { student: { name: { contains: 'Test Recovery Student' } } }
    });
    await prisma.recoveryOtp.deleteMany({
      where: { student: { name: { contains: 'Test Recovery Student' } } }
    });
    await prisma.recoveryReminder.deleteMany({
      where: { student: { name: { contains: 'Test Recovery Student' } } }
    });
    await prisma.attendance.deleteMany({
      where: { student: { name: { contains: 'Test Recovery Student' } } }
    });
    await prisma.enrollment.deleteMany({
      where: { student: { name: { contains: 'Test Recovery Student' } } }
    });
    await prisma.lesson.deleteMany({
      where: { class: { name: { contains: 'Test Recovery Class' } } }
    });
    await prisma.class.deleteMany({
      where: { name: { contains: 'Test Recovery Class' } }
    });
    await prisma.student.deleteMany({
      where: { name: { contains: 'Test Recovery Student' } }
    });
    await prisma.course.deleteMany({
      where: { name: { contains: 'Test Recovery Course' } }
    });

    // Create test data
    testCourse = await prisma.course.create({
      data: {
        name: 'Test Recovery Course',
        description: 'Course for testing recovery system',
        numberOfLessons: 10,
        allowsMakeup: true,
        isActive: true
      }
    });

    testStudent = await prisma.student.create({
      data: {
        name: 'Test Recovery Student',
        email: '<EMAIL>',
        phone: '+5511999999999',
        status: 'active'
      }
    });

    // Create original class where student missed lesson
    testClass = await prisma.class.create({
      data: {
        courseId: testCourse.id,
        name: 'Test Recovery Class A',
        description: 'Original class for recovery testing',
        startDate: new Date(),
        endDate: addDays(new Date(), 90),
        classTime: '19:00',
        isActive: true
      }
    });

    // Create makeup class (different class, same course)
    makeupClass = await prisma.class.create({
      data: {
        courseId: testCourse.id,
        name: 'Test Recovery Class B',
        description: 'Makeup class for recovery testing',
        startDate: new Date(),
        endDate: addDays(new Date(), 90),
        classTime: '14:00',
        isActive: true
      }
    });

    // Create lesson that was missed
    testLesson = await prisma.lesson.create({
      data: {
        classId: testClass.id,
        title: 'Introduction to Testing',
        lessonNumber: 1,
        scheduledDate: addDays(new Date(), -1), // Yesterday
        duration: 180,
        isCompleted: true
      }
    });

    // Create makeup lesson (same lesson number, different class)
    makeupLesson = await prisma.lesson.create({
      data: {
        classId: makeupClass.id,
        title: 'Introduction to Testing',
        lessonNumber: 1,
        scheduledDate: addDays(new Date(), 7), // Next week
        duration: 180,
        isCompleted: false
      }
    });

    // Create enrollment
    await prisma.enrollment.create({
      data: {
        studentId: testStudent.id,
        courseId: testCourse.id,
        classId: testClass.id,
        status: 'active'
      }
    });

    // Create absence record
    attendanceRecord = await prisma.attendance.create({
      data: {
        studentId: testStudent.id,
        lessonId: testLesson.id,
        status: 'absent'
      }
    });

    // Generate recovery OTP
    recoveryOtp = await recoveryService.generateRecoveryOtp(
      testStudent.id,
      attendanceRecord.id
    );
  });

  test.afterEach(async () => {
    // Clean up test data
    await prisma.recoveryBooking.deleteMany({
      where: { student: { name: { contains: 'Test Recovery Student' } } }
    });
    await prisma.recoveryOtp.deleteMany({
      where: { student: { name: { contains: 'Test Recovery Student' } } }
    });
    await prisma.recoveryReminder.deleteMany({
      where: { student: { name: { contains: 'Test Recovery Student' } } }
    });
    await prisma.attendance.deleteMany({
      where: { student: { name: { contains: 'Test Recovery Student' } } }
    });
    await prisma.enrollment.deleteMany({
      where: { student: { name: { contains: 'Test Recovery Student' } } }
    });
    await prisma.lesson.deleteMany({
      where: { class: { name: { contains: 'Test Recovery Class' } } }
    });
    await prisma.class.deleteMany({
      where: { name: { contains: 'Test Recovery Class' } }
    });
    await prisma.student.deleteMany({
      where: { name: { contains: 'Test Recovery Student' } }
    });
    await prisma.course.deleteMany({
      where: { name: { contains: 'Test Recovery Course' } }
    });
  });

  test('should display recovery page with valid OTP token', async ({ page }) => {
    // Navigate to recovery page with valid token
    await page.goto(`/recovery?token=${recoveryOtp.token}`);

    // Wait for page to load
    await expect(page.locator('h1')).toContainText('Reposição de Aula');

    // Check student information is displayed
    await expect(page.locator('text=' + testStudent.name)).toBeVisible();
    await expect(page.locator('text=' + testCourse.name)).toBeVisible();
    await expect(page.locator('text=' + testLesson.title)).toBeVisible();

    // Check available lessons are displayed
    await expect(page.locator('text=Aulas Disponíveis para Reposição')).toBeVisible();
    await expect(page.locator('text=' + makeupLesson.title)).toBeVisible();
    await expect(page.locator('text=Aula 1')).toBeVisible(); // Lesson number badge
  });

  test('should show error for invalid OTP token', async ({ page }) => {
    await page.goto('/recovery?token=invalid-token');
    
    await expect(page.locator('text=Erro de Acesso')).toBeVisible();
    await expect(page.locator('text=Invalid or expired OTP token')).toBeVisible();
  });

  test('should show error for missing OTP token', async ({ page }) => {
    await page.goto('/recovery');
    
    await expect(page.locator('text=Erro de Acesso')).toBeVisible();
    await expect(page.locator('text=Token de acesso não encontrado na URL')).toBeVisible();
  });

  test('should allow student to book makeup lesson', async ({ page }) => {
    await page.goto(`/recovery?token=${recoveryOtp.token}`);

    // Wait for available lessons to load
    await expect(page.locator('text=' + makeupLesson.title)).toBeVisible();

    // Select the makeup lesson
    await page.click(`button:has-text("${makeupLesson.title}")`);
    await expect(page.locator('text=Selecionada')).toBeVisible();

    // Book the lesson
    await page.click('button:has-text("Agendar Reposição")');

    // Wait for success message
    await expect(page.locator('text=Sucesso!')).toBeVisible();
    await expect(page.locator('text=Reposição agendada com sucesso')).toBeVisible();

    // Verify booking was created in database
    const booking = await prisma.recoveryBooking.findFirst({
      where: {
        studentId: testStudent.id,
        originalLessonId: testLesson.id,
        makeupLessonId: makeupLesson.id
      }
    });
    expect(booking).toBeTruthy();
    expect(booking?.status).toBe('booked');

    // Verify attendance record was created for makeup lesson
    const makeupAttendance = await prisma.attendance.findFirst({
      where: {
        studentId: testStudent.id,
        lessonId: makeupLesson.id,
        originalLessonId: testLesson.id
      }
    });
    expect(makeupAttendance).toBeTruthy();
    expect(makeupAttendance?.status).toBe('present');
  });

  test('should prevent double booking for same absence', async ({ page }) => {
    // First booking
    await page.goto(`/recovery?token=${recoveryOtp.token}`);
    await page.click(`button:has-text("${makeupLesson.title}")`);
    await page.click('button:has-text("Agendar Reposição")');
    await expect(page.locator('text=Sucesso!')).toBeVisible();

    // Try to book again with a new OTP (should fail)
    const newOtp = await recoveryService.generateRecoveryOtp(
      testStudent.id,
      attendanceRecord.id
    );

    await page.goto(`/recovery?token=${newOtp.token}`);
    await page.click(`button:has-text("${makeupLesson.title}")`);
    await page.click('button:has-text("Agendar Reposição")');

    // Should show error
    await expect(page.locator('text=Makeup lesson already booked for this absence')).toBeVisible();
  });

  test('should filter lessons by same lesson number', async ({ page }) => {
    // Create another makeup lesson with different lesson number
    const differentLessonNumber = await prisma.lesson.create({
      data: {
        classId: makeupClass.id,
        title: 'Advanced Testing Concepts',
        lessonNumber: 2, // Different lesson number
        scheduledDate: addDays(new Date(), 14),
        duration: 180,
        isCompleted: false
      }
    });

    await page.goto(`/recovery?token=${recoveryOtp.token}`);

    // Should only show lesson with same lesson number (1)
    await expect(page.locator('text=' + makeupLesson.title)).toBeVisible();
    await expect(page.locator('text=Aula 1')).toBeVisible();
    
    // Should NOT show lesson with different lesson number
    await expect(page.locator('text=' + differentLessonNumber.title)).not.toBeVisible();
    await expect(page.locator('text=Aula 2')).not.toBeVisible();

    // Clean up
    await prisma.lesson.delete({ where: { id: differentLessonNumber.id } });
  });

  test('should show message when no makeup lessons available', async ({ page }) => {
    // Delete the makeup lesson to simulate no available lessons
    await prisma.lesson.delete({ where: { id: makeupLesson.id } });

    await page.goto(`/recovery?token=${recoveryOtp.token}`);

    await expect(page.locator('text=Não há aulas disponíveis para reposição no momento')).toBeVisible();
    await expect(page.locator('button:has-text("Agendar Reposição")')).toBeDisabled();
  });

  test('should require lesson selection before booking', async ({ page }) => {
    await page.goto(`/recovery?token=${recoveryOtp.token}`);

    // Try to book without selecting a lesson
    await expect(page.locator('button:has-text("Agendar Reposição")')).toBeDisabled();

    // Select a lesson
    await page.click(`button:has-text("${makeupLesson.title}")`);
    
    // Button should now be enabled
    await expect(page.locator('button:has-text("Agendar Reposição")')).toBeEnabled();
  });

  test('should show loading states during booking process', async ({ page }) => {
    await page.goto(`/recovery?token=${recoveryOtp.token}`);

    // Select lesson
    await page.click(`button:has-text("${makeupLesson.title}")`);

    // Click book and check for loading state
    await page.click('button:has-text("Agendar Reposição")');
    await expect(page.locator('text=Agendando...')).toBeVisible();
  });

  test('should handle course that does not allow makeup', async ({ page }) => {
    // Update course to not allow makeup
    await prisma.course.update({
      where: { id: testCourse.id },
      data: { allowsMakeup: false }
    });

    // Try to generate OTP (should fail)
    await expect(async () => {
      await recoveryService.generateRecoveryOtp(testStudent.id, attendanceRecord.id);
    }).rejects.toThrow('This course does not allow makeup classes');
  });

  test('should validate makeup lesson is from same course', async ({ page }) => {
    // Create lesson from different course
    const differentCourse = await prisma.course.create({
      data: {
        name: 'Different Course',
        allowsMakeup: true,
        isActive: true
      }
    });

    const differentClass = await prisma.class.create({
      data: {
        courseId: differentCourse.id,
        name: 'Different Class',
        startDate: new Date(),
        endDate: addDays(new Date(), 90),
        isActive: true
      }
    });

    const differentLesson = await prisma.lesson.create({
      data: {
        classId: differentClass.id,
        title: 'Different Course Lesson',
        lessonNumber: 1,
        scheduledDate: addDays(new Date(), 7),
        isCompleted: false
      }
    });

    // Try to book lesson from different course via API
    const response = await fetch(`http://localhost:3000/api/recovery/book`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        token: recoveryOtp.token,
        originalLessonId: testLesson.id,
        makeupLessonId: differentLesson.id
      })
    });

    expect(response.ok).toBe(false);
    const data = await response.json();
    expect(data.error).toContain('same course');

    // Clean up
    await prisma.lesson.delete({ where: { id: differentLesson.id } });
    await prisma.class.delete({ where: { id: differentClass.id } });
    await prisma.course.delete({ where: { id: differentCourse.id } });
  });

  test('should mark OTP as used after successful booking', async ({ page }) => {
    await page.goto(`/recovery?token=${recoveryOtp.token}`);
    await page.click(`button:has-text("${makeupLesson.title}")`);
    await page.click('button:has-text("Agendar Reposição")');
    await expect(page.locator('text=Sucesso!')).toBeVisible();

    // Check that OTP is marked as used
    const usedOtp = await prisma.recoveryOtp.findUnique({
      where: { id: recoveryOtp.id }
    });
    expect(usedOtp?.usedAt).toBeTruthy();

    // Try to use the same token again (should fail)
    await page.goto(`/recovery?token=${recoveryOtp.token}`);
    await expect(page.locator('text=Invalid or expired OTP token')).toBeVisible();
  });
});

test.describe('Recovery Service API Tests', () => {
  let testStudent: any;
  let testCourse: any;
  let testClass: any;
  let testLesson: any;
  let attendanceRecord: any;

  test.beforeEach(async () => {
    // Clean up and create test data
    await prisma.student.deleteMany({
      where: { name: { contains: 'API Test Student' } }
    });
    await prisma.course.deleteMany({
      where: { name: { contains: 'API Test Course' } }
    });

    testCourse = await prisma.course.create({
      data: {
        name: 'API Test Course',
        allowsMakeup: true,
        isActive: true
      }
    });

    testStudent = await prisma.student.create({
      data: {
        name: 'API Test Student',
        email: '<EMAIL>',
        phone: '+5511888888888',
        status: 'active'
      }
    });

    testClass = await prisma.class.create({
      data: {
        courseId: testCourse.id,
        name: 'API Test Class',
        startDate: new Date(),
        endDate: addDays(new Date(), 90),
        isActive: true
      }
    });

    testLesson = await prisma.lesson.create({
      data: {
        classId: testClass.id,
        title: 'API Test Lesson',
        lessonNumber: 1,
        scheduledDate: addDays(new Date(), -1),
        isCompleted: true
      }
    });

    attendanceRecord = await prisma.attendance.create({
      data: {
        studentId: testStudent.id,
        lessonId: testLesson.id,
        status: 'absent'
      }
    });
  });

  test.afterEach(async () => {
    // Clean up
    await prisma.recoveryBooking.deleteMany({
      where: { student: { name: { contains: 'API Test Student' } } }
    });
    await prisma.recoveryOtp.deleteMany({
      where: { student: { name: { contains: 'API Test Student' } } }
    });
    await prisma.attendance.deleteMany({
      where: { student: { name: { contains: 'API Test Student' } } }
    });
    await prisma.lesson.deleteMany({
      where: { class: { name: { contains: 'API Test Class' } } }
    });
    await prisma.class.deleteMany({
      where: { name: { contains: 'API Test Class' } }
    });
    await prisma.student.deleteMany({
      where: { name: { contains: 'API Test Student' } }
    });
    await prisma.course.deleteMany({
      where: { name: { contains: 'API Test Course' } }
    });
  });

  test('should generate valid recovery OTP', async () => {
    const otp = await recoveryService.generateRecoveryOtp(
      testStudent.id,
      attendanceRecord.id
    );

    expect(otp).toBeTruthy();
    expect(otp.token).toHaveLength(64); // 32 bytes = 64 hex chars
    expect(otp.studentId).toBe(testStudent.id);
    expect(otp.attendanceId).toBe(attendanceRecord.id);
    expect(otp.expiresAt).toBeInstanceOf(Date);
    expect(otp.expiresAt.getTime()).toBeGreaterThan(Date.now());
  });

  test('should validate recovery OTP correctly', async () => {
    const otp = await recoveryService.generateRecoveryOtp(
      testStudent.id,
      attendanceRecord.id
    );

    const validatedData = await recoveryService.validateRecoveryOtp(otp.token);

    expect(validatedData.student.id).toBe(testStudent.id);
    expect(validatedData.attendance.id).toBe(attendanceRecord.id);
    expect(validatedData.attendance.lesson.class.course.allowsMakeup).toBe(true);
  });

  test('should reject invalid OTP token', async () => {
    await expect(
      recoveryService.validateRecoveryOtp('invalid-token')
    ).rejects.toThrow('Invalid or expired OTP token');
  });

  test('should filter available lessons by lesson number', async () => {
    // Create makeup lessons with different lesson numbers
    const makeupClass = await prisma.class.create({
      data: {
        courseId: testCourse.id,
        name: 'Makeup Class',
        startDate: new Date(),
        endDate: addDays(new Date(), 90),
        isActive: true
      }
    });

    const sameNumberLesson = await prisma.lesson.create({
      data: {
        classId: makeupClass.id,
        title: 'Same Number Lesson',
        lessonNumber: 1, // Same as missed lesson
        scheduledDate: addDays(new Date(), 7),
        isCompleted: false
      }
    });

    const differentNumberLesson = await prisma.lesson.create({
      data: {
        classId: makeupClass.id,
        title: 'Different Number Lesson',
        lessonNumber: 2, // Different from missed lesson
        scheduledDate: addDays(new Date(), 14),
        isCompleted: false
      }
    });

    const availableLessons = await recoveryService.getAvailableMakeupLessons(
      testCourse.id,
      testLesson.scheduledDate,
      1 // Missed lesson number
    );

    expect(availableLessons).toHaveLength(1);
    expect(availableLessons[0].id).toBe(sameNumberLesson.id);
    expect(availableLessons[0].lessonNumber).toBe(1);

    // Clean up
    await prisma.lesson.deleteMany({
      where: { classId: makeupClass.id }
    });
    await prisma.class.delete({ where: { id: makeupClass.id } });
  });
});