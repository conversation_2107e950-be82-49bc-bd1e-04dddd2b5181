import { test, expect } from '@playwright/test';

test.describe('Student Enrollments Display', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page
    await page.goto('/login');
    
    // Fill in login credentials (assuming test credentials exist)
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.click('button[type="submit"]');
    
    // Wait for redirect to admin dashboard
    await page.waitForURL('/admin');
    
    // Navigate to students page
    await page.goto('/admin/students');
  });

  test('should display enrollments when editing a student', async ({ page }) => {
    // Click on the first edit button (assuming at least one student exists)
    const editButton = page.locator('button:has(svg[data-lucide="edit"])').first();
    
    if (await editButton.count() > 0) {
      await editButton.click();
      
      // Wait for the edit form to load
      await page.waitForSelector('form', { timeout: 10000 });
      
      // Verify that the StudentEnrollmentsView component is present
      const enrollmentsSection = page.locator('h3:has-text("Matrículas de"), h4:has-text("Matrículas de")');
      await expect(enrollmentsSection).toBeVisible();
      
      // Check for enrollments statistics cards
      const statsCards = page.locator('div:has-text("Total"), div:has-text("Ativas"), div:has-text("Inativas")');
      await expect(statsCards.first()).toBeVisible();
      
      // Check for enrollments table or empty state
      const enrollmentsTable = page.locator('table, div:has-text("Nenhuma matrícula encontrada")');
      await expect(enrollmentsTable).toBeVisible();
    } else {
      console.log('No students found to edit, skipping enrollments display test');
    }
  });

  test('should display enrollments in student detail view', async ({ page }) => {
    // Click on a student name to go to detail view
    const studentNameLink = page.locator('a, div:has-text("Ver Detalhes"), button:has-text("Detalhes")').first();
    
    if (await studentNameLink.count() > 0) {
      await studentNameLink.click();
      
      // Wait for the detail page to load
      await page.waitForSelector('h1', { timeout: 10000 });
      
      // Look for the enrollments tab
      const enrollmentsTab = page.locator('button:has-text("Matrículas")');
      await expect(enrollmentsTab).toBeVisible();
      
      // Click on the enrollments tab if it's not already active
      await enrollmentsTab.click();
      
      // Verify enrollments content is displayed
      const enrollmentsContent = page.locator('div:has-text("Matrículas de"), table, div:has-text("Nenhuma matrícula encontrada")');
      await expect(enrollmentsContent.first()).toBeVisible();
    } else {
      console.log('No student detail link found, skipping detail view test');
    }
  });

  test('should show enrollment statistics correctly in edit mode', async ({ page }) => {
    // Go to edit mode for the first student
    const editButton = page.locator('button:has(svg[data-lucide="edit"])').first();
    
    if (await editButton.count() > 0) {
      await editButton.click();
      
      // Wait for the edit form to load
      await page.waitForSelector('form', { timeout: 10000 });
      
      // Check for statistics cards
      const totalCard = page.locator('div:has-text("Total") >> div:has-text("Matrículas")').first();
      const activeCard = page.locator('div:has-text("Ativas") >> div:has-text("Matrículas")').first();
      const inactiveCard = page.locator('div:has-text("Inativas") >> div:has-text("Matrículas")').first();
      const absencesCard = page.locator('div:has-text("Faltas Totais")').first();
      
      // These should be visible if there are enrollments, or show 0 if no enrollments
      await expect(totalCard.or(page.locator('text="0"'))).toBeVisible();
      await expect(activeCard.or(page.locator('text="0"'))).toBeVisible();
      await expect(inactiveCard.or(page.locator('text="0"'))).toBeVisible();
      await expect(absencesCard.or(page.locator('text="0"'))).toBeVisible();
    } else {
      console.log('No students found to edit, skipping statistics test');
    }
  });

  test('should show enrollment actions for inactive enrollments in edit mode', async ({ page }) => {
    // Go to edit mode for the first student
    const editButton = page.locator('button:has(svg[data-lucide="edit"])').first();
    
    if (await editButton.count() > 0) {
      await editButton.click();
      
      // Wait for the edit form to load
      await page.waitForSelector('form', { timeout: 10000 });
      
      // Look for enrollments table
      const enrollmentsTable = page.locator('table');
      
      if (await enrollmentsTable.count() > 0) {
        // Check if there are any inactive enrollments with action buttons
        const actionButtons = page.locator('button:has(svg[data-lucide="more-horizontal"])');
        
        if (await actionButtons.count() > 0) {
          // There are action buttons available
          await expect(actionButtons.first()).toBeVisible();
        } else {
          // No action buttons, which is fine if all enrollments are active
          console.log('No enrollment actions found, which is expected if all enrollments are active');
        }
      } else {
        // No enrollments table, should show empty state
        const emptyState = page.locator('div:has-text("Nenhuma matrícula encontrada")');
        await expect(emptyState).toBeVisible();
      }
    } else {
      console.log('No students found to edit, skipping actions test');
    }
  });

  test('should maintain enrollments data when switching between form sections', async ({ page }) => {
    // Go to edit mode for the first student
    const editButton = page.locator('button:has(svg[data-lucide="edit"])').first();
    
    if (await editButton.count() > 0) {
      await editButton.click();
      
      // Wait for the edit form to load
      await page.waitForSelector('form', { timeout: 10000 });
      
      // Get initial enrollments count
      const enrollmentsSection = page.locator('div:has-text("Matrículas de")').first();
      await expect(enrollmentsSection).toBeVisible();
      
      // Scroll to form fields and make a change
      const nameInput = page.locator('input[placeholder*="Nome completo"]').first();
      await nameInput.focus();
      
      // Scroll back to enrollments section
      await enrollmentsSection.scrollIntoViewIfNeeded();
      
      // Verify enrollments are still displayed correctly
      await expect(enrollmentsSection).toBeVisible();
      
      // Check that statistics are still showing
      const statsCards = page.locator('div:has-text("Total"), div:has-text("Ativas")');
      await expect(statsCards.first()).toBeVisible();
    } else {
      console.log('No students found to edit, skipping data persistence test');
    }
  });
});