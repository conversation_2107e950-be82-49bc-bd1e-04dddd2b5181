import { test, expect } from '@playwright/test';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

test.describe('Recovery System E2E Tests', () => {
  let testStudent: any;
  let testCourse: any;
  let testClass: any;
  let testLesson: any;
  let testMakeupLesson: any;
  let testAttendance: any;

  test.beforeAll(async () => {
    // Clean up any existing test data
    await prisma.recoveryBooking.deleteMany({
      where: { student: { name: { contains: 'E2E Test' } } }
    });
    await prisma.recoveryOtp.deleteMany({
      where: { student: { name: { contains: 'E2E Test' } } }
    });
    await prisma.recoveryReminder.deleteMany({
      where: { student: { name: { contains: 'E2E Test' } } }
    });
    await prisma.attendance.deleteMany({
      where: { student: { name: { contains: 'E2E Test' } } }
    });
    await prisma.student.deleteMany({
      where: { name: { contains: 'E2E Test' } }
    });

    // Create test student
    testStudent = await prisma.student.create({
      data: {
        name: 'E2E Test Student Recovery',
        email: '<EMAIL>',
        phone: '+5511999999999'
      }
    });

    // Create test course that allows makeup
    testCourse = await prisma.course.create({
      data: {
        name: 'E2E Recovery Test Course',
        description: 'Test course for recovery system',
        allowsMakeup: true
      }
    });

    // Create test class
    testClass = await prisma.class.create({
      data: {
        courseId: testCourse.id,
        name: 'E2E Recovery Test Class',
        startDate: new Date(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
      }
    });

    // Create test lesson (missed)
    testLesson = await prisma.lesson.create({
      data: {
        classId: testClass.id,
        title: 'E2E Test Missed Lesson',
        scheduledDate: new Date(Date.now() - 24 * 60 * 60 * 1000), // Yesterday
        lessonNumber: 1
      }
    });

    // Create makeup lesson (future)
    testMakeupLesson = await prisma.lesson.create({
      data: {
        classId: testClass.id,
        title: 'E2E Test Makeup Lesson',
        scheduledDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        lessonNumber: 2
      }
    });

    // Create enrollment
    await prisma.enrollment.create({
      data: {
        studentId: testStudent.id,
        courseId: testCourse.id,
        classId: testClass.id,
        status: 'active'
      }
    });

    // Create absence attendance record
    testAttendance = await prisma.attendance.create({
      data: {
        studentId: testStudent.id,
        lessonId: testLesson.id,
        status: 'absent',
        markedAt: new Date()
      }
    });
  });

  test.afterAll(async () => {
    // Clean up test data
    await prisma.recoveryBooking.deleteMany({
      where: { student: { name: { contains: 'E2E Test' } } }
    });
    await prisma.recoveryOtp.deleteMany({
      where: { student: { name: { contains: 'E2E Test' } } }
    });
    await prisma.recoveryReminder.deleteMany({
      where: { student: { name: { contains: 'E2E Test' } } }
    });
    await prisma.attendance.deleteMany({
      where: { student: { name: { contains: 'E2E Test' } } }
    });
    await prisma.enrollment.deleteMany({
      where: { student: { name: { contains: 'E2E Test' } } }
    });
    await prisma.lesson.deleteMany({
      where: { class: { name: { contains: 'E2E Recovery Test' } } }
    });
    await prisma.class.deleteMany({
      where: { name: { contains: 'E2E Recovery Test' } }
    });
    await prisma.course.deleteMany({
      where: { name: { contains: 'E2E Recovery Test' } }
    });
    await prisma.student.deleteMany({
      where: { name: { contains: 'E2E Test' } }
    });

    await prisma.$disconnect();
  });

  test('should create recovery reminder for absence', async () => {
    // Test the recovery service directly
    const { recoveryService } = await import('@/lib/services/recovery.service');

    const reminder = await recoveryService.createRecoveryReminder({
      studentId: testStudent.id,
      attendanceId: testAttendance.id,
      reminderDate: new Date(Date.now() + 24 * 60 * 60 * 1000) // Tomorrow
    });

    expect(reminder).toBeDefined();
    expect(reminder.studentId).toBe(testStudent.id);
    expect(reminder.attendanceId).toBe(testAttendance.id);
    expect(reminder.status).toBe('pending');
  });

  test('should generate OTP for recovery', async () => {
    const { recoveryService } = await import('@/lib/services/recovery.service');

    const otp = await recoveryService.generateRecoveryOtp(
      testStudent.id,
      testAttendance.id
    );

    expect(otp).toBeDefined();
    expect(otp.studentId).toBe(testStudent.id);
    expect(otp.attendanceId).toBe(testAttendance.id);
    expect(otp.token).toBeDefined();
    expect(otp.expiresAt).toBeDefined();
    expect(otp.usedAt).toBeNull();
  });

  test('should validate OTP token via API', async ({ request }) => {
    // First generate an OTP
    const { recoveryService } = await import('@/lib/services/recovery.service');
    const otp = await recoveryService.generateRecoveryOtp(
      testStudent.id,
      testAttendance.id
    );

    // Test the API endpoint
    const response = await request.get(`/api/recovery/validate?token=${otp.token}`);
    expect(response.ok()).toBeTruthy();

    const data = await response.json();
    expect(data.data.studentId).toBe(testStudent.id);
    expect(data.data.attendance.lesson.id).toBe(testLesson.id);
  });

  test('should get available makeup lessons via API', async ({ request }) => {
    // First generate an OTP
    const { recoveryService } = await import('@/lib/services/recovery.service');
    const otp = await recoveryService.generateRecoveryOtp(
      testStudent.id,
      testAttendance.id
    );

    // Test the API endpoint
    const response = await request.get(
      `/api/recovery/available-lessons?courseId=${testCourse.id}&token=${otp.token}`
    );
    expect(response.ok()).toBeTruthy();

    const data = await response.json();
    expect(Array.isArray(data.data)).toBeTruthy();
    expect(data.data.length).toBeGreaterThan(0);
    
    const makeupLesson = data.data.find((lesson: any) => lesson.id === testMakeupLesson.id);
    expect(makeupLesson).toBeDefined();
  });

  test('should book makeup lesson via API', async ({ request }) => {
    // First generate an OTP
    const { recoveryService } = await import('@/lib/services/recovery.service');
    const otp = await recoveryService.generateRecoveryOtp(
      testStudent.id,
      testAttendance.id
    );

    // Book the makeup lesson
    const response = await request.post('/api/recovery/book', {
      data: {
        token: otp.token,
        originalLessonId: testLesson.id,
        makeupLessonId: testMakeupLesson.id
      }
    });

    expect(response.ok()).toBeTruthy();

    const data = await response.json();
    expect(data.data.studentId).toBe(testStudent.id);
    expect(data.data.originalLessonId).toBe(testLesson.id);
    expect(data.data.makeupLessonId).toBe(testMakeupLesson.id);
    expect(data.data.status).toBe('booked');

    // Verify OTP was marked as used
    const updatedOtp = await prisma.recoveryOtp.findUnique({
      where: { id: otp.id }
    });
    expect(updatedOtp?.usedAt).not.toBeNull();

    // Verify attendance record was created for makeup lesson
    const makeupAttendance = await prisma.attendance.findFirst({
      where: {
        studentId: testStudent.id,
        lessonId: testMakeupLesson.id,
        originalLessonId: testLesson.id
      }
    });
    expect(makeupAttendance).toBeDefined();
    expect(makeupAttendance?.status).toBe('present');
  });

  test('should prevent duplicate bookings', async ({ request }) => {
    // First generate an OTP
    const { recoveryService } = await import('@/lib/services/recovery.service');
    const otp = await recoveryService.generateRecoveryOtp(
      testStudent.id,
      testAttendance.id
    );

    // Try to book again (should fail)
    const response = await request.post('/api/recovery/book', {
      data: {
        token: otp.token,
        originalLessonId: testLesson.id,
        makeupLessonId: testMakeupLesson.id
      }
    });

    expect(response.ok()).toBeFalsy();
    expect(response.status()).toBe(400);

    const data = await response.json();
    expect(data.error).toContain('already booked');
  });

  test('should reject invalid/expired tokens', async ({ request }) => {
    // Test with invalid token
    const response = await request.get('/api/recovery/validate?token=invalid-token');
    expect(response.ok()).toBeFalsy();
    expect(response.status()).toBe(401);

    const data = await response.json();
    expect(data.error).toContain('Invalid or expired');
  });

  test('should show recovery page with valid token', async ({ page }) => {
    // First generate an OTP
    const { recoveryService } = await import('@/lib/services/recovery.service');
    const otp = await recoveryService.generateRecoveryOtp(
      testStudent.id,
      testAttendance.id
    );

    // Visit the recovery page
    await page.goto(`/recovery?token=${otp.token}`);

    // Wait for the page to load
    await page.waitForSelector('h1');

    // Check if the page shows the correct content
    await expect(page.locator('h1')).toContainText('Reposição de Aula');
    await expect(page.locator('text=' + testStudent.name)).toBeVisible();
    await expect(page.locator('text=' + testCourse.name)).toBeVisible();
    await expect(page.locator('text=' + testLesson.title)).toBeVisible();

    // Check if makeup lessons are shown
    await expect(page.locator('text=' + testMakeupLesson.title)).toBeVisible();
  });

  test('should complete full recovery flow via UI', async ({ page }) => {
    // Create a fresh OTP for this test
    const { recoveryService } = await import('@/lib/services/recovery.service');
    const otp = await recoveryService.generateRecoveryOtp(
      testStudent.id,
      testAttendance.id
    );

    // Visit the recovery page
    await page.goto(`/recovery?token=${otp.token}`);

    // Wait for the page to load
    await page.waitForSelector('h1');

    // Select the makeup lesson
    await page.click(`[data-testid="lesson-${testMakeupLesson.id}"], text="${testMakeupLesson.title}"`);

    // Click the book button
    await page.click('text=Agendar Reposição');

    // Wait for success message
    await expect(page.locator('text=agendada com sucesso')).toBeVisible({ timeout: 10000 });

    // Verify the booking was created in the database
    const booking = await prisma.recoveryBooking.findFirst({
      where: {
        studentId: testStudent.id,
        originalLessonId: testLesson.id,
        makeupLessonId: testMakeupLesson.id
      }
    });
    expect(booking).toBeDefined();
  });

  test('should show error for invalid token on recovery page', async ({ page }) => {
    // Visit recovery page with invalid token
    await page.goto('/recovery?token=invalid-token');

    // Wait for error message
    await expect(page.locator('text=Erro de Acesso')).toBeVisible();
    await expect(page.locator('text=Invalid or expired')).toBeVisible();
  });

  test('should process recovery reminders', async () => {
    // Create a pending reminder
    const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000);
    const reminder = await prisma.recoveryReminder.create({
      data: {
        studentId: testStudent.id,
        attendanceId: testAttendance.id,
        reminderDate: new Date(), // Set to now so it gets processed
        status: 'pending'
      }
    });

    const { recoverySchedulerService } = await import('@/lib/services/recovery-scheduler.service');

    // Process the reminder
    const results = await recoverySchedulerService.processPendingReminders();

    expect(results.length).toBeGreaterThan(0);
    
    const processedReminder = results.find(r => r.reminderId === reminder.id);
    expect(processedReminder).toBeDefined();

    // Check if reminder was marked as sent or failed
    const updatedReminder = await prisma.recoveryReminder.findUnique({
      where: { id: reminder.id }
    });
    expect(updatedReminder?.status).not.toBe('pending');
  });
});