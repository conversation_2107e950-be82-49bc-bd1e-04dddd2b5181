import { test, expect } from '@playwright/test';

test.describe('Dashboard Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page
    await page.goto('/login');
    
    // Fill in login credentials (assuming test credentials exist)
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.click('button[type="submit"]');
    
    // Wait for redirect to admin dashboard
    await page.waitForURL('/admin');
  });

  test('should display dashboard correctly', async ({ page }) => {
    // Check if dashboard loads without errors
    await expect(page).toHaveURL('/admin');
    
    // Check for main dashboard elements
    await expect(page.locator('h1, h2, div[class*="dashboard"]')).toBeVisible();
  });

  test('should display statistics cards', async ({ page }) => {
    // Wait for dashboard data to load
    await page.waitForTimeout(2000);
    
    // Check for statistics cards - these should be visible even if data is 0
    const statisticsCards = page.locator('[data-testid="stats-card"], div[class*="stat"], div[class*="card"]');
    
    // Should have at least some statistical display elements
    await expect(statisticsCards.first()).toBeVisible();
  });

  test('should load dashboard data without errors', async ({ page }) => {
    // Monitor network requests for dashboard API
    const dashboardRequests: any[] = [];
    
    page.on('response', response => {
      if (response.url().includes('/api/dashboard')) {
        dashboardRequests.push({
          url: response.url(),
          status: response.status(),
          statusText: response.statusText()
        });
      }
    });
    
    // Refresh to trigger dashboard API call
    await page.reload();
    
    // Wait for potential dashboard API calls
    await page.waitForTimeout(3000);
    
    // Check if dashboard API was called and succeeded
    if (dashboardRequests.length > 0) {
      const lastRequest = dashboardRequests[dashboardRequests.length - 1];
      expect(lastRequest.status).toBeLessThan(400);
      console.log(`Dashboard API response: ${lastRequest.status} ${lastRequest.statusText}`);
    }
    
    // Verify no network errors prevented dashboard from loading
    const errorMessages = page.locator('div:has-text("erro"), div:has-text("Error"), div:has-text("500")');
    await expect(errorMessages).toHaveCount(0);
  });

  test('should display student statistics', async ({ page }) => {
    // Wait for data to load
    await page.waitForTimeout(2000);
    
    // Look for student-related statistics
    const studentStats = page.locator('text=/total.*aluno/i, text=/estudante/i, text=/student/i');
    
    // Should display some student-related information
    if (await studentStats.count() > 0) {
      await expect(studentStats.first()).toBeVisible();
      console.log('Student statistics found on dashboard');
    } else {
      console.log('No student statistics displayed, which may be expected for empty system');
    }
  });

  test('should display class statistics', async ({ page }) => {
    // Wait for data to load
    await page.waitForTimeout(2000);
    
    // Look for class/turma-related statistics
    const classStats = page.locator('text=/turma/i, text=/classe/i, text=/aula/i');
    
    // Should display some class-related information
    if (await classStats.count() > 0) {
      await expect(classStats.first()).toBeVisible();
      console.log('Class statistics found on dashboard');
    } else {
      console.log('No class statistics displayed, which may be expected for empty system');
    }
  });

  test('should display attendance information', async ({ page }) => {
    // Wait for data to load
    await page.waitForTimeout(2000);
    
    // Look for attendance-related information
    const attendanceInfo = page.locator('text=/presença/i, text=/attendance/i, text=/frequência/i');
    
    // Should display some attendance-related information
    if (await attendanceInfo.count() > 0) {
      await expect(attendanceInfo.first()).toBeVisible();
      console.log('Attendance information found on dashboard');
    } else {
      console.log('No attendance information displayed, which may be expected for empty system');
    }
  });

  test('should show upcoming lessons if any exist', async ({ page }) => {
    // Wait for data to load
    await page.waitForTimeout(2000);
    
    // Look for upcoming lessons section
    const upcomingLessons = page.locator('text=/próxima/i, text=/upcoming/i, text=/hoje/i');
    
    if (await upcomingLessons.count() > 0) {
      await expect(upcomingLessons.first()).toBeVisible();
      console.log('Upcoming lessons section found');
    } else {
      console.log('No upcoming lessons section found, which may be expected');
    }
  });

  test('should display recent activity if any exists', async ({ page }) => {
    // Wait for data to load
    await page.waitForTimeout(2000);
    
    // Look for recent activity section
    const recentActivity = page.locator('text=/atividade/i, text=/activity/i, text=/recente/i');
    
    if (await recentActivity.count() > 0) {
      await expect(recentActivity.first()).toBeVisible();
      console.log('Recent activity section found');
    } else {
      console.log('No recent activity section found, which may be expected');
    }
  });

  test('should handle loading states gracefully', async ({ page }) => {
    // Monitor for loading indicators
    const loadingIndicators = page.locator('[data-testid="loading"], div:has-text("Carregando"), div[class*="loading"], div[class*="spinner"]');
    
    // Navigate to dashboard
    await page.goto('/admin');
    
    // Check if loading indicators appear and then disappear
    if (await loadingIndicators.count() > 0) {
      console.log('Loading indicators found');
      
      // Wait for loading to complete (max 10 seconds)
      await page.waitForFunction(
        () => {
          const indicators = document.querySelectorAll('[data-testid="loading"], div:contains("Carregando")');
          return indicators.length === 0;
        },
        { timeout: 10000 }
      ).catch(() => {
        console.log('Loading indicators did not disappear within timeout, which may be expected');
      });
    }
    
    // Verify dashboard content is eventually visible
    await expect(page.locator('body')).toBeVisible();
  });

  test('should navigate to other admin sections from dashboard', async ({ page }) => {
    // Test navigation to students
    const studentsLink = page.locator('a[href*="/admin/students"], button:has-text("Alunos")');
    if (await studentsLink.count() > 0) {
      await studentsLink.first().click();
      await page.waitForURL('**/admin/students');
      console.log('Successfully navigated to students from dashboard');
      await page.goBack();
    }
    
    // Test navigation to courses
    const coursesLink = page.locator('a[href*="/admin/courses"], button:has-text("Cursos")');
    if (await coursesLink.count() > 0) {
      await coursesLink.first().click();
      await page.waitForURL('**/admin/courses');
      console.log('Successfully navigated to courses from dashboard');
      await page.goBack();
    }
    
    // Test navigation to teachers
    const teachersLink = page.locator('a[href*="/admin/teachers"], button:has-text("Professores")');
    if (await teachersLink.count() > 0) {
      await teachersLink.first().click();
      await page.waitForURL('**/admin/teachers');
      console.log('Successfully navigated to teachers from dashboard');
      await page.goBack();
    }
  });

  test('should be responsive on different screen sizes', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.reload();
    await page.waitForTimeout(1000);
    
    // Dashboard should still be functional on mobile
    await expect(page.locator('body')).toBeVisible();
    
    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.reload();
    await page.waitForTimeout(1000);
    
    // Dashboard should still be functional on tablet
    await expect(page.locator('body')).toBeVisible();
    
    // Reset to desktop
    await page.setViewportSize({ width: 1280, height: 720 });
  });

  test('should handle empty data states gracefully', async ({ page }) => {
    // Wait for dashboard to load
    await page.waitForTimeout(3000);
    
    // Check for empty state messages or zero values
    const zeroValues = page.locator('text=/^0$/, text=/Nenhum/, text=/Vazio/, text=/Empty/');
    
    if (await zeroValues.count() > 0) {
      console.log('Found empty state indicators, which is expected for a clean system');
    }
    
    // Verify the page doesn't show error messages for empty data
    const errorMessages = page.locator('text=/Erro/, text=/Error/, text=/Falha/, text=/Failed/');
    await expect(errorMessages).toHaveCount(0);
  });
});