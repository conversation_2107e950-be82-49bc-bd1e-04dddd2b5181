version: '3.8'

services:
  vox-student-test:
    image: hcktplanet/vox-student:latest
    container_name: vox-student-test
    ports:
      - "3002:3000"
    environment:
      - NODE_ENV=production
    env_file:
      - .env.production
    volumes:
      - ./data-test:/app/data
      - ./whatsapp-test:/app/whatsapp-session
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s