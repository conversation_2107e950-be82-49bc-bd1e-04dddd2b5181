GERAL
✅ Exibir o menu em todas as paginas

✅ Salvar os dados automaticamente nos formularios, botao de salvar é coisa do passado. na digitação (com debounce) mas precisa deixar isso salvo automatico. Exibir indicador visual que alterações foram salvas. (fazer isso para todas as paginas)  

✅ remover os botoes de salvar e cancelar do rodape das paginas CRUD que possuem autosave. se nao tem auto-save, implementar!


ADMIN
✅ Titulo das aulas: Curso possui cadastro de titulo para cada aula. Os alunos do curso podem ver apenas os titulos das aulas passadas E o titulo da proxima aula (6 dias no futuro); Exemplo: Hoje é dia 16-setembro e a proxima aula é 23 de setembro, nao pode ver titulo. Se for 17 de setembro, pode ver titulo da proxima aula na lista de aulas do aluno. Titulo default da aula, número da aula, ex: "4"

✅ Ver/editar Turma: Dividir parte de baixo da tela em 2 colunas: Novo painel na esquerda para Exibir lista de alunos; novo painel na direita para exibir Aulas (lessons) incluindo a data e hora, se ela já aconteceu ou nao, e numero de presenças, ausencias e alunos em recuperacao de aula; 

✅ No painel com a lista de aulas, fazer scroll para garantir visibilidade da proxima aula. Exemplo: Curso com 50 aulas, estamos na aula 45. Desejo ver a aula 45 (scroll automatico ao entrar na tela).

✅ Exibir lista de turmas que aluno esta matriculado na tela de editar / visualizar aluno;

✅ cadastro de professores: admin pode cadastrar professores. Nome, email e telefone.

✅ Listar turmas na parte de baixo da pagina de cursos, com estatisticas.

✅ Relatorio de NPS por professor: exibir a media de NPS, e detalhes de cada availacao. Exibir NPS por aula, por turma, por curso e por aluno. Permitir filtro por periodo;



PROFESSOR
✅ Lembrete de recuperação: no dia seguinte a uma ausencia (9h da manha) enviar lembrete para o aluno, com uma listagem das proximas turmas que terao a aula que foi perdida, listando apenas data e horario. O lembrete deve conter um link de autenticação (OTP) valido por 24h para a pagina de marcacao de recuperacao de aula.

✅ Agenda de mentorias: cada professor pode gerenciar sua agenda de mentorias. Ele conecta com seu google calendar, e define quais dias e horarios estara disponivel (como Calendly ou Office Booking), e o tempo de duração da mentoria (60 min, 90 min, 120 min); 

✅ Lembrete de mentoria: no cadastro de agenda de mentoria do professor, exibir um campo para definir lembrete, escolhendo um template de lembrete pelo nome. Previsualizar lembrete. Definir quantas horas de antecedencia da mentoria agendada, o lembrete sera enviado.


ALUNO
✅ Recuperação de aulas: Aluno que perde a aula X, pode recuperar esta mesma aula em outra turma do mesmo curso. Pagina exibe as proximas datas que terao a mesma aula do mesmo curso. Aluno escolhe qual data, e ao confirmar, é adicionado na lista de presença daquela aula.

✅ Mentorias: adicionar numero de mentorias no cadastro do curso. Cada aluno tem direito a X mentorias individuais por curso. Disponibilizar uma pagina para cadastro de mentorias. Ela exibe a agenda (conectar com google calendar). Aluno escolhe o horario de mentoria. Cadastro de evento no google calendar, convidando o aluno (email) no evento. Aluno recebe convite para reuniao automaticamente pelo google calendar.

✅ Visualizar / editar / cancelar mentorias: Aluno pode visualizar suas mentorias agendadas e editar ou cancelar. Alterações sao salvas no google calendar;

✅ Dashboard: Total de alunos: exibir quantos alunos novos no mes, nao a taxa de crescimento. Turmas ativas: exibir taxa de presença media, ao inves de novas turmas. Taxa de Presença: substituir por Mentorias: Quantidade agendadas; no rodape do card, exibir % de agendamentos vezes numero de mentorias pendentes - apenas alunos ativos! Calculo: ((total de alunos ativos * qtd mentorias do seu curso) - mentorias ja realizadas pelos alunos ativos).

✅ Titulo das aulas: Curso possui cadastro de titulo para cada aula. Os alunos do curso podem ver apenas os titulos das aulas passadas E o titulo da proxima aula (6 dias no futuro); Exemplo: Hoje é dia 16-setembro e a proxima aula é 23 de setembro, nao pode ver titulo. Se for 17 de setembro, pode ver titulo da proxima aula na lista de aulas do aluno.

✅ Refazer qualquer aula: alunos podem refazer qualquer aula de um curso que ja fez. Pagina exibe os cursos que o aluno ja fez. Ao escolher um curso, ela pode escolher uma aula e ver a lista de turmas ativas (datas e horarios) e agendar presença. exemplo: Aluno ja fez 2 cursos, ele pode ver os cursos que ja fez e escolher refazer aula 10. Ele escolhe a aula 10, e vê as turmas ativas que tem esta aula no futuro, e agenda presença. NAO PODE: agendar uma aula que ele ainda nao fez: esta na aula 6 e quer refazer aula 10. O sistema exibe apenas aulas que estao concluidas.

✅ NPS da aula: Enviar ao final da aula, uma mensagem via whatsapp perguntando se a aula foi boa, neutro ou ruim. enviar 3 links, um para cada opção. Esses links vao para um formulario que já registra a opção escolhida. Permite alterar a opção, e permite escrever um texto anonimo. 

NPS Feedback System - Complete Implementation
✅ What was implemented:
1. Database Schema
Added NpsToken model for secure 24-hour OTP-style tokens
Added NpsFeedback model for storing student ratings and anonymous comments
Added NpsRating enum (good, neutral, bad)
Updated existing models with NPS relations
2. NPS Token Generation Service (src/lib/services/nps.service.ts)
Secure token generation with 24-hour expiration
Token validation and retrieval
Feedback submission with update capability
Statistics and analytics functions
Automatic cleanup of expired tokens
3. API Endpoints
/api/nps/token - Generate and validate NPS tokens
/api/nps/feedback - Submit and retrieve feedback
/api/nps/process - Manual processing and statistics
/api/nps/schedule - Manual scheduling for testing
4. Feedback Form Page (/feedback?token=xxx)
User-friendly interface showing lesson details
Three rating options (😊 Boa, 😐 Neutra, 😞 Ruim)
Optional anonymous text feedback
Ability to update previous feedback
Token expiration handling
Mobile-responsive design
5. WhatsApp Integration
Extended existing WhatsApp service with NPS messaging
Three direct rating links in WhatsApp message:
😊 BOA - Direct link with pre-selected rating
😐 NEUTRA - Direct link with pre-selected rating
😞 RUIM - Direct link with pre-selected rating
Full feedback form link for detailed comments
Professional message template with lesson details
6. Automated Messaging System (src/lib/services/nps-messaging.service.ts)
Automatic scheduling when lessons are marked as completed
30-minute delay after lesson completion (configurable)
Only sends to students who attended the lesson
Integration with existing message queue system
Retry logic for failed messages
7. Lesson Completion Integration
Modified /api/lessons/[id] endpoint
Automatically triggers NPS scheduling when isCompleted changes from false to true
Non-blocking implementation (doesn't fail lesson update if NPS fails)
8. Cron Jobs & Automation
Every 15 minutes: Process pending NPS messages (*/15 * * * *)
Daily cleanup: Remove old messages and expired tokens
Added to Vercel cron configuration
9. Admin Dashboard (/admin/nps)
Real-time statistics and monitoring
Message status tracking (pending, sent, failed)
Visual charts and analytics
Manual message processing
Recent messages log with error details
10. Message Queue Integration
Added 'nps' message type to existing queue system
Priority-based processing
Retry mechanism with exponential backoff
Comprehensive error handling
🚀 How it works:
Teacher marks lesson as completed → System automatically schedules NPS messages
30 minutes later → WhatsApp messages sent to all students who attended
Students receive WhatsApp with 3 direct rating links + full form link
Students click rating → Redirected to feedback form with pre-selected rating
Students can add comments → Anonymous text feedback (optional)
Feedback stored → Available in admin dashboard for analysis
Links expire in 24 hours → Security and data management
📱 WhatsApp Message Example:
🔧 Key Features:
✅ Automatic triggering after lesson completion
✅ Three direct rating options in WhatsApp
✅ 24-hour secure tokens (OTP-style)
✅ Anonymous feedback capability
✅ Rating updates allowed
✅ Mobile-responsive feedback form
✅ Admin dashboard with analytics
✅ Automated processing via cron jobs
✅ Integration with existing WhatsApp service
✅ Error handling and retry logic
✅ Audit logging for all actions
The system is now fully functional and ready for use! Students will automatically receive NPS feedback requests via WhatsApp after attending completed lessons, making it easy to collect valuable feedback for course improvement.