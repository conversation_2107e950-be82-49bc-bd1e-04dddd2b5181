# VoxStudent v1.0.0 - Documentação

Sistema de gestão de estudantes com reconhecimento facial, integração WhatsApp e portal completo para administração educacional.

## 📚 Índice da Documentação

### 🎯 Para Usuários
- [**Manual do Usuário**](./user-manual/) - Guia completo em português para todos os tipos de usuário
- [**<PERSON>uia de Início Rápido**](./user-manual/quick-start.md) - Primeiros passos no sistema

### 💻 Para Desenvolvedores
- [**Guia de Desenvolvimento**](./development/) - Setup, arquitetura e contribuição
- [**Referência da API**](./development/api-reference.md) - Documentação completa da API
- [**Arquitetura do Sistema**](./development/architecture.md) - Visão técnica detalhada

### 🚀 Deploy e Operações
- [**<PERSON><PERSON><PERSON> de Deploy**](./deployment/) - Instruções de instalação e configuração
- [**Monitoramento**](./deployment/monitoring.md) - Logs, métricas e troubleshooting

### 🔒 Segurança
- [**Segurança e Compliance**](./security/) - LGPD, autenticação e boas práticas

### 🧪 Testes e QA
- [**Guia de Testes**](./testing/) - Testes unitários, E2E e QA

## 🎉 Novidades da Versão 1.0.0

### ✅ Funcionalidades Implementadas
- ✅ **Refazer Aulas**: Alunos podem refazer qualquer aula já concluída
- ✅ **Mentorias**: Sistema completo de agendamento de mentorias
- ✅ **Recuperação de Aulas**: Sistema automático de reposição
- ✅ **Dashboard Atualizado**: Métricas de mentorias e presença
- ✅ **Auto-save**: Salvamento automático em todos os formulários
- ✅ **Títulos de Aulas**: Sistema de títulos personalizados por curso

### 🔄 Em Desenvolvimento
- 🔄 **NPS por Aula**: Sistema de feedback pós-aula
- 🔄 **Relatórios de NPS**: Análise de satisfação por professor

## 🏗️ Arquitetura

- **Frontend**: Next.js 15 (App Router), React 19, TypeScript
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: SQLite (dev), PostgreSQL (prod)
- **Autenticação**: Passwordless (magic links)
- **Integrações**: face-api.js, whatsapp-web.js, Google Calendar

## 📞 Suporte

Para suporte técnico ou dúvidas sobre o sistema:
- 📧 Documentação técnica: [CLAUDE.md](../CLAUDE.md)
- 🐛 Issues: Consulte os logs do sistema
- 📖 Referência: Consulte os manuais específicos por perfil de usuário

---

**VoxStudent** - Sistema de gestão educacional moderno e completo.