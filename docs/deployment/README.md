# Guia de Deploy - VoxStudent

Instruções completas para deploy e configuração do VoxStudent em produção.

## 📋 Índice

- [**Deploy com Docker**](./docker-deployment.md) - Deploy usando containers
- [**Deploy Manual**](./manual-deployment.md) - Deploy tradicional
- [**Configuração do Servidor**](./server-setup.md) - Setup inicial do servidor
- [**Monitoramento**](./monitoring.md) - Logs e métricas
- [**Troubleshooting**](./troubleshooting.md) - Solução de problemas

## 🚀 Visão Geral

O VoxStudent pode ser deployado de duas formas principais:
1. **Docker** (recomendado) - Containerizado e fácil de gerenciar
2. **Manual** - Deploy tradicional no servidor

## 🐳 Deploy Rápido com Docker

### Pré-requisitos
- Docker e Docker Compose instalados
- Acesso SSH ao servidor
- Domínio configurado (opcional)

### 1. Preparar Arquivos
```bash
# Clonar repositório
git clone <repository-url>
cd vox-student-nextjs

# Configurar ambiente
cp .env.example .env.production
# Editar .env.production com suas configurações
```

### 2. Build e Deploy
```bash
# Build local (opcional para teste)
docker-compose up --build

# Deploy em produção
docker-compose -f docker-compose.prod.yml up -d
```

### 3. Verificar Deploy
```bash
# Verificar containers
docker-compose ps

# Ver logs
docker-compose logs -f app

# Acessar aplicação
curl http://localhost:3000/api/health
```

## 🔧 Configuração Essencial

### Variáveis de Ambiente

Principais configurações em `.env.production`:

```bash
# Database
DATABASE_URL="file:./prod.db"
# Para PostgreSQL: "postgresql://user:pass@localhost:5432/voxstudent"

# Authentication
JWT_SECRET="sua-chave-jwt-super-secreta-256-bits"
NEXTAUTH_SECRET="sua-chave-nextauth-diferente"

# Email (SMTP)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="sua-senha-de-app"
SMTP_FROM_EMAIL="<EMAIL>"

# Application
APP_URL="https://seudominio.com"
NODE_ENV="production"

# Admin
SUPER_ADMIN_EMAIL="<EMAIL>"
```

### Configuração de Domínio

#### Com Nginx (Recomendado)
```nginx
server {
    listen 80;
    server_name seudominio.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

#### SSL com Certbot
```bash
# Instalar Certbot
sudo apt install certbot python3-certbot-nginx

# Gerar certificado
sudo certbot --nginx -d seudominio.com

# Renovação automática
sudo crontab -e
# Adicionar: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 Monitoramento

### Health Checks
```bash
# Verificar saúde da aplicação
curl http://localhost:3000/api/health

# Verificar banco de dados
curl http://localhost:3000/api/health/database

# Verificar WhatsApp
curl http://localhost:3000/api/whatsapp/status
```

### Logs
```bash
# Logs da aplicação
docker-compose logs -f app

# Logs do banco (se usando PostgreSQL)
docker-compose logs -f db

# Logs específicos com timestamp
docker-compose logs -f --timestamps app
```

### Backup do Banco de Dados

#### SQLite
```bash
# Backup manual
cp data/prod.db backups/prod-$(date +%Y%m%d-%H%M%S).db

# Script de backup automático
#!/bin/bash
BACKUP_DIR="/home/<USER>/backups"
DB_PATH="/home/<USER>/vox-student/data/prod.db"
DATE=$(date +%Y%m%d-%H%M%S)

mkdir -p $BACKUP_DIR
cp $DB_PATH $BACKUP_DIR/voxstudent-$DATE.db

# Manter apenas últimos 30 backups
find $BACKUP_DIR -name "voxstudent-*.db" -mtime +30 -delete
```

#### PostgreSQL
```bash
# Backup
docker-compose exec db pg_dump -U postgres voxstudent > backup-$(date +%Y%m%d).sql

# Restore
cat backup-20240101.sql | docker-compose exec -T db psql -U postgres voxstudent
```

## 🔐 Segurança

### SSL/TLS
- **Sempre use HTTPS em produção**
- Configure certificados SSL válidos
- Use Nginx ou Caddy como proxy reverso

### Firewall
```bash
# UFW básico
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### Atualizações
```bash
# Atualizar sistema
sudo apt update && sudo apt upgrade -y

# Atualizar aplicação
git pull origin main
docker-compose down
docker-compose up --build -d
```

## 📱 Configurações Específicas

### WhatsApp
1. Configure `WHATSAPP_ENABLED=true`
2. Acesse `/admin/whatsapp`
3. Escaneie QR code com WhatsApp
4. Teste envio de mensagens

### Google Calendar (Mentorias)
1. Configure credenciais OAuth2
2. Ative Google Calendar API
3. Configure redirect URLs
4. Teste agendamento de mentorias

### Email SMTP
1. Configure servidor SMTP
2. Use senhas de aplicativo (Gmail)
3. Teste envio de magic links
4. Configure SPF/DKIM se possível

## 📈 Performance

### Otimizações de Produção
- **Habilite compressão gzip**
- **Configure cache headers**
- **Use CDN para assets estáticos**
- **Otimize imagens**

### Database Performance
```sql
-- Índices importantes (PostgreSQL)
CREATE INDEX idx_attendance_student_lesson ON attendance(student_id, lesson_id);
CREATE INDEX idx_enrollment_student_status ON enrollment(student_id, status);
CREATE INDEX idx_lessons_scheduled_date ON lessons(scheduled_date);
```

## 🚨 Troubleshooting Comum

### Aplicação não inicia
```bash
# Verificar logs
docker-compose logs app

# Verificar portas
netstat -tlnp | grep :3000

# Verificar variáveis de ambiente
docker-compose exec app env | grep -E "(DATABASE_URL|JWT_SECRET)"
```

### Banco de dados
```bash
# Verificar conexão
docker-compose exec app npx prisma db push

# Reset do banco (CUIDADO!)
docker-compose exec app npx prisma db push --force-reset
```

### WhatsApp desconectado
1. Acesse `/admin/whatsapp`
2. Clique em "Desconectar"
3. Escaneie novo QR code
4. Verifique logs: `docker-compose logs app | grep whatsapp`

## 📞 Suporte

### Recursos
- [Docker Deployment](./docker-deployment.md)
- [Server Setup](./server-setup.md)
- [Monitoring](./monitoring.md)

### Logs Úteis
- **Aplicação**: `docker-compose logs app`
- **Nginx**: `sudo tail -f /var/log/nginx/error.log`
- **Sistema**: `journalctl -u docker`

---

Para deploy em produção, recomenda-se sempre usar HTTPS, backup regular e monitoramento contínuo.