# Sistema de Feedback NPS - VoxStudent

O Sistema de Net Promoter Score (NPS) do VoxStudent coleta feedback automaticamente dos alunos após cada aula concluída, utilizando WhatsApp para máxima facilidade e alcance.

## 🎯 Como Funciona

### Fluxo Automático
1. **Professor marca aula como concluída** → Sistema detecta automaticamente
2. **30 minutos depois** → Mensagens NPS são agendadas automaticamente
3. **WhatsApp é enviado** → Apenas para alunos que estiveram presentes
4. **Aluno recebe 3 links diretos** → Para avaliação rápida (Boa, Neutra, Ruim)
5. **Feedback é coletado** → Com possibilidade de comentário anônimo
6. **Dados aparecem no dashboard** → Para análise pelos administradores

### Cronograma Automático
- **Processamento**: A cada 15 minutos (cron job automático)
- **Limpeza**: Diariamente remove tokens expirados
- **Retry**: Tentativas automáticas para mensagens falhadas

## 📱 Experiência do Aluno

### Mensagem WhatsApp Recebida
```
🎓 Como foi sua aula de [Nome do Curso]?

📚 Aula: [Título da Aula]
📅 Data: [Data da Aula]
🏫 Turma: [Nome da Turma]

Avalie rapidamente:
😊 BOA - [link direto]
😐 NEUTRA - [link direto]  
😞 RUIM - [link direto]

📝 Ou deixe um comentário detalhado:
[link para formulário completo]

⏰ Links válidos por 24 horas
```

### Opções de Resposta

#### 1. **Links Diretos (Rápidos)**
- **😊 BOA**: Registra avaliação positiva imediatamente
- **😐 NEUTRA**: Registra avaliação neutra imediatamente
- **😞 RUIM**: Registra avaliação negativa imediatamente

#### 2. **Formulário Completo**
- Permite escolher avaliação (Boa/Neutra/Ruim)
- Campo para comentário anônimo opcional
- Possibilidade de alterar avaliação anterior
- Interface responsiva mobile-friendly

### Características dos Tokens
- **Segurança**: Tokens únicos estilo OTP
- **Validade**: 24 horas para responder
- **Privacidade**: Comentários completamente anônimos
- **Flexibilidade**: Pode alterar resposta dentro do prazo

## 👨‍💼 Para Administradores

### Dashboard NPS (`/admin/nps`)

#### Métricas em Tempo Real
- **Total de Feedbacks**: Coletados até o momento
- **Taxa de Resposta**: % de alunos que responderam
- **Distribuição NPS**: Gráfico Boa/Neutra/Ruim
- **Tendências**: Evolução ao longo do tempo

#### Monitoramento de Mensagens
- **Status das Mensagens**: Pendente/Enviada/Falhada
- **Log de Erros**: Detalhes de falhas de envio
- **Processamento Manual**: Botão para forçar processamento
- **Estatísticas de Entrega**: Taxa de sucesso WhatsApp

#### Análises Avançadas
- **NPS por Professor**: Performance individual
- **NPS por Curso**: Feedback por programa
- **NPS por Turma**: Análise específica
- **NPS por Período**: Tendências temporais

### Relatórios Disponíveis

#### 1. **Relatório Geral**
- Média NPS geral do sistema
- Distribuição percentual (Boa/Neutra/Ruim)
- Total de respostas vs. total de aulas
- Taxa de resposta média

#### 2. **Relatório por Professor**
- NPS médio por educador
- Número de aulas avaliadas
- Distribuição de notas
- Comentários anônimos recebidos

#### 3. **Relatório por Curso**
- Performance de cada programa
- Comparação entre cursos
- Evolução ao longo do tempo
- Identificação de pontos de melhoria

#### 4. **Relatório Temporal**
- Tendências mensais/semanais
- Identificação de padrões
- Sazonalidade do feedback
- Correlação com eventos específicos

## 🔧 Configurações Técnicas

### Integração WhatsApp
- **Automática**: Usa a conexão WhatsApp existente
- **Queue System**: Integrado com fila de mensagens
- **Rate Limiting**: Respeita limites do WhatsApp
- **Retry Logic**: Tentativas automáticas em caso de falha

### Segurança e Privacidade
- **Tokens Seguros**: Criptografados e únicos
- **Expiração Automática**: 24 horas de validade
- **Anonimato**: Comentários não identificam o aluno
- **LGPD Compliant**: Conforme lei de proteção de dados

### Performance
- **Processamento Assíncrono**: Não bloqueia outras operações
- **Cache Inteligente**: Otimização de consultas
- **Limpeza Automática**: Remove dados expirados
- **Monitoramento**: Logs detalhados para debug

## 📊 Métricas e KPIs

### Net Promoter Score
```
NPS = % Promotores (Boa) - % Detratores (Ruim)
```
- **Boa (9-10)**: Promotores - Recomendam o curso
- **Neutra (7-8)**: Neutros - Satisfeitos mas não promovem
- **Ruim (0-6)**: Detratores - Insatisfeitos

### Taxa de Resposta
```
Taxa = (Respostas Recebidas / Mensagens Enviadas) × 100
```

### Tempo de Resposta
- **Média**: Tempo médio para responder
- **Distribuição**: Respostas por período após envio
- **Padrões**: Horários de maior engajamento

## 🚀 Benefícios do Sistema

### Para a Instituição
- **Feedback Contínuo**: Melhoria constante da qualidade
- **Identificação Rápida**: Problemas detectados rapidamente
- **Dados Objetivos**: Métricas para tomada de decisão
- **Automação Total**: Sem trabalho manual necessário

### Para os Professores
- **Feedback Direto**: Saber como estão performando
- **Melhoria Contínua**: Dados para aprimoramento
- **Reconhecimento**: Identificação de boas práticas
- **Suporte**: Identificação de necessidades de apoio

### Para os Alunos
- **Voz Ativa**: Canal direto para opinar
- **Facilidade**: WhatsApp = zero atrito
- **Anonimato**: Liberdade para ser honesto
- **Impacto**: Ver melhorias baseadas no feedback

## 🔧 Troubleshooting

### Problemas Comuns

#### Mensagens Não Enviadas
- Verificar conexão WhatsApp em `/admin/whatsapp`
- Checar logs de erro no dashboard NPS
- Validar números de telefone dos alunos
- Confirmar que WhatsApp está funcionando

#### Baixa Taxa de Resposta
- Verificar se links estão funcionando
- Confirmar que tokens não expiraram
- Testar formulário de feedback
- Revisar template da mensagem

#### Dados Não Aparecem
- Aguardar processamento (até 15 minutos)
- Forçar processamento manual no dashboard
- Verificar se aula foi marcada como concluída
- Confirmar que alunos tiveram presença

### Logs e Monitoramento
- **Dashboard NPS**: Monitoramento em tempo real
- **Logs de Sistema**: Detalhes técnicos de erros
- **Queue Monitor**: Status da fila de mensagens
- **WhatsApp Logs**: Histórico de envios

## 📈 Otimização do Sistema

### Melhores Práticas
1. **Marque aulas como concluídas imediatamente**
2. **Monitore dashboard NPS regularmente**
3. **Analise comentários para insights**
4. **Compare dados entre períodos**
5. **Use feedback para melhorias concretas**

### Dicas de Análise
- **Foque em tendências**, não apenas números absolutos
- **Compare professores similares** para identificar boas práticas
- **Correlacione NPS com outros dados** (presença, conclusão)
- **Aja rapidamente** em casos de NPS muito baixo

---

O Sistema NPS do VoxStudent transforma feedback em insights acionáveis, ajudando a elevar continuamente a qualidade educacional!