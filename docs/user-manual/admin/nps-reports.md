# Relatórios de NPS - Administração

Os relatórios de NPS (Net Promoter Score) fornecem insights detalhados sobre a satisfação dos alunos, permitindo análises por professor, curso, turma e período.

## 📊 Dashboard NPS Principal

Acesse via `/admin/nps` para visualizar o painel principal de análise.

### Métricas Globais
- **NPS Geral**: Score médio de todo o sistema
- **Taxa de Resposta**: Percentual de alunos que responderam
- **Total de Feedbacks**: Número absoluto de avaliações
- **Distribuição**: Gráfico pizza (Boa/Neutra/Ruim)

### Gráficos em Tempo Real
- **Tendência Temporal**: Evolução do NPS ao longo do tempo
- **Comparação Mensal**: NPS mês atual vs. anterior
- **Heatmap de Horários**: Quando os alunos mais respondem
- **Taxa de Resposta**: Por canal de envio (WhatsApp/Email)

## 🎯 Relatório por Professor

### Como Acessar
1. Dashboard NPS → Filtrar por Professor
2. Ou acessar `/admin/reports/nps/teachers`

### Métricas por Professor
```
📊 Professor: João Silva
🎯 NPS Médio: +45 (Excelente)
📈 Tendência: ↗️ +12 vs. mês anterior
📚 Aulas Avaliadas: 28
💬 Comentários: 15 (8 positivos, 7 neutros)
```

### Análises Detalhadas
- **Ranking de Professores**: Ordenação por NPS
- **Evolução Individual**: Gráfico temporal por educador
- **Distribuição de Notas**: Boa/Neutra/Ruim por professor
- **Comentários Anônimos**: Agrupados por educador

### Insights Disponíveis
- **Melhores Práticas**: Professores com NPS consistentemente alto
- **Oportunidades**: Educadores com potencial de melhoria
- **Padrões**: Correlação entre NPS e outros fatores
- **Suporte Necessário**: Professores que precisam de apoio

## 📚 Relatório por Curso

### Métricas por Programa
- **NPS por Curso**: Score médio de cada programa
- **Comparação**: Ranking de cursos por satisfação
- **Tendências**: Evolução ao longo dos módulos
- **Correlação**: NPS vs. taxa de conclusão

### Análise de Conteúdo
- **Aulas Críticas**: Lessons com NPS baixo recorrente
- **Pontos Fortes**: Conteúdos mais bem avaliados
- **Melhorias**: Módulos que precisam de ajuste
- **Sequência**: Impacto da ordem das aulas no NPS

### Exemplo de Relatório
```
📈 Curso: Desenvolvimento Web Full Stack
🎯 NPS Médio: +38
📊 Distribuição:
   😊 Boa: 65% (130 avaliações)
   😐 Neutra: 25% (50 avaliações)
   😞 Ruim: 10% (20 avaliações)

🔍 Aulas Destaque:
   ✅ Aula 5 - React Hooks: NPS +78
   ⚠️ Aula 12 - Banco de Dados: NPS -15
   ✅ Projeto Final: NPS +85
```

## 🏫 Relatório por Turma

### Análise de Grupos
- **NPS por Turma**: Performance de grupos específicos
- **Dinâmica de Grupo**: Como o grupo influencia a satisfação
- **Horários**: Correlação entre horário e satisfação
- **Tamanho**: Impacto do número de alunos no NPS

### Fatores Analisados
- **Professor**: Mesmo educador, turmas diferentes
- **Horário**: Manhã vs. tarde vs. noite
- **Modalidade**: Presencial vs. online vs. híbrido
- **Período**: Sazonalidade e eventos especiais

## 👨‍🎓 Relatório por Aluno

### Jornada Individual
- **Evolução do NPS**: Como a satisfação muda ao longo do curso
- **Padrões de Resposta**: Frequência e timing das avaliações
- **Comentários**: Histórico de feedback anônimo
- **Correlações**: NPS vs. presença, performance, etc.

### Análise Comportamental
- **Engajamento**: Alunos que sempre respondem vs. ocasionais
- **Críticos Construtivos**: Estudantes com feedback detalhado
- **Satisfação Geral**: Perfil de satisfação por tipo de aluno

## 📅 Filtros e Períodos

### Filtros Disponíveis
- **Professor**: Educador específico ou grupo
- **Curso**: Programa específico ou categoria
- **Turma**: Grupo específico ou modalidade
- **Período**: Data de início e fim personalizada
- **Tipo de Aula**: Regular, recuperação, mentoria

### Períodos Pré-definidos
- **Última Semana**: Feedback dos últimos 7 dias
- **Último Mês**: Dados dos últimos 30 dias
- **Trimestre Atual**: 3 meses mais recentes
- **Ano Letivo**: Período acadêmico completo
- **Personalizado**: Datas específicas

## 📈 Métricas Avançadas

### Net Promoter Score
```
Cálculo do NPS:
NPS = % Promotores (Boa) - % Detratores (Ruim)

Interpretação:
+70 a +100: Excelente (World Class)
+50 a +69: Muito Bom (Excellent)
+30 a +49: Bom (Good)
+10 a +29: Regular (Average)
-100 a +9: Crítico (Needs Improvement)
```

### Métricas Complementares
- **Taxa de Resposta**: (Respostas / Mensagens Enviadas) × 100
- **Tempo de Resposta**: Média de tempo para avaliar
- **Engagement Score**: Frequência de respostas detalhadas
- **Satisfação Líquida**: Média ponderada das avaliações

### Tendências e Padrões
- **Sazonalidade**: Variações mensais/semanais
- **Correlação**: NPS vs. presença, conclusão, etc.
- **Preditores**: Fatores que indicam NPS futuro
- **Ciclos**: Padrões recorrentes identificados

## 📊 Exportação de Dados

### Formatos Disponíveis
- **PDF**: Relatórios prontos para apresentação
- **Excel**: Dados brutos para análise personalizada
- **CSV**: Integração com outras ferramentas
- **Dashboard**: Visualização interativa online

### Dados Exportáveis
- **Métricas Agregadas**: NPS, taxas, distribuições
- **Dados Detalhados**: Cada avaliação individual (anonimizada)
- **Comentários**: Feedback textual agrupado
- **Séries Temporais**: Evolução ao longo do tempo

## 🎯 Ações Baseadas em Insights

### NPS Alto (+50 ou mais)
- **Documentar Boas Práticas**: O que está funcionando
- **Replicar Sucessos**: Aplicar em outras turmas/cursos
- **Reconhecer Professores**: Feedback positivo para educadores
- **Casos de Estudo**: Usar como exemplo interno

### NPS Neutro (0 a +49)
- **Investigar Causas**: Por que não é excelente
- **Melhorias Incrementais**: Pequenos ajustes no conteúdo
- **Feedback Direto**: Conversar com professores
- **Monitoramento**: Acompanhar evolução de perto

### NPS Baixo (menor que 0)
- **Ação Imediata**: Intervenção urgente necessária
- **Análise Profunda**: Identificar problemas específicos
- **Plano de Melhoria**: Ações concretas e mensuráveis
- **Suporte Intensivo**: Apoio extra para professores

## 🔍 Análise de Comentários

### Categorização Automática
- **Temas Recorrentes**: Agrupamento por tópicos
- **Sentimento**: Análise positivo/neutro/negativo
- **Palavras-chave**: Termos mais frequentes
- **Urgência**: Comentários que requerem ação

### Insights dos Comentários
- **Pontos Fortes**: O que os alunos mais elogiam
- **Oportunidades**: Áreas de melhoria identificadas
- **Sugestões**: Ideias propostas pelos estudantes
- **Problemas**: Questões que precisam ser resolvidas

## 📞 Suporte e Troubleshooting

### Problemas Comuns
- **Dados não aparecem**: Aguardar processamento (15 min)
- **NPS incorreto**: Verificar período selecionado
- **Comentários ausentes**: Confirmar que foram enviados
- **Filtros não funcionam**: Limpar cache do navegador

### Otimização de Análises
1. **Use períodos consistentes** para comparações
2. **Combine métricas quantitativas e qualitativas**
3. **Foque em tendências**, não apenas números pontuais
4. **Correlacione NPS com outros dados** do sistema
5. **Aja rapidamente** em casos críticos

---

Os relatórios de NPS são fundamentais para a melhoria contínua da qualidade educacional no VoxStudent!