# Guia de Desenvolvimento - VoxStudent

Documentação completa para desenvolvedores contribuindo com o VoxStudent.

## 📋 Índice

- [**Arquitetura do Sistema**](./architecture.md) - Visão técnica detalhada
- [**Configuração de Desenvolvimento**](./setup.md) - Setup do ambiente local
- [**Referência da API**](./api-reference.md) - Documentação completa da API
- [**Padrões de Código**](./coding-standards.md) - Convenções e boas práticas
- [**Contribuição**](./contributing.md) - Como contribuir com o projeto

## 🚀 Início Rápido para Desenvolvedores

### Pré-requisitos
- Node.js 18+ e npm
- Git configurado
- Editor com suporte TypeScript (VS Code recomendado)

### Setup Inicial
```bash
# 1. Clone o repositório
git clone <repository-url>
cd vox-student-nextjs

# 2. Instale dependências
npm install

# 3. Configure ambiente
cp .env.example .env.local

# 4. Setup banco de dados
npm run db:generate
npm run db:push

# 5. Inicie desenvolvimento
npm run dev
```

## 🏗️ Arquitetura

### Stack Principal
- **Frontend**: Next.js 15 (App Router), React 19, TypeScript
- **Styling**: Tailwind CSS v4, shadcn/ui
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: SQLite (dev), PostgreSQL (prod)
- **Auth**: JWT + HTTP-only cookies

### Estrutura de Pastas
```
src/
├── app/                 # Next.js App Router
│   ├── admin/          # Páginas administrativas
│   ├── api/            # API endpoints
│   ├── auth/           # Autenticação
│   └── recovery/       # Sistema de recuperação
├── components/         # Componentes React
│   ├── ui/            # Componentes base (shadcn)
│   ├── layouts/       # Layouts
│   └── navigation/    # Navegação
├── lib/               # Utilitários e serviços
│   ├── services/      # Lógica de negócio
│   ├── auth.ts       # Autenticação
│   └── prisma.ts     # Cliente do banco
├── contexts/          # React contexts
├── hooks/             # Custom hooks
└── types/             # Definições TypeScript
```

## 🔧 Comandos de Desenvolvimento

### Comandos Essenciais
```bash
npm run dev              # Servidor de desenvolvimento
npm run build           # Build para produção
npm run lint            # ESLint
npm run typecheck       # Verificação TypeScript
npm run test            # Testes unitários
npm run test:e2e        # Testes E2E
```

### Banco de Dados
```bash
npm run db:generate     # Gerar cliente Prisma
npm run db:push         # Aplicar mudanças no schema
npm run db:migrate      # Executar migrações
npm run db:studio       # Abrir Prisma Studio
```

### QA e Validação
```bash
npm run qa:setup        # Setup ambiente QA
npm run pre-release     # Validação completa
```

## 📝 Padrões de Desenvolvimento

### Service Layer Pattern
Organize lógica de negócio em serviços:

```typescript
// src/lib/services/students.service.ts
export class StudentsService {
  static async getStudentById(id: string) {
    return await prisma.student.findUnique({
      where: { id },
      include: { enrollments: true }
    });
  }
}
```

### API Routes
Siga o padrão estabelecido:

```typescript
// src/app/api/students/route.ts
export async function GET(request: NextRequest) {
  try {
    const authResult = await verifyAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Lógica da API
    
    return NextResponse.json({ data: result });
  } catch (error) {
    return NextResponse.json({ error: 'Internal error' }, { status: 500 });
  }
}
```

### Componentes React
Use TypeScript e padrões consistentes:

```typescript
interface StudentCardProps {
  student: Student;
  onEdit?: (id: string) => void;
}

export function StudentCard({ student, onEdit }: StudentCardProps) {
  return (
    <Card>
      {/* Implementação */}
    </Card>
  );
}
```

## 🧪 Testes

### Estrutura de Testes
```
tests/
├── e2e/                # Testes Playwright
│   ├── auth.spec.ts   # Autenticação
│   └── students.spec.ts # Gestão de alunos
└── unit/              # Testes Jest
    └── services/      # Testes de serviços
```

### Executar Testes
```bash
# Testes unitários
npm run test
npm run test:coverage

# Testes E2E
npm run test:e2e
npm run test:e2e:qa
```

## 🔒 Segurança

### Autenticação
- Magic links apenas (sem passwords)
- JWT com HTTP-only cookies
- Expiração automática de sessões

### Dados Biométricos
- Face descriptors (não imagens)
- Conformidade com LGPD
- Criptografia em trânsito e repouso

### API Security
- Rate limiting
- Validação de entrada
- Logs de auditoria

## 📦 Deploy

### Ambiente de Desenvolvimento
```bash
npm run dev
```

### Build de Produção
```bash
npm run build
npm run start
```

### Docker
```bash
docker-compose up --build
```

## 🤝 Contribuição

### Workflow
1. Fork do repositório
2. Criar branch de feature
3. Desenvolver e testar
4. Executar validações:
   ```bash
   npm run lint && npm run typecheck
   npm run test
   ```
5. Commit seguindo convenções
6. Abrir Pull Request

### Convenções de Commit
```bash
feat: adicionar nova funcionalidade
fix: corrigir bug
docs: atualizar documentação
style: mudanças de formatação
refactor: refatoração de código
test: adicionar ou corrigir testes
```

### Code Review
- Todos os PRs passam por review
- Testes devem passar
- Cobertura mínima de 80%
- Seguir padrões estabelecidos

## 📚 Recursos Adicionais

- [Prisma Documentation](https://www.prisma.io/docs)
- [Next.js App Router](https://nextjs.org/docs/app)
- [Tailwind CSS v4](https://tailwindcss.com/docs)
- [shadcn/ui Components](https://ui.shadcn.com)

## 🆘 Troubleshooting

### Problemas Comuns
- **Build errors**: Verificar tipos TypeScript
- **Database issues**: Executar `npm run db:push`
- **WhatsApp connection**: Verificar configuração
- **Camera permissions**: Verificar HTTPS em produção

### Debug
```bash
# Logs detalhados
DEBUG=* npm run dev

# Prisma debug
DEBUG="prisma:*" npm run dev
```

---

Para dúvidas específicas, consulte a documentação completa ou abra uma issue.