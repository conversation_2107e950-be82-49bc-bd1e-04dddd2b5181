# Arquitetura do Sistema - VoxStudent

Documentação detalhada da arquitetura técnica do VoxStudent.

## 🏗️ Visão Geral da Arquitetura

### Arquitetura de Alto Nível

```
┌─────────────────────────────────────────────────────────────────────┐
│                           CAMADA CLIENT                            │
├─────────────────────────────────────────────────────────────────────┤
│  React Components │ Face Recognition │ Camera Controls │ WhatsApp   │
│  Tailwind CSS     │ face-api.js      │ MediaDevices   │ QR Code    │
├─────────────────────────────────────────────────────────────────────┤
│                        CAMADA APPLICATION                          │
├─────────────────────────────────────────────────────────────────────┤
│  Next.js App Router │ API Routes │ Server Actions │ Middleware     │
│  Authentication     │ Services   │ Utilities      │ Validation     │
├─────────────────────────────────────────────────────────────────────┤
│                          CAMADA DATA                               │
├─────────────────────────────────────────────────────────────────────┤
│  Prisma ORM │ SQLite/PostgreSQL │ File Storage │ Session Storage  │
├─────────────────────────────────────────────────────────────────────┤
│                       CAMADA INTEGRATION                           │
├─────────────────────────────────────────────────────────────────────┤
│  WhatsApp Web.js │ Email (SMTP/Mailpit) │ Face Recognition Models │
└─────────────────────────────────────────────────────────────────────┘
```

## 🛠️ Stack Tecnológico

### Frontend
- **Next.js 15**: Framework React com App Router
- **React 19**: Biblioteca de UI componentes
- **TypeScript**: Tipagem estática
- **Tailwind CSS v4**: Framework CSS utility-first
- **shadcn/ui**: Componentes headless baseados em Radix UI
- **face-api.js**: Reconhecimento facial no browser
- **Lucide React**: Biblioteca de ícones

### Backend
- **Node.js**: Runtime JavaScript
- **Next.js API Routes**: Endpoints RESTful
- **Prisma ORM**: Abstração do banco de dados
- **JWT**: Gerenciamento de sessões
- **bcrypt**: Hash de senhas (quando necessário)

### Banco de Dados
- **SQLite**: Desenvolvimento local
- **PostgreSQL**: Produção (opcional)
- **Prisma Client**: ORM type-safe

### Integrações
- **whatsapp-web.js**: Integração WhatsApp
- **Nodemailer**: Envio de emails
- **Google Calendar API**: Agendamento de mentorias

## 🗂️ Estrutura de Pastas Detalhada

```
src/
├── app/                          # Next.js App Router
│   ├── (auth)/                  # Route groups para auth
│   ├── admin/                   # Páginas administrativas
│   │   ├── attendance/         # Controle de presença
│   │   ├── classes/            # Gestão de turmas
│   │   ├── courses/            # Gestão de cursos
│   │   ├── students/           # Gestão de alunos
│   │   └── settings/           # Configurações
│   ├── api/                    # API Routes
│   │   ├── auth/               # Endpoints de autenticação
│   │   ├── students/           # CRUD de alunos
│   │   ├── courses/            # CRUD de cursos
│   │   ├── attendance/         # Controle de presença
│   │   ├── whatsapp/           # Integração WhatsApp
│   │   └── recovery/           # Sistema de recuperação
│   ├── auth/                   # Páginas de autenticação
│   ├── mentoring/              # Sistema de mentorias
│   ├── recovery/               # Recuperação/refazer aulas
│   ├── layout.tsx              # Layout raiz
│   ├── page.tsx                # Página inicial
│   └── globals.css             # Estilos globais
├── components/                  # Componentes React
│   ├── ui/                     # Componentes base (shadcn/ui)
│   │   ├── button.tsx
│   │   ├── card.tsx
│   │   ├── input.tsx
│   │   └── ...
│   ├── layouts/                # Componentes de layout
│   │   ├── AdminLayout.tsx
│   │   └── AuthLayout.tsx
│   ├── navigation/             # Componentes de navegação
│   │   ├── Sidebar.tsx
│   │   └── Header.tsx
│   └── features/               # Componentes específicos
│       ├── attendance/
│       ├── face-recognition/
│       └── whatsapp/
├── lib/                        # Utilitários e serviços
│   ├── services/               # Lógica de negócio
│   │   ├── auth.service.ts
│   │   ├── students.service.ts
│   │   ├── courses.service.ts
│   │   ├── attendance.service.ts
│   │   ├── whatsapp.service.ts
│   │   └── recovery.service.ts
│   ├── auth.ts                 # Utilitários de autenticação
│   ├── prisma.ts               # Cliente Prisma
│   ├── utils.ts                # Utilitários gerais
│   └── validations.ts          # Schemas de validação
├── contexts/                   # React Contexts
│   ├── AuthContext.tsx
│   └── ThemeContext.tsx
├── hooks/                      # Custom React Hooks
│   ├── useAuth.ts
│   ├── useCamera.ts
│   └── useFaceRecognition.ts
├── types/                      # Definições TypeScript
│   ├── auth.ts
│   ├── student.ts
│   └── course.ts
└── middleware.ts               # Middleware Next.js
```

## 🔐 Camada de Autenticação

### Fluxo de Autenticação
```
1. Usuário insere email/telefone
2. Sistema gera magic link
3. Link enviado via email/WhatsApp
4. Usuário clica no link
5. Sistema valida token
6. JWT gerado e armazenado em HTTP-only cookie
7. Middleware verifica autenticação em rotas protegidas
```

### Implementação
```typescript
// JWT Token Structure
interface JWTPayload {
  userId: string;
  email: string;
  role: string;
  exp: number;
}

// Auth Middleware
export function middleware(request: NextRequest) {
  const token = request.cookies.get('auth_token');
  
  if (isProtectedRoute(request.nextUrl.pathname)) {
    if (!token) {
      return NextResponse.redirect(new URL('/login', request.url));
    }
    
    const isValid = verifyToken(token.value);
    if (!isValid) {
      return NextResponse.redirect(new URL('/login', request.url));
    }
  }
}
```

## 📊 Camada de Dados

### Schema do Banco de Dados
```prisma
// Principais modelos
model User {
  id            String   @id @default(cuid())
  email         String   @unique
  profile       UserProfile?
  sessions      Session[]
}

model Student {
  id               String   @id @default(cuid())
  name             String
  email            String?
  faceDescriptor   String?  // JSON face descriptor
  enrollments      Enrollment[]
  attendance       Attendance[]
}

model Course {
  id          String   @id @default(cuid())
  name        String
  classes     Class[]
}

model Class {
  id          String   @id @default(cuid())
  courseId    String
  lessons     Lesson[]
  enrollments Enrollment[]
}

model Attendance {
  id                        String        @id @default(cuid())
  studentId                 String
  lessonId                  String
  status                    PresenceStatus
  markedByFacialRecognition Boolean       @default(false)
}
```

### Service Layer Pattern
```typescript
// Exemplo: StudentsService
export class StudentsService {
  static async createStudent(data: CreateStudentData) {
    return await prisma.student.create({
      data: {
        ...data,
        registrationDate: new Date()
      }
    });
  }

  static async getStudentById(id: string) {
    return await prisma.student.findUnique({
      where: { id },
      include: {
        enrollments: {
          include: {
            course: true,
            class: true
          }
        },
        attendance: {
          include: {
            lesson: true
          }
        }
      }
    });
  }
}
```

## 🎭 Camada de Apresentação

### Component Architecture
```typescript
// Hierarquia de Componentes
Layout (app/layout.tsx)
├── Header
├── Sidebar
└── Main Content
    ├── Page Components (app/*/page.tsx)
    ├── Feature Components (components/features/*)
    └── UI Components (components/ui/*)
```

### Estado Global
```typescript
// AuthContext
const AuthContext = createContext<{
  user: User | null;
  login: (token: string) => void;
  logout: () => void;
  loading: boolean;
}>({
  user: null,
  login: () => {},
  logout: () => {},
  loading: false
});
```

## 🤖 Integrações Externas

### WhatsApp Integration
```typescript
// WhatsApp Service Architecture
class WhatsAppService {
  private client: Client;
  
  async initialize() {
    this.client = new Client({
      authStrategy: new LocalAuth()
    });
    
    this.client.on('qr', (qr) => {
      // Gerar QR Code para auth
    });
    
    this.client.on('ready', () => {
      // Cliente pronto
    });
  }
  
  async sendMessage(phone: string, message: string) {
    return await this.client.sendMessage(phone, message);
  }
}
```

### Face Recognition
```typescript
// Face Recognition Flow
1. Load face-api.js models
2. Initialize webcam
3. Detect face in video stream
4. Extract face descriptor
5. Compare with stored descriptors
6. Mark attendance if match found
```

## 🚀 Performance e Otimização

### Next.js Optimizations
- **App Router**: Roteamento moderno com layouts aninhados
- **Server Components**: Renderização no servidor por padrão
- **Streaming**: Carregamento progressivo de UI
- **Code Splitting**: Divisão automática de código

### Database Optimization
- **Prisma Connection Pooling**: Pool de conexões
- **Selective Includes**: Carregamento otimizado de relações
- **Database Indexes**: Índices para queries frequentes

### Frontend Performance
- **Lazy Loading**: Carregamento sob demanda
- **Image Optimization**: Next.js Image component
- **Bundle Analysis**: Análise de tamanho do bundle

## 🔒 Segurança

### Security Measures
- **HTTPS Enforced**: TLS em produção
- **JWT Secure Cookies**: HTTP-only, Secure, SameSite
- **Input Validation**: Zod schemas
- **SQL Injection Prevention**: Prisma ORM
- **XSS Protection**: Sanitização de entrada
- **LGPD Compliance**: Proteção de dados biométricos

### Audit Trail
```typescript
// Exemplo de log de auditoria
model AuditLog {
  id          String   @id @default(cuid())
  userId      String?
  action      String   // 'CREATE', 'UPDATE', 'DELETE'
  tableName   String
  recordId    String?
  oldValues   String?  // JSON
  newValues   String?  // JSON
  timestamp   DateTime @default(now())
}
```

## 📈 Escalabilidade

### Horizontal Scaling
- **Stateless Design**: Aplicação sem estado no servidor
- **Database Scaling**: Suporte a PostgreSQL para produção
- **CDN Ready**: Assets estáticos otimizados
- **Container Ready**: Docker para deploy

### Monitoring
- **Health Checks**: Endpoints de saúde
- **Logging**: Structured logging
- **Error Tracking**: Comprehensive error handling
- **Performance Metrics**: Response time tracking

---

Esta arquitetura garante um sistema escalável, seguro e fácil de manter, seguindo as melhores práticas da indústria.