{"name": "vox-student-nextjs", "version": "1.3.2", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "db:generate": "npx prisma generate", "db:push": "npx prisma db push", "db:studio": "npx prisma studio", "db:migrate": "npx prisma migrate dev", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:qa": "playwright test tests/e2e/*.qa.spec.ts", "qa:setup": "docker-compose -f docker-compose.qa.yml up -d --build", "qa:teardown": "docker-compose -f docker-compose.qa.yml down -v", "qa:test": "docker-compose -f docker-compose.qa.yml --profile testing up --build --abort-on-container-exit", "qa:logs": "docker-compose -f docker-compose.qa.yml logs -f", "qa:full": "npm run qa:setup && sleep 30 && npm run test:e2e:qa && npm run qa:teardown", "db:migrate:qa": "dotenv -e .env.qa npx prisma migrate deploy", "db:seed:qa": "dotenv -e .env.qa npx prisma db seed", "db:reset:qa": "dotenv -e .env.qa npx prisma migrate reset --force", "pre-release": "./scripts/pre-release-qa.sh", "pre-release:setup": "./scripts/pre-release-qa.sh --setup-only", "pre-release:cleanup": "./scripts/pre-release-qa.sh --cleanup"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.11.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@tailwindcss/postcss": "^4.1.11", "@types/qrcode": "^1.5.5", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "encoding": "^0.1.13", "face-api.js": "^0.22.2", "googleapis": "^153.0.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "next": "^15.4.2", "nodemailer": "^7.0.5", "qrcode": "^1.5.4", "qrcode-terminal": "^0.12.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "whatsapp-web.js": "^1.31.0", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.54.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/bcryptjs": "^2.4.6", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/puppeteer": "^5.4.7", "@types/react": "^19", "@types/react-dom": "^19", "dotenv-cli": "^7.4.2", "eslint": "^9", "eslint-config-next": "15.3.5", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "node-fetch": "^2.7.0", "prisma": "^6.12.0", "puppeteer": "^24.13.0", "tailwindcss": "^4.1.11", "typescript": "^5", "whatwg-fetch": "^3.6.20"}}