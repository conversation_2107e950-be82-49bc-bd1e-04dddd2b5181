import React from 'react';
import { AdminCard } from '@/components/layouts/admin-card';
import { LucideIcon } from 'lucide-react';

interface FormCardProps {
  title: string;
  description?: string;
  icon?: LucideIcon;
  children: React.ReactNode;
  className?: string;
  headerAction?: React.ReactNode;
}

export function FormCard({ 
  title, 
  description, 
  icon, 
  children, 
  className = '',
  headerAction
}: FormCardProps) {
  return (
    <AdminCard 
      title={title} 
      description={description} 
      icon={icon} 
      className={className}
      headerAction={headerAction}
    >
      <div className="space-y-6 pt-6">
        {children}
      </div>
    </AdminCard>
  );
}