import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './select';
import { Label } from './label';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ModernSelectProps {
  label?: string;
  icon?: LucideIcon;
  error?: string;
  containerClassName?: string;
  placeholder?: string;
  value?: string;
  onValueChange?: (value: string) => void;
  options: Array<{ value: string; label: string; }>;
  disabled?: boolean;
}

export function ModernSelect({ 
  label, 
  icon: Icon, 
  error, 
  containerClassName,
  placeholder,
  value,
  onValueChange,
  options,
  disabled = false
}: ModernSelectProps) {
  return (
    <div className={cn("space-y-2", containerClassName)}>
      {label && (
        <Label className="text-sm font-semibold text-gray-700 flex items-center">
          {Icon && <Icon className="w-4 h-4 mr-2 text-[#667eea]" />}
          {label}
        </Label>
      )}
      <Select value={value} onValueChange={onValueChange} disabled={disabled}>
        <SelectTrigger 
          className={cn(
            "border-2 border-gray-200 focus:border-[#667eea] focus:ring-4 focus:ring-[#667eea]/20 transition-all duration-300",
            error && "border-red-500 focus:border-red-500 focus:ring-red-500/20"
          )}
        >
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent className="bg-white border border-gray-200 shadow-lg">
          {options.map((option) => (
            <SelectItem 
              key={option.value} 
              value={option.value}
              className="hover:bg-gray-100 focus:bg-gray-100"
            >
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  );
}