import React from 'react';
import { Button, ButtonProps } from './button';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ModernButtonProps extends ButtonProps {
  icon?: LucideIcon;
  gradient?: boolean;
  loading?: boolean;
}

export function ModernButton({ 
  icon: Icon, 
  gradient = true,
  loading = false,
  className,
  children,
  disabled,
  ...props 
}: ModernButtonProps) {
  const isDisabled = disabled || loading;
  
  const gradientClasses = gradient 
    ? "bg-gradient-to-r from-[#667eea] to-[#764ba2] hover:from-[#5a6fd8] hover:to-[#6b4190] text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300"
    : "";

  return (
    <Button
      className={cn(
        gradientClasses,
        isDisabled && "opacity-50 transform-none cursor-not-allowed",
        className
      )}
      disabled={isDisabled}
      {...props}
    >
      {loading ? (
        <>
          <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
          Carregando...
        </>
      ) : (
        <>
          {Icon && <Icon className="w-4 h-4 mr-2" />}
          {children}
        </>
      )}
    </Button>
  );
}