import React from 'react';
import { Input, InputProps } from './input';
import { Label } from './label';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ModernInputProps extends InputProps {
  label?: string;
  icon?: LucideIcon;
  error?: string;
  containerClassName?: string;
}

export function ModernInput({ 
  label, 
  icon: Icon, 
  error, 
  className,
  containerClassName,
  ...props 
}: ModernInputProps) {
  return (
    <div className={cn("space-y-2", containerClassName)}>
      {label && (
        <Label htmlFor={props.id} className="text-sm font-semibold text-gray-700 flex items-center">
          {Icon && <Icon className="w-4 h-4 mr-2 text-[#667eea]" />}
          {label}
        </Label>
      )}
      <Input
        className={cn(
          "border-2 border-gray-200 focus:border-[#667eea] focus:ring-4 focus:ring-[#667eea]/20 transition-all duration-300",
          error && "border-red-500 focus:border-red-500 focus:ring-red-500/20",
          className
        )}
        {...props}
      />
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  );
}