import React from 'react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, MapPin, CheckCircle, Circle } from 'lucide-react';

interface LessonProps {
  lessonNumber?: number | null;
  title: string;
  description?: string | null;
  scheduledDate?: Date | string;
  duration?: number | null;
  location?: string | null;
  isCompleted?: boolean;
  className?: string;
  showNumber?: boolean; // Whether to show lesson number
  showCompletionStatus?: boolean;
  onClick?: () => void;
}

export function Lesson({
  lessonNumber,
  title,
  description,
  scheduledDate,
  duration,
  location,
  isCompleted = false,
  className,
  showNumber = true,
  showCompletionStatus = false,
  onClick
}: LessonProps) {
  // Determine display logic for lesson title and number
  const hasCustomTitle = title && title.trim() !== '' && title !== `Aula ${lessonNumber}`;
  const displayTitle = hasCustomTitle ? title : `Aula ${lessonNumber}`;
  const shouldShowNumber = showNumber && lessonNumber && hasCustomTitle;
  
  // Format scheduled date
  const formatDate = (date: Date | string | undefined) => {
    if (!date) return null;
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('pt-BR', {
      weekday: 'short',
      day: '2-digit',
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Format duration
  const formatDuration = (minutes: number | null | undefined) => {
    if (!minutes) return null;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0 && mins > 0) {
      return `${hours}h ${mins}min`;
    } else if (hours > 0) {
      return `${hours}h`;
    } else {
      return `${mins}min`;
    }
  };

  return (
    <div
      className={cn(
        "p-4 border rounded-lg transition-all duration-200",
        "hover:shadow-md hover:border-[#667eea]/30",
        isCompleted && "bg-green-50 border-green-200",
        onClick && "cursor-pointer hover:bg-gray-50",
        className
      )}
      onClick={onClick}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          {/* Title and Number */}
          <div className="flex items-center gap-2 mb-2">
            {showCompletionStatus && (
              isCompleted ? (
                <CheckCircle className="w-5 h-5 text-green-600" />
              ) : (
                <Circle className="w-5 h-5 text-gray-400" />
              )
            )}
            
            <h3 className="font-semibold text-gray-900">
              {shouldShowNumber && (
                <span className="text-[#667eea] mr-2">#{lessonNumber}</span>
              )}
              {displayTitle}
            </h3>
            
            {isCompleted && (
              <Badge variant="success" className="ml-2">
                Concluída
              </Badge>
            )}
          </div>

          {/* Description */}
          {description && (
            <p className="text-sm text-gray-600 mb-3">
              {description}
            </p>
          )}

          {/* Metadata */}
          <div className="flex flex-wrap gap-4 text-sm text-gray-500">
            {scheduledDate && (
              <div className="flex items-center gap-1">
                <Calendar className="w-4 h-4" />
                <span>{formatDate(scheduledDate)}</span>
              </div>
            )}
            
            {duration && (
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                <span>{formatDuration(duration)}</span>
              </div>
            )}
            
            {location && (
              <div className="flex items-center gap-1">
                <MapPin className="w-4 h-4" />
                <span>{location}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

interface LessonListProps {
  lessons: Array<{
    id?: string;
    lessonNumber?: number | null;
    title: string;
    description?: string | null;
    scheduledDate?: Date | string;
    duration?: number | null;
    location?: string | null;
    isCompleted?: boolean;
  }>;
  showNumber?: boolean;
  showCompletionStatus?: boolean;
  onLessonClick?: (lesson: any) => void;
  className?: string;
}

export function LessonList({
  lessons,
  showNumber = true,
  showCompletionStatus = false,
  onLessonClick,
  className
}: LessonListProps) {
  if (lessons.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <Calendar className="w-12 h-12 mx-auto mb-4 text-gray-400" />
        <p>Nenhuma aula encontrada</p>
      </div>
    );
  }

  return (
    <div className={cn("space-y-3", className)}>
      {lessons.map((lesson, index) => (
        <Lesson
          key={lesson.id || index}
          lessonNumber={lesson.lessonNumber}
          title={lesson.title}
          description={lesson.description}
          scheduledDate={lesson.scheduledDate}
          duration={lesson.duration}
          location={lesson.location}
          isCompleted={lesson.isCompleted}
          showNumber={showNumber}
          showCompletionStatus={showCompletionStatus}
          onClick={onLessonClick ? () => onLessonClick(lesson) : undefined}
        />
      ))}
    </div>
  );
}