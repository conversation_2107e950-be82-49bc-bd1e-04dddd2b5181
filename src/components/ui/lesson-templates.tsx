import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus, Trash2, GripVertical, BookOpen } from 'lucide-react';

export interface LessonTemplate {
  lessonNumber: number;
  title: string;
  description?: string;
}

interface LessonTemplatesProps {
  numberOfLessons: number;
  lessonTemplates: LessonTemplate[];
  onChange: (templates: LessonTemplate[]) => void;
  className?: string;
}

export function LessonTemplates({
  numberOfLessons,
  lessonTemplates,
  onChange,
  className
}: LessonTemplatesProps) {
  const [templates, setTemplates] = useState<LessonTemplate[]>(lessonTemplates);

  // Update templates when numberOfLessons changes
  useEffect(() => {
    if (numberOfLessons > 0) {
      const updatedTemplates: LessonTemplate[] = [];
      
      // Create or update templates for each lesson
      for (let i = 1; i <= numberOfLessons; i++) {
        const existing = templates.find(t => t.lessonNumber === i);
        updatedTemplates.push({
          lessonNumber: i,
          title: existing?.title || '', // Título em branco por padrão
          description: existing?.description || ''
        });
      }
      
      setTemplates(updatedTemplates);
      onChange(updatedTemplates);
    } else {
      setTemplates([]);
      onChange([]);
    }
  }, [numberOfLessons]);

  // Update parent when templates change
  useEffect(() => {
    onChange(templates);
  }, [templates, onChange]);

  const updateTemplate = (lessonNumber: number, field: 'title' | 'description', value: string) => {
    setTemplates(prev => prev.map(template => 
      template.lessonNumber === lessonNumber
        ? { ...template, [field]: value }
        : template
    ));
  };

  const resetToDefault = (lessonNumber: number) => {
    updateTemplate(lessonNumber, 'title', ''); // Reset para título vazio
    updateTemplate(lessonNumber, 'description', '');
  };

  if (numberOfLessons === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="w-5 h-5" />
            Títulos das Aulas
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <BookOpen className="w-12 h-12 mx-auto mb-4 text-gray-400" />
            <p>Defina o número de aulas para configurar os títulos</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BookOpen className="w-5 h-5" />
          Títulos das Aulas ({numberOfLessons} aulas)
        </CardTitle>
        <p className="text-sm text-gray-600">
          Personalize os títulos e descrições das aulas. Deixe o título padrão para usar "Aula X".
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        {templates.map((template) => (
          <div
            key={template.lessonNumber}
            className="p-4 border rounded-lg bg-gray-50 space-y-3"
          >
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2 min-w-0">
                <span className="flex-shrink-0 w-8 h-8 bg-[#667eea] text-white rounded-full flex items-center justify-center text-sm font-medium">
                  {template.lessonNumber}
                </span>
                <div className="flex-1 min-w-0">
                  <Input
                    value={template.title}
                    onChange={(e) => updateTemplate(template.lessonNumber, 'title', e.target.value)}
                    placeholder={`Aula ${template.lessonNumber}`}
                    className="bg-white"
                  />
                </div>
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => resetToDefault(template.lessonNumber)}
                className="flex-shrink-0"
              >
                Padrão
              </Button>
            </div>
            
            <Textarea
              value={template.description || ''}
              onChange={(e) => updateTemplate(template.lessonNumber, 'description', e.target.value)}
              placeholder="Descrição da aula (opcional)"
              className="bg-white"
              rows={2}
            />
          </div>
        ))}
        
        {templates.length > 0 && (
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Preview dos títulos:</h4>
            <div className="space-y-1">
              {templates.map((template) => {
                const displayTitle = (template.title && template.title.trim() !== '') 
                  ? template.title 
                  : `Aula ${template.lessonNumber}`;
                const isDefault = !template.title || template.title.trim() === '';
                
                return (
                  <div key={template.lessonNumber} className="text-sm text-blue-800">
                    <span className="font-medium">{displayTitle}</span>
                    {isDefault && (
                      <span className="text-blue-600 ml-1">(padrão)</span>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}