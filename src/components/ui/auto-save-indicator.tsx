import React from 'react';
import { CheckCircle, Loader2, Alert<PERSON>ir<PERSON>, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface AutoSaveIndicatorProps {
  isAutoSaving: boolean;
  lastSaved: Date | null;
  hasUnsavedChanges: boolean;
  error: string | null;
  className?: string;
  showTimestamp?: boolean;
}

export function AutoSaveIndicator({
  isAutoSaving,
  lastSaved,
  hasUnsavedChanges,
  error,
  className,
  showTimestamp = true
}: AutoSaveIndicatorProps) {
  const getStatus = () => {
    if (error) {
      return {
        icon: AlertCircle,
        text: 'Erro ao salvar',
        color: 'text-red-600',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200'
      };
    }

    if (isAutoSaving) {
      return {
        icon: Loader2,
        text: 'Salvando...',
        color: 'text-blue-600',
        bgColor: 'bg-blue-50',
        borderColor: 'border-blue-200',
        animate: 'animate-spin'
      };
    }

    if (hasUnsavedChanges) {
      return {
        icon: Clock,
        text: 'Alterações não salvas',
        color: 'text-orange-600',
        bgColor: 'bg-orange-50',
        borderColor: 'border-orange-200'
      };
    }

    if (lastSaved) {
      return {
        icon: CheckCircle,
        text: 'Salvo',
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200'
      };
    }

    return null;
  };

  const status = getStatus();
  if (!status) return null;

  const Icon = status.icon;
  const formatLastSaved = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) {
      return 'agora mesmo';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes} min atrás`;
    } else {
      return date.toLocaleTimeString('pt-BR', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    }
  };

  return (
    <div
      className={cn(
        'inline-flex items-center gap-2 px-3 py-1.5 rounded-full border text-sm font-medium',
        status.color,
        status.bgColor,
        status.borderColor,
        className
      )}
    >
      <Icon 
        className={cn('w-4 h-4', status.animate)} 
      />
      <span>{status.text}</span>
      {showTimestamp && lastSaved && !isAutoSaving && !hasUnsavedChanges && !error && (
        <span className="text-xs opacity-70">
          • {formatLastSaved(lastSaved)}
        </span>
      )}
    </div>
  );
}

export function AutoSaveInlineIndicator({
  isAutoSaving,
  lastSaved,
  hasUnsavedChanges,
  error,
  className
}: AutoSaveIndicatorProps) {
  const getStatus = () => {
    if (error) {
      return {
        icon: AlertCircle,
        text: 'Erro ao salvar',
        color: 'text-red-600'
      };
    }

    if (isAutoSaving) {
      return {
        icon: Loader2,
        text: 'Salvando...',
        color: 'text-blue-600',
        animate: 'animate-spin'
      };
    }

    if (hasUnsavedChanges) {
      return {
        icon: Clock,
        text: 'Não salvo',
        color: 'text-orange-600'
      };
    }

    if (lastSaved) {
      return {
        icon: CheckCircle,
        text: 'Salvo',
        color: 'text-green-600'
      };
    }

    return null;
  };

  const status = getStatus();
  if (!status) return null;

  const Icon = status.icon;

  return (
    <div className={cn('inline-flex items-center gap-1.5 text-xs', status.color, className)}>
      <Icon className={cn('w-3 h-3', status.animate)} />
      <span>{status.text}</span>
    </div>
  );
}