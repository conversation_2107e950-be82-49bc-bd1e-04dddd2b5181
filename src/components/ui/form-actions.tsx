import React from 'react';
import { ModernButton } from './modern-button';
import { Button } from './button';
import { LucideIcon } from 'lucide-react';

interface FormActionsProps {
  onCancel?: () => void;
  onSubmit?: () => void;
  cancelLabel?: string;
  submitLabel?: string;
  submitIcon?: LucideIcon;
  isLoading?: boolean;
  submitDisabled?: boolean;
  className?: string;
}

export function FormActions({ 
  onCancel,
  onSubmit,
  cancelLabel = "Cancelar",
  submitLabel = "Salvar",
  submitIcon,
  isLoading = false,
  submitDisabled = false,
  className = ""
}: FormActionsProps) {
  return (
    <div className={`flex justify-end space-x-4 pt-6 border-t border-gray-200 ${className}`}>
      {onCancel && (
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          className="border-2 border-gray-300 text-gray-700 hover:bg-gray-50 transition-all duration-300"
        >
          {cancelLabel}
        </Button>
      )}
      <ModernButton
        type="submit"
        onClick={onSubmit}
        icon={submitIcon}
        loading={isLoading}
        disabled={submitDisabled}
      >
        {submitLabel}
      </ModernButton>
    </div>
  );
}