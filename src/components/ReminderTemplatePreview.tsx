'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, Eye, RefreshCw, MessageSquare, Clock, User } from 'lucide-react';

interface ReminderTemplate {
  id: string;
  name: string;
  template: string;
  category?: string;
  description?: string;
}

interface PreviewData {
  success: boolean;
  message?: string;
  variables?: Record<string, string>;
  error?: string;
}

interface ReminderTemplatePreviewProps {
  templateId?: string;
  slotId?: string;
  className?: string;
  onTemplateChange?: (templateId: string) => void;
}

export function ReminderTemplatePreview({
  templateId,
  slotId,
  className,
  onTemplateChange
}: ReminderTemplatePreviewProps) {
  const [templates, setTemplates] = useState<ReminderTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<ReminderTemplate | null>(null);
  const [previewData, setPreviewData] = useState<PreviewData | null>(null);
  const [loading, setLoading] = useState(false);
  const [templatesLoading, setTemplatesLoading] = useState(true);

  // Load available templates
  useEffect(() => {
    loadTemplates();
  }, []);

  // Load template details when templateId changes
  useEffect(() => {
    if (templateId) {
      const template = templates.find(t => t.id === templateId);
      if (template) {
        setSelectedTemplate(template);
        if (slotId) {
          loadPreview(templateId, slotId);
        }
      }
    }
  }, [templateId, templates, slotId]);

  const loadTemplates = async () => {
    try {
      setTemplatesLoading(true);
      const response = await fetch('/api/reminder-templates?category=mentoria');
      if (response.ok) {
        const result = await response.json();
        setTemplates(result.data || []);
      } else {
        console.error('Failed to load reminder templates');
      }
    } catch (error) {
      console.error('Error loading reminder templates:', error);
    } finally {
      setTemplatesLoading(false);
    }
  };

  const loadPreview = async (templateId: string, slotId: string) => {
    try {
      setLoading(true);
      const response = await fetch(
        `/api/mentoring/reminders?action=preview&templateId=${templateId}&slotId=${slotId}`
      );
      
      if (response.ok) {
        const result = await response.json();
        setPreviewData(result);
      } else {
        const error = await response.json();
        setPreviewData({
          success: false,
          error: error.error || 'Failed to load preview'
        });
      }
    } catch (error) {
      console.error('Error loading preview:', error);
      setPreviewData({
        success: false,
        error: 'Failed to load preview'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleTemplateSelect = (template: ReminderTemplate) => {
    setSelectedTemplate(template);
    setPreviewData(null);
    
    if (onTemplateChange) {
      onTemplateChange(template.id);
    }
    
    if (slotId) {
      loadPreview(template.id, slotId);
    }
  };

  const handleRefreshPreview = () => {
    if (selectedTemplate && slotId) {
      loadPreview(selectedTemplate.id, slotId);
    }
  };

  if (templatesLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <MessageSquare className="w-5 h-5" />
            <CardTitle>Preview do Template</CardTitle>
          </div>
          <CardDescription>
            Visualize como ficará a mensagem de lembrete
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin mr-2" />
            <span className="text-sm text-gray-600">Carregando templates...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <MessageSquare className="w-5 h-5" />
            <CardTitle>Preview do Template</CardTitle>
          </div>
          {selectedTemplate && slotId && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefreshPreview}
              disabled={loading}
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Atualizar
            </Button>
          )}
        </div>
        <CardDescription>
          Visualize como ficará a mensagem de lembrete
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Template Selection */}
        {!templateId && (
          <div>
            <label className="text-sm font-medium mb-2 block">
              Selecionar Template:
            </label>
            <div className="grid gap-2 max-h-40 overflow-y-auto">
              {templates.map((template) => (
                <div
                  key={template.id}
                  className={`p-3 rounded border cursor-pointer transition-colors ${
                    selectedTemplate?.id === template.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handleTemplateSelect(template)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-sm">{template.name}</span>
                      {template.category && (
                        <Badge variant="secondary" className="text-xs">
                          {template.category}
                        </Badge>
                      )}
                    </div>
                    <Eye className="w-4 h-4 text-gray-400" />
                  </div>
                  {template.description && (
                    <p className="text-xs text-gray-600 mt-1">
                      {template.description}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Selected Template Info */}
        {selectedTemplate && (
          <div className="border rounded p-3 bg-gray-50">
            <div className="flex items-center space-x-2 mb-2">
              <span className="font-medium text-sm">{selectedTemplate.name}</span>
              {selectedTemplate.category && (
                <Badge variant="secondary" className="text-xs">
                  {selectedTemplate.category}
                </Badge>
              )}
            </div>
            {selectedTemplate.description && (
              <p className="text-xs text-gray-600 mb-2">
                {selectedTemplate.description}
              </p>
            )}
            <div className="text-xs text-gray-500">
              <strong>Template:</strong> {selectedTemplate.template}
            </div>
          </div>
        )}

        {/* Preview Section */}
        {selectedTemplate && (
          <div>
            <label className="text-sm font-medium mb-2 block flex items-center">
              <Eye className="w-4 h-4 mr-2" />
              Preview da Mensagem:
            </label>
            
            {!slotId ? (
              <div className="border rounded p-4 bg-yellow-50 border-yellow-200">
                <div className="flex items-center space-x-2 text-yellow-700">
                  <Clock className="w-4 h-4" />
                  <span className="text-sm">
                    Selecione uma agenda de mentoria para visualizar o preview
                  </span>
                </div>
              </div>
            ) : loading ? (
              <div className="border rounded p-4 flex items-center justify-center">
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
                <span className="text-sm text-gray-600">Gerando preview...</span>
              </div>
            ) : previewData ? (
              <div className={`border rounded p-4 ${
                previewData.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
              }`}>
                {previewData.success ? (
                  <div>
                    <div className="flex items-center space-x-2 mb-3">
                      <MessageSquare className="w-4 h-4 text-green-600" />
                      <span className="text-sm font-medium text-green-700">
                        Mensagem de Preview:
                      </span>
                    </div>
                    <div className="bg-white border rounded p-3 text-sm">
                      {previewData.message}
                    </div>
                    
                    {previewData.variables && Object.keys(previewData.variables).length > 0 && (
                      <div className="mt-3">
                        <span className="text-xs font-medium text-green-700 mb-2 block">
                          Variáveis utilizadas:
                        </span>
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          {Object.entries(previewData.variables).map(([key, value]) => (
                            <div key={key} className="flex justify-between">
                              <span className="text-gray-600">{`{${key}}`}</span>
                              <span className="font-medium">{value}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center space-x-2 text-red-700">
                    <span className="text-sm">❌ {previewData.error}</span>
                  </div>
                )}
              </div>
            ) : null}
          </div>
        )}

        {/* Template Variables Help */}
        {selectedTemplate && (
          <div className="border rounded p-3 bg-blue-50 border-blue-200">
            <div className="flex items-center space-x-2 mb-2">
              <User className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-700">
                Variáveis Disponíveis para Mentoria:
              </span>
            </div>
            <div className="text-xs text-blue-600 space-y-1">
              <div>• <code>{'{nome_do_aluno}'}</code> - Nome do aluno</div>
              <div>• <code>{'{nome_professor}'}</code> - Nome do professor</div>
              <div>• <code>{'{data_mentoria}'}</code> - Data da mentoria</div>
              <div>• <code>{'{hora_inicio_mentoria}'}</code> - Hora de início</div>
              <div>• <code>{'{hora_fim_mentoria}'}</code> - Hora de fim</div>
              <div>• <code>{'{duracao_mentoria}'}</code> - Duração em minutos</div>
              <div>• <code>{'{dia_semana_mentoria}'}</code> - Dia da semana</div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}