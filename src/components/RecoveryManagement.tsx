'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { RefreshCwIcon, CleanerIcon, BarChart3Icon } from 'lucide-react';

interface RecoveryStats {
  total: number;
  pending: number;
  sent: number;
  failed: number;
  successRate: string;
}

export default function RecoveryManagement() {
  const [stats, setStats] = useState<RecoveryStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isCleaning, setIsCleaning] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/recovery/process');
      
      if (!response.ok) {
        throw new Error('Failed to load stats');
      }

      const data = await response.json();
      setStats(data.data);
    } catch (error) {
      console.error('Error loading recovery stats:', error);
      setMessage({
        type: 'error',
        text: 'Erro ao carregar estatísticas de reposição'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const processReminders = async () => {
    try {
      setIsProcessing(true);
      setMessage(null);

      const response = await fetch('/api/recovery/process', {
        method: 'POST'
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to process reminders');
      }

      setMessage({
        type: 'success',
        text: data.message
      });

      // Reload stats
      await loadStats();
    } catch (error) {
      console.error('Error processing reminders:', error);
      setMessage({
        type: 'error',
        text: error instanceof Error ? error.message : 'Erro ao processar lembretes'
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const cleanupOldData = async () => {
    try {
      setIsCleaning(true);
      setMessage(null);

      const response = await fetch('/api/recovery/cleanup', {
        method: 'POST'
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to cleanup data');
      }

      setMessage({
        type: 'success',
        text: data.message
      });

      // Reload stats
      await loadStats();
    } catch (error) {
      console.error('Error cleaning up data:', error);
      setMessage({
        type: 'error',
        text: error instanceof Error ? error.message : 'Erro ao limpar dados antigos'
      });
    } finally {
      setIsCleaning(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Gerenciamento de Reposições
          </h2>
          <p className="text-gray-600">
            Controle e monitore o sistema de lembretes de reposição de aulas.
          </p>
        </div>
        <Button
          onClick={loadStats}
          disabled={isLoading}
          variant="outline"
        >
          <RefreshCwIcon className="h-4 w-4 mr-2" />
          Atualizar
        </Button>
      </div>

      {/* Statistics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                Total de Lembretes
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                Pendentes
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{stats.pending}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                Enviados
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.sent}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                Taxa de Sucesso
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.successRate}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <RefreshCwIcon className="h-5 w-5" />
              Processar Lembretes
            </CardTitle>
            <CardDescription>
              Execute manualmente o processamento de lembretes pendentes.
              Normalmente isso é feito automaticamente pelo sistema.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={processReminders}
              disabled={isProcessing}
              className="w-full"
            >
              {isProcessing ? 'Processando...' : 'Processar Lembretes Pendentes'}
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CleanerIcon className="h-5 w-5" />
              Limpeza de Dados
            </CardTitle>
            <CardDescription>
              Remove lembretes antigos e tokens OTP expirados para manter
              a base de dados limpa.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={cleanupOldData}
              disabled={isCleaning}
              variant="outline"
              className="w-full"
            >
              {isCleaning ? 'Limpando...' : 'Limpar Dados Antigos'}
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Status Messages */}
      {message && (
        <Alert>
          <AlertDescription className={message.type === 'error' ? 'text-red-700' : 'text-green-700'}>
            {message.text}
          </AlertDescription>
        </Alert>
      )}

      {/* System Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3Icon className="h-5 w-5" />
            Informações do Sistema
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Como Funciona</h4>
              <ul className="space-y-1 text-gray-600">
                <li>• Lembretes são criados automaticamente para faltas</li>
                <li>• Enviados às 9h do dia seguinte à ausência</li>
                <li>• Estudantes recebem link com validade de 24h</li>
                <li>• Link permite escolher aula de reposição</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Requisitos</h4>
              <ul className="space-y-1 text-gray-600">
                <li>• Curso deve permitir reposições</li>
                <li>• Estudante deve ter telefone cadastrado</li>
                <li>• WhatsApp deve estar configurado</li>
                <li>• Deve haver aulas futuras disponíveis</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}