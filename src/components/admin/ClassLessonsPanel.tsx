"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Clock, Plus, Calendar, Users, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { useConfirmationDialog } from '@/components/ui/confirmation-dialog';

interface Lesson {
  id: string;
  title: string;
  scheduledDate: string;
  duration: number;
  status: 'scheduled' | 'completed' | 'cancelled';
  attendanceCount: number;
  totalStudents: number;
}

interface ClassLessonsPanelProps {
  classId: string;
  courseId: string;
  numberOfLessons?: number;
  className?: string;
}

export function ClassLessonsPanel({ classId, courseId, numberOfLessons, className }: ClassLessonsPanelProps) {
  const [lessons, setLessons] = useState<Lesson[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const { showConfirmation, ConfirmationDialog } = useConfirmationDialog();

  useEffect(() => {
    if (classId) {
      fetchLessons();
    }
  }, [classId]);

  const fetchLessons = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        router.push('/login');
        return;
      }

      const response = await fetch(`/api/classes/${classId}/lessons`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch lessons');
      }

      const data = await response.json();
      setLessons(data);
    } catch (err) {
      toast.error('Erro ao carregar aulas');
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateLessons = async (force: boolean = false) => {
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        toast.error('Sessão expirada. Faça login novamente.');
        router.push('/login');
        return;
      }

      const response = await fetch(`/api/classes/${classId}/generate-lessons`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ force })
      });

      if (!response.ok) {
        const error = await response.json();

        // If lessons already exist and force is false, show confirmation dialog
        if (error.error?.includes('já possui aulas') && !force) {
          showConfirmation({
            title: 'Regenerar Aulas',
            description: `Esta turma já possui ${error.existingLessons || lessons.length} aulas. Deseja regenerar todas as aulas? Esta ação irá substituir as aulas existentes.`,
            confirmText: 'Regenerar Aulas',
            cancelText: 'Cancelar',
            variant: 'default',
            icon: 'warning',
            onConfirm: () => handleGenerateLessons(true)
          });
          return;
        }

        throw new Error(error.error || 'Falha ao gerar aulas');
      }

      const result = await response.json();
      toast.success(result.message || 'Aulas geradas com sucesso!');
      fetchLessons();
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Erro ao gerar aulas');
    }
  };

  const handleViewLesson = (lessonId: string) => {
    router.push(`/admin/attendance/${lessonId}`);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'scheduled': return 'Agendada';
      case 'completed': return 'Concluída';
      case 'cancelled': return 'Cancelada';
      default: return status;
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            Aulas da Turma
          </CardTitle>
          <CardDescription>Carregando aulas...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-3">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              Aulas da Turma
            </CardTitle>
            <CardDescription>
              {lessons.length} de {numberOfLessons || 0} aulas programadas
            </CardDescription>
          </div>
          {numberOfLessons && numberOfLessons > 0 && (
            <Button onClick={() => handleGenerateLessons()} size="sm">
              {lessons.length > 0 ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Gerar Aulas Novamente
                </>
              ) : (
                <>
                  <Plus className="w-4 h-4 mr-2" />
                  Gerar Aulas
                </>
              )}
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {lessons.length === 0 ? (
          <div className="text-center py-8">
            <Clock className="w-12 h-12 mx-auto mb-3 text-gray-400" />
            <p className="font-medium">Nenhuma aula agendada</p>
            <p className="text-sm text-gray-500">
              {numberOfLessons && numberOfLessons > 0 
                ? "Clique em 'Gerar Aulas' para criar as aulas programadas."
                : "Configure o número de aulas no curso para gerar aulas."
              }
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {lessons.map((lesson) => (
              <div 
                key={lesson.id} 
                className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                onClick={() => handleViewLesson(lesson.id)}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">{lesson.title}</p>
                    <p className="text-sm text-gray-600">
                      {formatDate(lesson.scheduledDate)} • {lesson.duration} minutos
                    </p>
                  </div>
                  <div className="flex items-center gap-3">
                    <Badge className={getStatusColor(lesson.status)}>
                      {getStatusText(lesson.status)}
                    </Badge>
                    <div className="flex items-center gap-1 text-sm text-gray-600">
                      <Users className="w-4 h-4" />
                      <span>{lesson.attendanceCount}/{lesson.totalStudents}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
      <ConfirmationDialog />
    </Card>
  );
}