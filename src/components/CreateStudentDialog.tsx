'use client';

import React, { useState, useCallback } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, UserPlus } from 'lucide-react';
import { toast } from 'sonner';
import { useAutoSave } from '@/hooks/useAutoSave';
import { AutoSaveInlineIndicator } from '@/components/ui/auto-save-indicator';

interface StudentFormData {
  name: string;
  email: string;
  phone: string;
  birthDate: string;
  notes: string;
}

interface Student {
  id: string;
  name: string;
  email: string | null;
  phone: string | null;
  birthDate: string | null;
  notes: string | null;
  status: string;
}

interface CreateStudentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onStudentCreated: (student: Student) => void;
}

export function CreateStudentDialog({
  open,
  onOpenChange,
  onStudentCreated
}: CreateStudentDialogProps) {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<StudentFormData>({
    name: '',
    email: '',
    phone: '',
    birthDate: '',
    notes: ''
  });

  // Auto-save functionality (save as draft)
  const autoSaveDraft = useCallback(async (data: StudentFormData) => {
    if (!data.name.trim()) {
      return false;
    }

    try {
      // Save to localStorage as draft
      const draftKey = 'student_draft';
      localStorage.setItem(draftKey, JSON.stringify({
        ...data,
        timestamp: new Date().toISOString()
      }));
      return true;
    } catch (err) {
      console.error('Auto-save draft error:', err);
      return false;
    }
  }, []);

  const autoSaveState = useAutoSave({
    data: formData,
    onSave: autoSaveDraft,
    config: {
      debounceMs: 2000,
      enableToast: false
    },
    isValid: (data) => !!data.name.trim(),
    enabled: open
  });

  const handleInputChange = (field: keyof StudentFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('Nome é obrigatório');
      return;
    }

    if (formData.name.trim().length < 2) {
      toast.error('Nome deve ter pelo menos 2 caracteres');
      return;
    }

    // Validate email format if provided
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      toast.error('Email inválido');
      return;
    }

    try {
      setLoading(true);

      const token = localStorage.getItem('auth_token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch('/api/students', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          name: formData.name.trim(),
          email: formData.email.trim() || null,
          phone: formData.phone.trim() || null,
          birthDate: formData.birthDate || null,
          notes: formData.notes.trim() || null
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao criar aluno');
      }

      // Clear draft on successful creation
      localStorage.removeItem('student_draft');
      toast.success('Aluno criado com sucesso!');
      onStudentCreated(result.data);
      handleClose();
    } catch (error) {
      console.error('Error creating student:', error);
      toast.error(error instanceof Error ? error.message : 'Erro ao criar aluno');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    // Clear draft when closing
    localStorage.removeItem('student_draft');
    setFormData({
      name: '',
      email: '',
      phone: '',
      birthDate: '',
      notes: ''
    });
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="w-full max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="flex items-center gap-2">
                <UserPlus className="w-5 h-5" />
                Criar Novo Aluno
              </DialogTitle>
              <DialogDescription>
                Preencha as informações do novo aluno
              </DialogDescription>
            </div>
            <AutoSaveInlineIndicator
              isAutoSaving={autoSaveState.isAutoSaving}
              lastSaved={autoSaveState.lastSaved}
              hasUnsavedChanges={autoSaveState.hasUnsavedChanges}
              error={autoSaveState.error}
            />
          </div>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Nome Completo *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Nome completo do aluno"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder="<EMAIL>"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone">Telefone</Label>
            <Input
              id="phone"
              type="tel"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              placeholder="(11) 99999-9999"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="birthDate">Data de Nascimento</Label>
            <Input
              id="birthDate"
              type="date"
              value={formData.birthDate}
              onChange={(e) => handleInputChange('birthDate', e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Observações</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="Observações sobre o aluno..."
              rows={3}
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancelar
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Criando...
                </>
              ) : (
                'Criar Aluno'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
