'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  adminOnly?: boolean;
}

export function ProtectedRoute({ children, adminOnly = false }: ProtectedRouteProps) {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading) {
      if (!user) {
        // User not authenticated, redirect to login
        router.push('/login?message=Já%20faz%20um%20tempo%20que%20você%20fez%20login.%20Por%20segurança,%20faça%20login%20novamente.');
        return;
      }

      if (adminOnly && user.profile?.role !== 'admin' && user.profile?.role !== 'super_admin') {
        // User doesn't have admin privileges
        router.push('/login?message=Acesso%20negado.%20Você%20não%20tem%20permissão%20para%20acessar%20esta%20página.');
        return;
      }
    }
  }, [user, loading, router, adminOnly]);

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  // Don't render children if user is not authenticated or doesn't have required permissions
  if (!user || (adminOnly && user.profile?.role !== 'admin' && user.profile?.role !== 'super_admin')) {
    return null;
  }

  return <>{children}</>;
}