'use client';

import { usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { DashboardLayout } from './dashboard-layout';

interface AppLayoutProps {
  children: React.ReactNode;
}

export function AppLayout({ children }: AppLayoutProps) {
  const pathname = usePathname();
  const { user, loading } = useAuth();

  // Routes that should NOT have navigation
  const publicRoutes = [
    '/login',
    '/auth/verify',
    '/auth/whatsapp'
  ];

  // Check if current route should have navigation
  const shouldShowNavigation = !publicRoutes.includes(pathname) && !loading && user;

  // If user is authenticated and not on a public route, wrap with navigation
  if (shouldShowNavigation) {
    // Extract page title from pathname for the header
    const getPageTitle = () => {
      if (pathname === '/') return 'Dashboard';
      if (pathname.startsWith('/admin/students')) return 'Gerenciar Alunos';
      if (pathname.startsWith('/admin/courses')) return 'Gerenciar Cursos';
      if (pathname.startsWith('/admin/classes')) return 'Gerenciar Turmas';
      if (pathname.startsWith('/admin/reminder-templates')) return 'Templates de Mensagem';
      if (pathname.startsWith('/admin/attendance')) return 'Controle de Presença';
      if (pathname.startsWith('/admin/reports')) return 'Relatórios';
      if (pathname.startsWith('/admin/settings')) return 'Configurações';
      if (pathname.startsWith('/admin/whatsapp')) return 'WhatsApp';
      if (pathname.startsWith('/admin/security')) return 'Segurança';
      if (pathname.startsWith('/admin/enrollments')) return 'Matricular Aluno';
      return 'VoxStudent';
    };

    return (
      <DashboardLayout title={getPageTitle()}>
        {children}
      </DashboardLayout>
    );
  }

  // For public routes or when not authenticated, render without navigation
  return <>{children}</>;
}