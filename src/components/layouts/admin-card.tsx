import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { LucideIcon } from 'lucide-react';

interface AdminCardProps {
  title: string;
  description?: string;
  icon?: LucideIcon;
  iconColor?: string;
  children: React.ReactNode;
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
  headerAction?: React.ReactNode;
}

export function AdminCard({ 
  title, 
  description, 
  icon: Icon, 
  iconColor = "from-[#667eea] to-[#764ba2]",
  children, 
  className = '',
  headerClassName = '',
  contentClassName = '',
  headerAction
}: AdminCardProps) {
  return (
    <Card className={`shadow-xl border-0 ${className}`}>
      <CardHeader className={`bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg ${headerClassName}`}>
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <CardTitle className="text-xl font-bold text-gray-900 flex items-center">
              {Icon && (
                <div className={`w-8 h-8 bg-gradient-to-br ${iconColor} rounded-lg flex items-center justify-center mr-3`}>
                  <Icon className="w-4 h-4 text-white" />
                </div>
              )}
              {title}
            </CardTitle>
            {description && (
              <CardDescription className="text-lg">
                {description}
              </CardDescription>
            )}
          </div>
          {headerAction && (
            <div className="flex-shrink-0 ml-4">
              {headerAction}
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className={contentClassName}>
        {children}
      </CardContent>
    </Card>
  );
}