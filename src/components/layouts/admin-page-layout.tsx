import React from 'react';
import { LucideIcon } from 'lucide-react';
import { AdminPageHeader } from './admin-page-header';

interface AdminPageLayoutProps {
  title: string;
  description: string;
  icon: LucideIcon;
  children: React.ReactNode;
  backUrl?: string;
  actionButton?: {
    label: string;
    onClick: () => void;
    icon?: LucideIcon;
  };
  className?: string;
}

export function AdminPageLayout({ 
  title, 
  description, 
  icon, 
  children, 
  backUrl = '/',
  actionButton,
  className = ''
}: AdminPageLayoutProps) {
  return (
    <div className={`min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4 ${className}`}>
      <div className="w-full max-w-7xl mx-auto">
        <AdminPageHeader
          title={title}
          description={description}
          icon={icon}
          backUrl={backUrl}
          actionButton={actionButton}
        />
        <div className="space-y-6">
          {children}
        </div>
      </div>
    </div>
  );
}