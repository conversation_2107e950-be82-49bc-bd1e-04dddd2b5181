import { prisma } from '@/lib/prisma';

export interface Teacher {
  id: string;
  name: string;
  email: string | null;
  phone: string | null;
  specialization: string | null;
  status: string;
  notes: string | null;
  createdAt: string;
  updatedAt: string;
  _count?: {
    classes: number;
    lessons: number;
  };
}

export interface CreateTeacherData {
  name: string;
  email?: string;
  phone?: string;
  specialization?: string;
  status?: string;
  notes?: string;
}

export interface UpdateTeacherData {
  name?: string;
  email?: string;
  phone?: string;
  specialization?: string;
  status?: string;
  notes?: string;
}

export interface ServiceResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

class TeachersServerService {
  // Get all teachers with optional search
  async getTeachers(search?: string): Promise<ServiceResponse<Teacher[]>> {
    try {
      const where = search
        ? {
            OR: [
              { name: { contains: search, mode: 'insensitive' as const } },
              { email: { contains: search, mode: 'insensitive' as const } },
              { phone: { contains: search, mode: 'insensitive' as const } },
              { specialization: { contains: search, mode: 'insensitive' as const } }
            ]
          }
        : {};

      const teachers = await prisma.teacher.findMany({
        where,
        include: {
          _count: {
            select: {
              classes: true,
              lessons: true
            }
          }
        },
        orderBy: {
          name: 'asc'
        }
      });

      return {
        success: true,
        data: teachers.map(teacher => ({
          ...teacher,
          createdAt: teacher.createdAt.toISOString(),
          updatedAt: teacher.updatedAt.toISOString()
        }))
      };
    } catch (error) {
      console.error('Error fetching teachers:', error);
      return {
        success: false,
        error: 'Failed to fetch teachers'
      };
    }
  }

  // Get teacher by ID
  async getTeacher(id: string): Promise<ServiceResponse<Teacher>> {
    try {
      const teacher = await prisma.teacher.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              classes: true,
              lessons: true
            }
          }
        }
      });

      if (!teacher) {
        return {
          success: false,
          error: 'Teacher not found'
        };
      }

      return {
        success: true,
        data: {
          ...teacher,
          createdAt: teacher.createdAt.toISOString(),
          updatedAt: teacher.updatedAt.toISOString()
        }
      };
    } catch (error) {
      console.error('Error fetching teacher:', error);
      return {
        success: false,
        error: 'Failed to fetch teacher'
      };
    }
  }

  // Create new teacher
  async createTeacher(data: CreateTeacherData): Promise<ServiceResponse<Teacher>> {
    try {
      // Validate required fields
      if (!data.name?.trim()) {
        return {
          success: false,
          error: 'Name is required'
        };
      }

      // Check if email already exists (if provided)
      if (data.email) {
        const existingTeacher = await prisma.teacher.findFirst({
          where: { email: data.email }
        });

        if (existingTeacher) {
          return {
            success: false,
            error: 'A teacher with this email already exists'
          };
        }
      }

      const teacher = await prisma.teacher.create({
        data: {
          name: data.name.trim(),
          email: data.email?.trim() || null,
          phone: data.phone?.trim() || null,
          specialization: data.specialization?.trim() || null,
          status: data.status || 'active',
          notes: data.notes?.trim() || null
        },
        include: {
          _count: {
            select: {
              classes: true,
              lessons: true
            }
          }
        }
      });

      return {
        success: true,
        data: {
          ...teacher,
          createdAt: teacher.createdAt.toISOString(),
          updatedAt: teacher.updatedAt.toISOString()
        }
      };
    } catch (error) {
      console.error('Error creating teacher:', error);
      return {
        success: false,
        error: 'Failed to create teacher'
      };
    }
  }

  // Update teacher
  async updateTeacher(id: string, data: UpdateTeacherData): Promise<ServiceResponse<Teacher>> {
    try {
      // Check if teacher exists
      const existingTeacher = await prisma.teacher.findUnique({
        where: { id }
      });

      if (!existingTeacher) {
        return {
          success: false,
          error: 'Teacher not found'
        };
      }

      // Check if email already exists (if provided and different from current)
      if (data.email && data.email !== existingTeacher.email) {
        const teacherWithEmail = await prisma.teacher.findFirst({
          where: { 
            email: data.email,
            id: { not: id }
          }
        });

        if (teacherWithEmail) {
          return {
            success: false,
            error: 'A teacher with this email already exists'
          };
        }
      }

      const updateData: Partial<UpdateTeacherData> = {};
      if (data.name !== undefined) updateData.name = data.name.trim();
      if (data.email !== undefined) updateData.email = data.email?.trim() || null;
      if (data.phone !== undefined) updateData.phone = data.phone?.trim() || null;
      if (data.specialization !== undefined) updateData.specialization = data.specialization?.trim() || null;
      if (data.status !== undefined) updateData.status = data.status;
      if (data.notes !== undefined) updateData.notes = data.notes?.trim() || null;

      const teacher = await prisma.teacher.update({
        where: { id },
        data: updateData,
        include: {
          _count: {
            select: {
              classes: true,
              lessons: true
            }
          }
        }
      });

      return {
        success: true,
        data: {
          ...teacher,
          createdAt: teacher.createdAt.toISOString(),
          updatedAt: teacher.updatedAt.toISOString()
        }
      };
    } catch (error) {
      console.error('Error updating teacher:', error);
      return {
        success: false,
        error: 'Failed to update teacher'
      };
    }
  }

  // Delete teacher
  async deleteTeacher(id: string): Promise<ServiceResponse<boolean>> {
    try {
      // Check if teacher exists
      const existingTeacher = await prisma.teacher.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              classes: true,
              lessons: true
            }
          }
        }
      });

      if (!existingTeacher) {
        return {
          success: false,
          error: 'Teacher not found'
        };
      }

      // Check if teacher has active assignments
      if (existingTeacher._count.classes > 0 || existingTeacher._count.lessons > 0) {
        return {
          success: false,
          error: 'Cannot delete teacher with active class or lesson assignments. Please reassign or remove assignments first.'
        };
      }

      await prisma.teacher.delete({
        where: { id }
      });

      return {
        success: true,
        data: true
      };
    } catch (error) {
      console.error('Error deleting teacher:', error);
      return {
        success: false,
        error: 'Failed to delete teacher'
      };
    }
  }

  // Get teacher statistics
  async getTeacherStats(): Promise<ServiceResponse<{
    total: number;
    active: number;
    inactive: number;
    withClasses: number;
    withoutClasses: number;
  }>> {
    try {
      const [total, active, inactive, withClasses] = await Promise.all([
        prisma.teacher.count(),
        prisma.teacher.count({ where: { status: 'active' } }),
        prisma.teacher.count({ where: { status: 'inactive' } }),
        prisma.teacher.count({
          where: {
            classes: {
              some: {
                isActive: true
              }
            }
          }
        })
      ]);

      return {
        success: true,
        data: {
          total,
          active,
          inactive,
          withClasses,
          withoutClasses: active - withClasses
        }
      };
    } catch (error) {
      console.error('Error fetching teacher stats:', error);
      return {
        success: false,
        error: 'Failed to fetch teacher statistics'
      };
    }
  }
}

export const teachersServerService = new TeachersServerService();