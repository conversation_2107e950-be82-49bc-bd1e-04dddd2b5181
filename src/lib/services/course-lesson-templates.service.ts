import { prisma } from '@/lib/prisma';

export interface LessonTemplate {
  id?: string;
  lessonNumber: number;
  title: string;
  description?: string;
}

export interface CourseWithTemplates {
  id: string;
  name: string;
  numberOfLessons: number | null;
  lessonTemplates: LessonTemplate[];
}

export class CourseLessonTemplatesService {
  /**
   * Get course with its lesson templates
   */
  static async getCourseWithTemplates(courseId: string): Promise<CourseWithTemplates | null> {
    const course = await prisma.course.findUnique({
      where: { id: courseId },
      include: {
        lessonTemplates: {
          orderBy: { lessonNumber: 'asc' }
        }
      }
    });

    if (!course) return null;

    return {
      id: course.id,
      name: course.name,
      numberOfLessons: course.numberOfLessons,
      lessonTemplates: course.lessonTemplates.map(template => ({
        id: template.id,
        lessonNumber: template.lessonNumber,
        title: template.title,
        description: template.description || undefined
      }))
    };
  }

  /**
   * Update lesson templates for a course
   */
  static async updateCourseTemplates(
    courseId: string, 
    templates: LessonTemplate[]
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Get current course
      const course = await prisma.course.findUnique({
        where: { id: courseId },
        select: { numberOfLessons: true }
      });

      if (!course) {
        return { success: false, error: 'Curso não encontrado' };
      }

      // Validate templates don't exceed numberOfLessons
      if (course.numberOfLessons) {
        const invalidTemplates = templates.filter(t => t.lessonNumber > course.numberOfLessons!);
        if (invalidTemplates.length > 0) {
          return { 
            success: false, 
            error: `Existem templates para aulas além do número definido (${course.numberOfLessons})` 
          };
        }
      }

      // Delete existing templates for this course
      await prisma.courseLessonTemplate.deleteMany({
        where: { courseId }
      });

      // Create new templates
      if (templates.length > 0) {
        await prisma.courseLessonTemplate.createMany({
          data: templates.map(template => ({
            courseId,
            lessonNumber: template.lessonNumber,
            title: template.title,
            description: template.description || null
          }))
        });
      }

      return { success: true };

    } catch (error) {
      console.error('Error updating course templates:', error);
      return { 
        success: false, 
        error: 'Erro interno do servidor ao atualizar templates' 
      };
    }
  }

  /**
   * Generate default templates for a course
   */
  static async generateDefaultTemplates(
    courseId: string, 
    numberOfLessons: number
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Delete existing templates
      await prisma.courseLessonTemplate.deleteMany({
        where: { courseId }
      });

      // Create default templates
      const templates = [];
      for (let i = 1; i <= numberOfLessons; i++) {
        templates.push({
          courseId,
          lessonNumber: i,
          title: '', // Título em branco por padrão para evitar "Aula X: Aula X"
          description: null
        });
      }

      if (templates.length > 0) {
        await prisma.courseLessonTemplate.createMany({
          data: templates
        });
      }

      return { success: true };

    } catch (error) {
      console.error('Error generating default templates:', error);
      return { 
        success: false, 
        error: 'Erro interno do servidor ao gerar templates padrão' 
      };
    }
  }

  /**
   * Get lesson title for a specific lesson number, with fallback to default
   */
  static async getLessonTitle(courseId: string, lessonNumber: number): Promise<string> {
    try {
      const template = await prisma.courseLessonTemplate.findUnique({
        where: {
          courseId_lessonNumber: {
            courseId,
            lessonNumber
          }
        }
      });

      // Se tem template e título não está vazio, usa o título customizado
      // Senão, usa o padrão "Aula X"
      return (template?.title && template.title.trim() !== '') ? template.title : `Aula ${lessonNumber}`;
    } catch (error) {
      console.error('Error getting lesson title:', error);
      return `Aula ${lessonNumber}`;
    }
  }

  /**
   * Apply templates to existing lessons
   */
  static async applyTemplatesToLessons(courseId: string): Promise<{ success: boolean; error?: string; updated: number }> {
    try {
      // Get all templates for this course
      const templates = await prisma.courseLessonTemplate.findMany({
        where: { courseId },
        orderBy: { lessonNumber: 'asc' }
      });

      if (templates.length === 0) {
        return { success: true, updated: 0 };
      }

      // Get all classes for this course
      const classes = await prisma.class.findMany({
        where: { courseId },
        include: {
          lessons: {
            orderBy: { lessonNumber: 'asc' }
          }
        }
      });

      let totalUpdated = 0;

      // Update lessons for each class
      for (const classData of classes) {
        for (const lesson of classData.lessons) {
          if (lesson.lessonNumber) {
            const template = templates.find(t => t.lessonNumber === lesson.lessonNumber);
            if (template) {
              await prisma.lesson.update({
                where: { id: lesson.id },
                data: {
                  title: template.title,
                  description: template.description
                }
              });
              totalUpdated++;
            }
          }
        }
      }

      return { success: true, updated: totalUpdated };

    } catch (error) {
      console.error('Error applying templates to lessons:', error);
      return { 
        success: false, 
        error: 'Erro interno do servidor ao aplicar templates',
        updated: 0
      };
    }
  }
}