interface DashboardStats {
  totalStudents: number;
  newStudentsThisMonth: number;
  activeClasses: number;
  averageAttendanceRate: number;
  upcomingLessons: number;
  scheduledMentoringSessions: number;
  mentoringCompletionPercentage: number;
  pendingMentoringSessions: number;
  totalAvailableMentoringSessions: number;
}

interface ClassPerformance {
  name: string;
  attendance: number;
  students: number;
  color: string;
}

interface UpcomingLesson {
  time: string;
  class: string;
  students: number;
}

interface DashboardData {
  stats: DashboardStats;
  classPerformance: ClassPerformance[];
  upcomingLessons: UpcomingLesson[];
  recentActivity: Array<{
    action: string;
    user: string;
    time: string;
    icon: string;
    color: string;
  }>;
}

class DashboardService {
  private async fetchWithAuth(url: string) {
    const token = localStorage.getItem('auth_token');
    const headers: HeadersInit = {
      'Content-Type': 'application/json'
    };
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    const response = await fetch(url, {
      credentials: 'include',
      headers
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return response.json();
  }

  async getDashboardData(): Promise<DashboardData> {
    try {
      const response = await this.fetchWithAuth('/api/dashboard');
      return response.data;
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      
      // Return fallback data in case of error
      return {
        stats: {
          totalStudents: 0,
          newStudentsThisMonth: 0,
          activeClasses: 0,
          averageAttendanceRate: 0,
          upcomingLessons: 0,
          scheduledMentoringSessions: 0,
          mentoringCompletionPercentage: 0,
          pendingMentoringSessions: 0,
          totalAvailableMentoringSessions: 0
        },
        classPerformance: [],
        upcomingLessons: [],
        recentActivity: []
      };
    }
  }
}

const dashboardService = new DashboardService();
export default dashboardService;
export { dashboardService };
export type { DashboardData, DashboardStats, ClassPerformance, UpcomingLesson };