/**
 * Admin Configuration Service
 * Manages super admin users defined in environment variables with tuple format [email,phone]
 */

export interface SuperAdminTuple {
  email: string;
  phone: string;
}

class AdminConfigService {
  private static instance: AdminConfigService;
  private superAdminTuples: SuperAdminTuple[] = [];
  private initialized = false;

  private constructor() {}

  static getInstance(): AdminConfigService {
    if (!AdminConfigService.instance) {
      AdminConfigService.instance = new AdminConfigService();
    }
    return AdminConfigService.instance;
  }

  /**
   * Initialize admin configuration from environment variables
   * Should be called on application startup
   */
  initialize(): void {
    if (this.initialized) return;

    this.superAdminTuples = [];

    // Parse super admin tuples
    const superAdminTuples = this.parseTuples(process.env.SUPER_ADMIN_TUPLES);
    this.superAdminTuples.push(...superAdminTuples);

    this.initialized = true;
    // Only log count in development, no sensitive details
    // if (process.env.NODE_ENV === 'development') {
    //   console.log(`Server: ✅ Super admin configuration initialized with ${this.superAdminTuples.length} super admin users`);
    // }
  }

  /**
   * Parse tuple string format: [email,phone],[email,phone]
   */
  private parseTuples(tuplesString?: string): SuperAdminTuple[] {
    if (!tuplesString?.trim()) return [];

    const tuples: SuperAdminTuple[] = [];
    
    try {
      // Match all [email,phone] patterns
      const matches = tuplesString.match(/\[([^,\]]+),([^\]]+)\]/g);
      
      if (matches) {
        matches.forEach(match => {
          const content = match.slice(1, -1); // Remove [ and ]
          const [email, phone] = content.split(',').map(s => s.trim());
          
          if (email && phone) {
            tuples.push({
              email: email.toLowerCase(),
              phone: phone
            });
          }
        });
      }
    } catch (error) {
      console.error('Error parsing super admin tuples:', error);
    }

    return tuples;
  }

  /**
   * Get all super admin tuples
   */
  getAllSuperAdminTuples(): SuperAdminTuple[] {
    if (!this.initialized) this.initialize();
    return [...this.superAdminTuples];
  }

  /**
   * Check if an email is configured as super admin
   */
  isSuperAdminEmail(email: string): boolean {
    if (!this.initialized) this.initialize();
    return this.superAdminTuples.some(tuple => tuple.email === email.toLowerCase());
  }

  /**
   * Check if a phone number is configured as super admin
   */
  isSuperAdminPhone(phoneNumber: string): boolean {
    if (!this.initialized) this.initialize();
    const normalizedPhone = phoneNumber.replace(/\D/g, '');
    return this.superAdminTuples.some(tuple => 
      tuple.phone.replace(/\D/g, '') === normalizedPhone
    );
  }

  /**
   * Get super admin tuple by email
   */
  getSuperAdminTupleByEmail(email: string): SuperAdminTuple | undefined {
    if (!this.initialized) this.initialize();
    return this.superAdminTuples.find(tuple => tuple.email === email.toLowerCase());
  }

  /**
   * Get super admin tuple by phone
   */
  getSuperAdminTupleByPhone(phoneNumber: string): SuperAdminTuple | undefined {
    if (!this.initialized) this.initialize();
    const normalizedPhone = phoneNumber.replace(/\D/g, '');
    return this.superAdminTuples.find(tuple => 
      tuple.phone.replace(/\D/g, '') === normalizedPhone
    );
  }

  /**
   * Check if an email belongs to a user defined in environment variables
   */
  isEnvUser(email: string): boolean {
    if (!this.initialized) this.initialize();
    return this.superAdminTuples.some(tuple => tuple.email === email.toLowerCase());
  }

  /**
   * Validate admin configuration
   */
  validateConfiguration(): boolean {
    if (!this.initialized) this.initialize();
    
    if (this.superAdminTuples.length === 0) {
      if (process.env.NODE_ENV === 'development') {
        console.log('Server: ⚠️  No super admin users configured in environment variables');
      }
      return false;
    }

    return true;
  }

  /**
   * Backward compatibility methods for existing code
   */
  isAdminEmail(email: string): boolean {
    return this.isSuperAdminEmail(email);
  }

  isAdminPhone(phoneNumber: string): boolean {
    return this.isSuperAdminPhone(phoneNumber);
  }

  getUserRole(email: string, profileRole?: string): string {
    if (!this.initialized) this.initialize();
    
    if (this.isSuperAdminEmail(email)) {
      return 'super_admin';
    }
    
    return profileRole || 'user';
  }
}

export const adminConfigService = AdminConfigService.getInstance();