import { prisma } from '@/lib/prisma';
import { googleCalendarService } from './google-calendar.service';

export interface MentoringSlot {
  id: string;
  teacherId: string;
  dayOfWeek: string;
  startTime: string;
  endTime: string;
  duration: number;
  isActive: boolean;
  maxBookingsPerWeek: number | null;
  googleCalendarId: string | null;
  // Reminder configuration
  reminderEnabled: boolean;
  reminderHoursBefore: number | null;
  reminderTemplateId: string | null;
  createdAt: string;
  updatedAt: string;
  teacher?: {
    id: string;
    name: string;
    email: string | null;
  };
  reminderTemplate?: {
    id: string;
    name: string;
    template: string;
    category: string | null;
  } | null;
  _count?: {
    bookings: number;
  };
}

export interface MentoringBooking {
  id: string;
  studentId: string;
  slotId: string;
  scheduledDateTime: string;
  status: string;
  studentMessage?: string | null;
  courseContext?: string | null;
  cancellationReason?: string | null;
  googleEventId?: string | null;
  createdAt: string;
  updatedAt: string;
  student: {
    id: string;
    name: string;
    email: string;
  };
  slot: {
    id: string;
    teacher: {
      id: string;
      name: string;
      email: string;
    };
    dayOfWeek: string;
    startTime: string;
    endTime: string;
    duration: number;
  };
}

export interface CreateMentoringSlotData {
  teacherId: string;
  dayOfWeek: string;
  startTime: string;
  endTime: string;
  duration?: number;
  maxBookingsPerWeek?: number;
  googleCalendarId?: string;
  // Reminder configuration
  reminderEnabled?: boolean;
  reminderHoursBefore?: number;
  reminderTemplateId?: string;
}

export interface UpdateMentoringSlotData {
  dayOfWeek?: string;
  startTime?: string;
  endTime?: string;
  duration?: number;
  isActive?: boolean;
  maxBookingsPerWeek?: number;
  googleCalendarId?: string;
  // Reminder configuration
  reminderEnabled?: boolean;
  reminderHoursBefore?: number;
  reminderTemplateId?: string;
}

export interface CreateMentoringBookingData {
  studentId: string;
  slotId: string;
  courseId?: string;
  scheduledDateTime: string;
  studentMessage?: string;
  courseContext?: string;
}

export interface UpdateMentoringBookingData {
  scheduledDate?: string;
  notes?: string;
  feedback?: string;
  status?: string;
}

export interface ServiceResult<T> {
  success: boolean;
  data?: T;
  error?: string;
}

class MentoringService {
  // MENTORING SLOTS MANAGEMENT

  async getMentoringSlots(teacherId?: string): Promise<ServiceResult<MentoringSlot[]>> {
    try {
      const where = teacherId ? { teacherId } : {};

      const slots = await prisma.mentoringSlot.findMany({
        where,
        include: {
          teacher: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          reminderTemplate: {
            select: {
              id: true,
              name: true,
              template: true,
              category: true,
            },
          },
          _count: {
            select: {
              bookings: true,
            },
          },
        },
        orderBy: [
          { dayOfWeek: 'asc' },
          { startTime: 'asc' },
        ],
      });

      return {
        success: true,
        data: slots.map(slot => ({
          ...slot,
          createdAt: slot.createdAt.toISOString(),
          updatedAt: slot.updatedAt.toISOString(),
        })),
      };
    } catch (error) {
      console.error('Error fetching mentoring slots:', error);
      return {
        success: false,
        error: 'Failed to fetch mentoring slots'
      };
    }
  }

  async getMentoringSlot(id: string): Promise<ServiceResult<MentoringSlot>> {
    try {
      const slot = await prisma.mentoringSlot.findUnique({
        where: { id },
        include: {
          teacher: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          reminderTemplate: {
            select: {
              id: true,
              name: true,
              template: true,
              category: true,
            },
          },
          _count: {
            select: {
              bookings: true,
            },
          },
        },
      });

      if (!slot) {
        return {
          success: false,
          error: 'Mentoring slot not found'
        };
      }

      return {
        success: true,
        data: {
          ...slot,
          createdAt: slot.createdAt.toISOString(),
          updatedAt: slot.updatedAt.toISOString(),
        },
      };
    } catch (error) {
      console.error('Error fetching mentoring slot:', error);
      return {
        success: false,
        error: 'Failed to fetch mentoring slot'
      };
    }
  }

  async createMentoringSlot(data: CreateMentoringSlotData): Promise<ServiceResult<MentoringSlot>> {
    try {
      // Validate time format
      if (!this.isValidTimeFormat(data.startTime) || !this.isValidTimeFormat(data.endTime)) {
        return {
          success: false,
          error: 'Invalid time format. Use HH:MM format.'
        };
      }

      // Calculate duration if not provided
      let duration = data.duration;
      if (!duration) {
        duration = this.calculateDurationFromTimes(data.startTime, data.endTime);
      }

      const slot = await prisma.mentoringSlot.create({
        data: {
          teacherId: data.teacherId,
          dayOfWeek: data.dayOfWeek as 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday',
          startTime: data.startTime,
          endTime: data.endTime,
          duration,
          maxBookingsPerWeek: data.maxBookingsPerWeek || 5,
          googleCalendarId: data.googleCalendarId,
          // Reminder configuration
          reminderEnabled: data.reminderEnabled || false,
          reminderHoursBefore: data.reminderHoursBefore,
          reminderTemplateId: data.reminderTemplateId,
        },
        include: {
          teacher: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          reminderTemplate: {
            select: {
              id: true,
              name: true,
              template: true,
              category: true,
            },
          },
          _count: {
            select: {
              bookings: true,
            },
          },
        },
      });

      return {
        success: true,
        data: {
          ...slot,
          createdAt: slot.createdAt.toISOString(),
          updatedAt: slot.updatedAt.toISOString(),
        },
      };
    } catch (error) {
      console.error('Error creating mentoring slot:', error);
      return {
        success: false,
        error: 'Failed to create mentoring slot'
      };
    }
  }

  async updateMentoringSlot(id: string, data: UpdateMentoringSlotData): Promise<ServiceResult<MentoringSlot>> {
    try {
      // Validate time formats if provided
      if (data.startTime && !this.isValidTimeFormat(data.startTime)) {
        return {
          success: false,
          error: 'Invalid start time format. Use HH:MM format.'
        };
      }

      if (data.endTime && !this.isValidTimeFormat(data.endTime)) {
        return {
          success: false,
          error: 'Invalid end time format. Use HH:MM format.'
        };
      }

      const updateData: Record<string, unknown> = {};
      Object.keys(data).forEach(key => {
        if (data[key as keyof UpdateMentoringSlotData] !== undefined) {
          updateData[key] = data[key as keyof UpdateMentoringSlotData];
        }
      });

      // Recalculate duration if start or end time changed
      if (data.startTime || data.endTime) {
        const existingSlot = await prisma.mentoringSlot.findUnique({
          where: { id },
        });

        if (existingSlot) {
          const newStartTime = data.startTime || existingSlot.startTime;
          const newEndTime = data.endTime || existingSlot.endTime;
          updateData.duration = this.calculateDurationFromTimes(newStartTime, newEndTime);
        }
      }

      const slot = await prisma.mentoringSlot.update({
        where: { id },
        data: updateData,
        include: {
          teacher: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          reminderTemplate: {
            select: {
              id: true,
              name: true,
              template: true,
              category: true,
            },
          },
          _count: {
            select: {
              bookings: true,
            },
          },
        },
      });

      return {
        success: true,
        data: {
          ...slot,
          createdAt: slot.createdAt.toISOString(),
          updatedAt: slot.updatedAt.toISOString(),
        },
      };
    } catch (error) {
      console.error('Error updating mentoring slot:', error);
      return {
        success: false,
        error: 'Failed to update mentoring slot'
      };
    }
  }

  async deleteMentoringSlot(id: string): Promise<ServiceResult<boolean>> {
    try {
      // Check if slot has any bookings
      const bookingsCount = await prisma.mentoringBooking.count({
        where: { 
          slotId: id,
          status: {
            in: ['scheduled', 'completed']
          }
        }
      });

      if (bookingsCount > 0) {
        return {
          success: false,
          error: 'Cannot delete slot with existing bookings. Please cancel or reschedule all bookings first.'
        };
      }

      await prisma.mentoringSlot.delete({
        where: { id }
      });

      return {
        success: true,
        data: true
      };
    } catch (error) {
      console.error('Error deleting mentoring slot:', error);
      return {
        success: false,
        error: 'Failed to delete mentoring slot'
      };
    }
  }

  // MENTORING BOOKINGS MANAGEMENT

  async getMentoringBookings(filters?: {
    studentId?: string;
    teacherId?: string;
    status?: string;
    fromDate?: string;
    toDate?: string;
  }): Promise<ServiceResult<MentoringBooking[]>> {
    try {
      const where: Record<string, unknown> = {};

      if (filters?.studentId) where.studentId = filters.studentId;
      if (filters?.teacherId) where.teacherId = filters.teacherId;
      if (filters?.status) where.status = filters.status;
      
      if (filters?.fromDate || filters?.toDate) {
        where.scheduledDateTime = {};
        if (filters.fromDate) (where.scheduledDateTime as Record<string, Date>).gte = new Date(filters.fromDate);
        if (filters.toDate) (where.scheduledDateTime as Record<string, Date>).lte = new Date(filters.toDate);
      }

      const bookings = await prisma.mentoringBooking.findMany({
        where,
        include: {
          student: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          teacher: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          course: {
            select: {
              id: true,
              name: true,
            },
          },
          slot: {
            include: {
              reminderTemplate: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
        orderBy: {
          scheduledDate: 'asc',
        },
      });

      return {
        success: true,
        data: bookings.map(booking => ({
          ...booking,
          scheduledDate: booking.scheduledDate.toISOString(),
          createdAt: booking.createdAt.toISOString(),
          updatedAt: booking.updatedAt.toISOString(),
          cancelledAt: booking.cancelledAt?.toISOString() || null,
          rescheduledAt: booking.rescheduledAt?.toISOString() || null,
          reminderSentAt: booking.reminderSentAt?.toISOString() || null,
          slot: booking.slot ? {
            ...booking.slot,
            createdAt: booking.slot.createdAt.toISOString(),
            updatedAt: booking.slot.updatedAt.toISOString(),
          } : undefined,
        })),
      };
    } catch (error) {
      console.error('Error fetching mentoring bookings:', error);
      return {
        success: false,
        error: 'Failed to fetch mentoring bookings'
      };
    }
  }

  async createMentoringBooking(data: CreateMentoringBookingData): Promise<ServiceResult<MentoringBooking>> {
    try {
      // Get slot details
      const slot = await prisma.mentoringSlot.findUnique({
        where: { id: data.slotId },
        include: {
          teacher: true,
        },
      });

      if (!slot) {
        return {
          success: false,
          error: 'Agenda de mentoria não encontrada'
        };
      }

      if (!slot.isActive) {
        return {
          success: false,
          error: 'Agenda de mentoria não está ativa'
        };
      }

      // Get student details with enrollments and courses
      const student = await prisma.student.findUnique({
        where: { id: data.studentId },
        include: {
          enrollments: {
            where: { isActive: true },
            include: {
              course: true
            }
          }
        }
      });

      if (!student) {
        return {
          success: false,
          error: 'Estudante não encontrado'
        };
      }

      // Check mentoring rights if courseId is provided
      if (data.courseId) {
        const enrollment = student.enrollments.find(e => e.courseId === data.courseId);
        if (!enrollment) {
          return {
            success: false,
            error: 'Estudante não está matriculado neste curso'
          };
        }

        const course = enrollment.course;
        const mentoringLimit = course.mentoringSessionsPerEnrollment || 0;

        if (mentoringLimit > 0) {
          // Count completed mentoring sessions for this course
          const completedSessions = await prisma.mentoringBooking.count({
            where: {
              studentId: data.studentId,
              courseId: data.courseId,
              status: 'completed'
            }
          });

          if (completedSessions >= mentoringLimit) {
            return {
              success: false,
              error: `Você já utilizou todas as ${mentoringLimit} sessões de mentoria disponíveis para este curso`
            };
          }
        }
      }

      const scheduledDateTime = new Date(data.scheduledDateTime);

      // Check if the time slot is available
      const existingBooking = await prisma.mentoringBooking.findFirst({
        where: {
          slotId: data.slotId,
          scheduledDateTime: scheduledDateTime,
          status: 'confirmed'
        }
      });

      if (existingBooking) {
        return {
          success: false,
          error: 'Horário já está ocupado'
        };
      }

      // Create Google Calendar event if calendar ID is provided
      let googleEventId = null;
      if (slot.googleCalendarId && slot.teacher.email && student.email) {
        const endDateTime = new Date(scheduledDateTime);
        endDateTime.setMinutes(endDateTime.getMinutes() + slot.duration);

        const calendarEvent = googleCalendarService.createMentoringEvent(
          slot.teacher.email,
          student.email,
          student.name,
          scheduledDateTime.toISOString(),
          endDateTime.toISOString(),
          data.courseContext
        );

        const calendarResult = await googleCalendarService.createEvent(
          slot.googleCalendarId,
          calendarEvent
        );

        if (calendarResult.success) {
          googleEventId = calendarResult.eventId || null;
        }
      }

      // Create booking
      const booking = await prisma.mentoringBooking.create({
        data: {
          studentId: data.studentId,
          teacherId: slot.teacherId,
          courseId: data.courseId || null,
          slotId: data.slotId,
          scheduledDate: scheduledDateTime,
          startTime: slot.startTime,
          endTime: slot.endTime,
          duration: slot.duration,
          status: 'scheduled',
          notes: data.studentMessage || null,
          googleEventId,
        },
        include: {
          student: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          slot: {
            include: {
              teacher: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
              reminderTemplate: true,
            },
          },
        },
      });

      // Schedule reminder if enabled for this slot
      if (slot.reminderEnabled && slot.reminderTemplateId && slot.reminderHoursBefore) {
        try {
          const { mentoringReminderService } = await import('./mentoring-reminder.service');
          const reminderResult = await mentoringReminderService.scheduleReminderForBooking(booking.id);
          
          if (!reminderResult.success) {
            console.warn(`Failed to schedule reminder for booking ${booking.id}: ${reminderResult.error}`);
          } else {
            console.log(`✅ Reminder scheduled for mentoring booking ${booking.id}`);
          }
        } catch (error) {
          console.error('Error scheduling mentoring reminder:', error);
        }
      }

      return {
        success: true,
        data: {
          id: booking.id,
          studentId: booking.studentId,
          slotId: booking.slotId,
          scheduledDateTime: booking.scheduledDateTime.toISOString(),
          status: booking.status,
          studentMessage: booking.studentMessage,
          courseContext: booking.courseContext,
          cancellationReason: booking.cancellationReason,
          googleEventId: booking.googleEventId,
          createdAt: booking.createdAt.toISOString(),
          updatedAt: booking.updatedAt.toISOString(),
          student: booking.student,
          slot: {
            id: booking.slot.id,
            teacher: booking.slot.teacher,
            dayOfWeek: booking.slot.dayOfWeek,
            startTime: booking.slot.startTime,
            endTime: booking.slot.endTime,
            duration: booking.slot.duration,
          },
        },
      };
    } catch (error) {
      console.error('Error creating mentoring booking:', error);
      return {
        success: false,
        error: 'Falha ao criar agendamento de mentoria'
      };
    }
  }

  async cancelMentoringBooking(id: string, reason: string): Promise<ServiceResult<MentoringBooking>> {
    try {
      const booking = await prisma.mentoringBooking.findUnique({
        where: { id },
        include: {
          slot: {
            include: {
              teacher: true,
            },
          },
          student: true,
        },
      });

      if (!booking) {
        return {
          success: false,
          error: 'Agendamento não encontrado'
        };
      }

      if (booking.status === 'cancelled') {
        return {
          success: false,
          error: 'Agendamento já foi cancelado'
        };
      }

      // Cancel Google Calendar event if exists
      if (booking.googleEventId && booking.slot?.googleCalendarId) {
        await googleCalendarService.deleteEvent(
          booking.slot.googleCalendarId,
          booking.googleEventId
        );
      }

      // Update booking status
      const updatedBooking = await prisma.mentoringBooking.update({
        where: { id },
        data: {
          status: 'cancelled',
          cancellationReason: reason,
          updatedAt: new Date(),
        },
        include: {
          student: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          slot: {
            include: {
              teacher: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
      });

      return {
        success: true,
        data: {
          id: updatedBooking.id,
          studentId: updatedBooking.studentId,
          slotId: updatedBooking.slotId,
          scheduledDateTime: updatedBooking.scheduledDateTime.toISOString(),
          status: updatedBooking.status,
          studentMessage: updatedBooking.studentMessage,
          courseContext: updatedBooking.courseContext,
          cancellationReason: updatedBooking.cancellationReason,
          googleEventId: updatedBooking.googleEventId,
          createdAt: updatedBooking.createdAt.toISOString(),
          updatedAt: updatedBooking.updatedAt.toISOString(),
          student: updatedBooking.student,
          slot: {
            id: updatedBooking.slot.id,
            teacher: updatedBooking.slot.teacher,
            dayOfWeek: updatedBooking.slot.dayOfWeek,
            startTime: updatedBooking.slot.startTime,
            endTime: updatedBooking.slot.endTime,
            duration: updatedBooking.slot.duration,
          },
        },
      };
    } catch (error) {
      console.error('Error cancelling mentoring booking:', error);
      return {
        success: false,
        error: 'Falha ao cancelar agendamento'
      };
    }
  }

  async completeMentoringBooking(bookingId: string): Promise<ServiceResult<MentoringBooking>> {
    try {
      const booking = await prisma.mentoringBooking.findUnique({
        where: { id: bookingId },
        include: {
          slot: {
            include: {
              teacher: true,
            },
          },
          student: true,
        },
      });

      if (!booking) {
        return {
          success: false,
          error: 'Agendamento não encontrado'
        };
      }

      if (booking.status === 'completed') {
        return {
          success: false,
          error: 'Agendamento já foi marcado como concluído'
        };
      }

      if (booking.status === 'cancelled') {
        return {
          success: false,
          error: 'Não é possível marcar um agendamento cancelado como concluído'
        };
      }

      const updatedBooking = await prisma.mentoringBooking.update({
        where: { id: bookingId },
        data: {
          status: 'completed',
          updatedAt: new Date(),
        },
        include: {
          student: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          slot: {
            include: {
              teacher: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
      });

      return {
        success: true,
        data: {
          id: updatedBooking.id,
          studentId: updatedBooking.studentId,
          slotId: updatedBooking.slotId,
          scheduledDateTime: updatedBooking.scheduledDateTime.toISOString(),
          status: updatedBooking.status,
          studentMessage: updatedBooking.studentMessage,
          courseContext: updatedBooking.courseContext,
          cancellationReason: updatedBooking.cancellationReason,
          googleEventId: updatedBooking.googleEventId,
          createdAt: updatedBooking.createdAt.toISOString(),
          updatedAt: updatedBooking.updatedAt.toISOString(),
          student: updatedBooking.student,
          slot: {
            id: updatedBooking.slot.id,
            teacher: updatedBooking.slot.teacher,
            dayOfWeek: updatedBooking.slot.dayOfWeek,
            startTime: updatedBooking.slot.startTime,
            endTime: updatedBooking.slot.endTime,
            duration: updatedBooking.slot.duration,
          },
        },
      };
    } catch (error) {
      console.error('Error completing mentoring booking:', error);
      return {
        success: false,
        error: 'Falha ao marcar agendamento como concluído'
      };
    }
  }

  // UTILITY METHODS

  private isValidTimeFormat(time: string): boolean {
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    return timeRegex.test(time);
  }

  private calculateDurationFromTimes(startTime: string, endTime: string): number {
    const [startHours, startMinutes] = startTime.split(':').map(Number);
    const [endHours, endMinutes] = endTime.split(':').map(Number);

    const startTotalMinutes = startHours * 60 + startMinutes;
    const endTotalMinutes = endHours * 60 + endMinutes;

    return endTotalMinutes - startTotalMinutes;
  }

  private combineDateTime(date: Date, time: string): Date {
    const [hours, minutes] = time.split(':').map(Number);
    const combined = new Date(date);
    combined.setHours(hours, minutes, 0, 0);
    return combined;
  }

  private async checkSlotAvailability(
    slotId: string,
    startDateTime: Date,
    endDateTime: Date
  ): Promise<{ success: boolean; available?: boolean; error?: string }> {
    try {
      // Check for conflicting bookings
      const conflictingBookings = await prisma.mentoringBooking.count({
        where: {
          slotId,
          status: {
            in: ['scheduled', 'completed']
          },
          scheduledDate: {
            gte: new Date(startDateTime.toDateString()),
            lt: new Date(new Date(startDateTime.toDateString()).getTime() + 24 * 60 * 60 * 1000)
          }
        }
      });

      return {
        success: true,
        available: conflictingBookings === 0
      };
    } catch (error) {
      console.error('Error checking slot availability:', error);
      return {
        success: false,
        error: 'Failed to check availability'
      };
    }
  }

  async getAvailableDates(slotId: string, weeksAhead: number = 4): Promise<ServiceResult<Array<{
    date: string;
    dayOfWeek: string;
    slots: Array<{
      startTime: string;
      endTime: string;
      available: boolean;
    }>;
  }>>> {
    try {
      const slot = await prisma.mentoringSlot.findUnique({
        where: { id: slotId },
        include: {
          bookings: {
            where: {
              status: 'confirmed',
              scheduledDateTime: {
                gte: new Date()
              }
            }
          }
        }
      });

      if (!slot) {
        return { success: false, error: 'Agenda não encontrada' };
      }

      if (!slot.isActive) {
        return { success: false, error: 'Agenda não está ativa' };
      }

      const availableDates: Array<{
        date: string;
        dayOfWeek: string;
        slots: Array<{
          startTime: string;
          endTime: string;
          available: boolean;
        }>;
      }> = [];
      const startDate = new Date();
      const endDate = new Date();
      endDate.setDate(startDate.getDate() + (weeksAhead * 7));

      // Get day of week number (0 = Sunday, 1 = Monday, etc.)
      const dayOfWeekMap = {
        'sunday': 0,
        'monday': 1,
        'tuesday': 2,
        'wednesday': 3,
        'thursday': 4,
        'friday': 5,
        'saturday': 6
      };

      const targetDayOfWeek = dayOfWeekMap[slot.dayOfWeek as keyof typeof dayOfWeekMap];

      for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
        if (date.getDay() === targetDayOfWeek) {
          const dateString = date.toISOString().split('T')[0];
          
          // Generate time slots based on slot duration
          const slots = [];
          const [startHour, startMinute] = slot.startTime.split(':').map(Number);
          const [endHour, endMinute] = slot.endTime.split(':').map(Number);
          
          const startTotalMinutes = startHour * 60 + startMinute;
          const endTotalMinutes = endHour * 60 + endMinute;
          
          for (let minutes = startTotalMinutes; minutes < endTotalMinutes; minutes += slot.duration) {
            const slotHour = Math.floor(minutes / 60);
            const slotMinute = minutes % 60;
            const timeString = `${slotHour.toString().padStart(2, '0')}:${slotMinute.toString().padStart(2, '0')}`;
            
            // Check if this time slot is already booked
            const scheduledDateTime = new Date(date);
            scheduledDateTime.setHours(slotHour, slotMinute, 0, 0);
            
            const isBooked = slot.bookings.some(booking => {
              const bookingDateTime = new Date(booking.scheduledDateTime);
              return bookingDateTime.getTime() === scheduledDateTime.getTime();
            });

            // Check if the slot time has already passed today
            const now = new Date();
            const isInPast = scheduledDateTime <= now;
            
            slots.push({
              startTime: timeString,
              endTime: `${Math.floor((minutes + slot.duration) / 60).toString().padStart(2, '0')}:${((minutes + slot.duration) % 60).toString().padStart(2, '0')}`,
              available: !isBooked && !isInPast
            });
          }

          // Only include dates that have at least one available slot
          if (slots.some(slot => slot.available)) {
            availableDates.push({
              date: dateString,
              dayOfWeek: slot.dayOfWeek,
              slots
            });
          }
        }
      }

      return { success: true, data: availableDates };
    } catch (error) {
      console.error('Error getting available dates:', error);
      return { success: false, error: 'Falha ao buscar datas disponíveis' };
    }
  }
}

export const mentoringService = new MentoringService();