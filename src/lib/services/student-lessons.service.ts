import { prisma } from '@/lib/prisma';

export interface StudentLessonVisibility {
  studentId: string;
  classId: string;
  lessons: {
    id: string;
    title: string;
    description: string | null;
    lessonNumber: number | null;
    scheduledDate: Date;
    duration: number | null;
    location: string | null;
    isCompleted: boolean;
    notes: string | null;
    attendance: {
      status: string;
      markedAt: Date | null;
      markedByFacialRecognition: boolean;
    } | null;
  }[];
  totalLessons: number;
  visibleLessons: number;
  hiddenLessons: number;
  student: {
    id: string;
    name: string;
  };
  class: {
    id: string;
    name: string;
  };
  course: {
    id: string;
    name: string;
  };
}

export class StudentLessonsService {
  /**
   * Get lessons visible to a student for a specific class
   * Students can see:
   * - Past lessons (completed or date has passed)
   * - Upcoming lessons within 6 days
   */
  static async getStudentLessons(
    studentId: string, 
    classId: string
  ): Promise<StudentLessonVisibility | null> {
    // Check if student is enrolled in this class
    const enrollment = await prisma.enrollment.findFirst({
      where: {
        studentId,
        classId,
        status: 'active'
      },
      include: {
        student: {
          select: {
            id: true,
            name: true
          }
        },
        class: {
          select: {
            id: true,
            name: true
          }
        },
        course: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    if (!enrollment) {
      return null; // Student not enrolled in this class
    }

    // Get current date and calculate 6 days in the future
    const now = new Date();
    const sixDaysFromNow = new Date(now);
    sixDaysFromNow.setDate(now.getDate() + 6);

    // Get all lessons for this class
    const allLessons = await prisma.lesson.findMany({
      where: {
        classId
      },
      orderBy: [
        { lessonNumber: 'asc' },
        { scheduledDate: 'asc' }
      ],
      include: {
        attendance: {
          where: {
            studentId
          },
          select: {
            status: true,
            markedAt: true,
            markedByFacialRecognition: true
          }
        }
      }
    });

    // Filter lessons based on visibility rules
    const visibleLessons = allLessons.filter(lesson => {
      const lessonDate = new Date(lesson.scheduledDate);
      
      // Show past lessons (completed or date has passed)
      if (lessonDate < now || lesson.isCompleted) {
        return true;
      }
      
      // Show upcoming lessons within 6 days
      if (lessonDate >= now && lessonDate <= sixDaysFromNow) {
        return true;
      }
      
      return false;
    });

    // Format response with lesson data and attendance info
    const lessonsWithAttendance = visibleLessons.map(lesson => ({
      id: lesson.id,
      title: lesson.title,
      description: lesson.description,
      lessonNumber: lesson.lessonNumber,
      scheduledDate: lesson.scheduledDate,
      duration: lesson.duration,
      location: lesson.location,
      isCompleted: lesson.isCompleted,
      notes: lesson.notes,
      attendance: lesson.attendance.length > 0 ? {
        status: lesson.attendance[0].status,
        markedAt: lesson.attendance[0].markedAt,
        markedByFacialRecognition: lesson.attendance[0].markedByFacialRecognition
      } : null
    }));

    return {
      studentId,
      classId,
      student: enrollment.student,
      class: enrollment.class,
      course: enrollment.course,
      lessons: lessonsWithAttendance,
      totalLessons: allLessons.length,
      visibleLessons: visibleLessons.length,
      hiddenLessons: allLessons.length - visibleLessons.length
    };
  }

  /**
   * Check if a student can see a specific lesson
   */
  static async canStudentSeeLesson(
    studentId: string, 
    lessonId: string
  ): Promise<boolean> {
    const lesson = await prisma.lesson.findUnique({
      where: { id: lessonId },
      include: {
        class: {
          include: {
            enrollments: {
              where: {
                studentId,
                status: 'active'
              }
            }
          }
        }
      }
    });

    if (!lesson || lesson.class.enrollments.length === 0) {
      return false; // Lesson doesn't exist or student not enrolled
    }

    const now = new Date();
    const sixDaysFromNow = new Date(now);
    sixDaysFromNow.setDate(now.getDate() + 6);
    const lessonDate = new Date(lesson.scheduledDate);

    // Apply visibility rules
    return (
      lessonDate < now || 
      lesson.isCompleted || 
      (lessonDate >= now && lessonDate <= sixDaysFromNow)
    );
  }

  /**
   * Get all classes and their visible lessons for a student
   */
  static async getAllStudentLessons(studentId: string) {
    const enrollments = await prisma.enrollment.findMany({
      where: {
        studentId,
        status: 'active'
      },
      include: {
        class: {
          select: {
            id: true,
            name: true
          }
        },
        course: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    const results = [];
    
    for (const enrollment of enrollments) {
      if (enrollment.class && enrollment.class.id) {
        const lessons = await this.getStudentLessons(studentId, enrollment.class.id);
        if (lessons) {
          results.push(lessons);
        }
      }
    }

    return results;
  }
}