import { prisma } from '@/lib/prisma';
import { addDays, startOfDay, addHours } from 'date-fns';
import { randomBytes } from 'crypto';

export interface RecoveryReminderData {
  studentId: string;
  attendanceId: string;
  reminderDate: Date;
}

export interface AvailableMakeupLesson {
  id: string;
  title: string;
  scheduledDate: Date;
  lessonNumber?: number | null;
  class: {
    id: string;
    name: string;
    course: {
      id: string;
      name: string;
    };
  };
}

export interface RecoveryBookingData {
  studentId: string;
  originalLessonId: string;
  makeupLessonId: string;
  otpToken: string;
}

// OTP validity period in hours (default to 24 if not set in env)
const OTP_VALIDITY_HOURS = Number(process.env.OTP_VALIDITY_HOURS) || 24;

class RecoveryService {
  /**
   * Generate a secure OTP token for recovery authentication.
   * 
   * This provides strong cryptographic randomness suitable for authentication tokens.
   * The token is safe for use in URLs and storage, and is not predictable.
   *
   * @returns {string} A 64-character hex string representing 256 bits of entropy.
   */
  private generateOtpToken(): string {
    return randomBytes(32).toString('hex');
  }

  /**
   * Create a recovery reminder for an absence
   */
  async createRecoveryReminder(data: RecoveryReminderData) {
    try {
      // Check if reminder already exists
      const existingReminder = await prisma.recoveryReminder.findUnique({
        where: {
          studentId_attendanceId: {
            studentId: data.studentId,
            attendanceId: data.attendanceId
          }
        }
      });

      if (existingReminder) {
        throw new Error('Recovery reminder already exists for this absence');
      }

      const reminder = await prisma.recoveryReminder.create({
        data: {
          studentId: data.studentId,
          attendanceId: data.attendanceId,
          reminderDate: data.reminderDate,
          status: 'pending'
        },
        include: {
          student: {
            select: {
              id: true,
              name: true,
              phone: true
            }
          },
          attendance: {
            include: {
              lesson: {
                select: {
                  id: true,
                  title: true,
                  scheduledDate: true,
                  class: {
                    select: {
                      name: true,
                      course: {
                        select: {
                          name: true
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      });

      return reminder;
    } catch (error) {
      console.error('Error creating recovery reminder:', error);
      throw error;
    }
  }

  /**
   * Generate an OTP for recovery authentication
   */
  async generateRecoveryOtp(studentId: string, attendanceId: string) {
    try {
      // Verify the attendance record exists and is an absence
      const attendance = await prisma.attendance.findFirst({
        where: {
          id: attendanceId,
          studentId: studentId,
          status: 'absent'
        },
        include: {
          lesson: {
            include: {
              class: {
                include: {
                  course: true
                }
              }
            }
          }
        }
      });

      if (!attendance) {
        throw new Error('Attendance record not found or not an absence');
      }

      // Check if course allows makeup classes
      if (!attendance.lesson.class.course.allowsMakeup) {
        throw new Error('This course does not allow makeup classes');
      }

      // Check if there's already an OTP for this absence
      const existingOtp = await prisma.recoveryOtp.findFirst({
        where: {
          studentId,
          attendanceId,
          expiresAt: {
            gt: new Date()
          }
        }
      });

      if (existingOtp) {
        return existingOtp;
      }

      const token = this.generateOtpToken();
      const expiresAt = addHours(new Date(), OTP_VALIDITY_HOURS); // OTP validity period

      const otp = await prisma.recoveryOtp.create({
        data: {
          studentId,
          attendanceId,
          token,
          expiresAt
        }
      });

      return otp;
    } catch (error) {
      console.error('Error generating recovery OTP:', error);
      throw error;
    }
  }

  /**
   * Validate an OTP token and return the associated data
   */
  async validateRecoveryOtp(token: string) {
    try {
      const otp = await prisma.recoveryOtp.findFirst({
        where: {
          token,
          usedAt: null,
          expiresAt: {
            gt: new Date()
          }
        },
        include: {
          student: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true
            }
          },
          attendance: {
            include: {
              lesson: {
                include: {
                  class: {
                    include: {
                      course: {
                        select: {
                          id: true,
                          name: true,
                          allowsMakeup: true
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      });

      if (!otp) {
        throw new Error('Invalid or expired OTP token');
      }

      return otp;
    } catch (error) {
      console.error('Error validating recovery OTP:', error);
      throw error;
    }
  }

  /**
   * Get available makeup lessons for a specific course and missed lesson
   */
  async getAvailableMakeupLessons(courseId: string, missedLessonDate: Date, missedLessonNumber?: number): Promise<AvailableMakeupLesson[]> {
    try {
      // Build the where clause
      const whereClause = {
        class: {
          courseId: courseId,
          isActive: true
        },
        scheduledDate: {
          gt: new Date() // Only future lessons
        },
        isCompleted: false
      };

      // If lesson number is provided, only show lessons with the same lesson number
      if (missedLessonNumber !== undefined && missedLessonNumber !== null) {
        whereClause.lessonNumber = missedLessonNumber;
      }

      // Get all future lessons for the same course that haven't happened yet
      const availableLessons = await prisma.lesson.findMany({
        where: whereClause,
        include: {
          class: {
            include: {
              course: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        },
        orderBy: {
          scheduledDate: 'asc'
        }
      });

      return availableLessons.map(lesson => ({
        id: lesson.id,
        title: lesson.title,
        scheduledDate: lesson.scheduledDate,
        lessonNumber: lesson.lessonNumber,
        class: {
          id: lesson.class.id,
          name: lesson.class.name,
          course: {
            id: lesson.class.course.id,
            name: lesson.class.course.name
          }
        }
      }));
    } catch (error) {
      console.error('Error getting available makeup lessons:', error);
      throw error;
    }
  }

  /**
   * Book a makeup lesson using OTP authentication
   */
  async bookMakeupLesson(data: RecoveryBookingData) {
    try {
      // Validate the OTP first
      const otp = await this.validateRecoveryOtp(data.otpToken);
      
      if (otp.studentId !== data.studentId) {
        throw new Error('OTP does not belong to this student');
      }

      // Verify the original lesson matches the attendance record
      if (otp.attendance.lessonId !== data.originalLessonId) {
        throw new Error('Original lesson does not match the absence record');
      }

      // Check if makeup lesson exists and is available
      const makeupLesson = await prisma.lesson.findFirst({
        where: {
          id: data.makeupLessonId,
          scheduledDate: {
            gt: new Date()
          },
          isCompleted: false
        },
        include: {
          class: {
            include: {
              course: true
            }
          }
        }
      });

      if (!makeupLesson) {
        throw new Error('Makeup lesson not found or not available');
      }

      // Verify it's the same course
      if (makeupLesson.class.courseId !== otp.attendance.lesson.class.courseId) {
        throw new Error('Makeup lesson must be from the same course');
      }

      // Check if student already has a booking for this absence
      const existingBooking = await prisma.recoveryBooking.findUnique({
        where: {
          studentId_originalLessonId: {
            studentId: data.studentId,
            originalLessonId: data.originalLessonId
          }
        }
      });

      if (existingBooking) {
        throw new Error('Makeup lesson already booked for this absence');
      }

      // Create the booking in a transaction
      const result = await prisma.$transaction(async (tx) => {
        // Mark the OTP as used
        await tx.recoveryOtp.update({
          where: { id: otp.id },
          data: { usedAt: new Date() }
        });

        // Create the recovery booking
        const booking = await tx.recoveryBooking.create({
          data: {
            studentId: data.studentId,
            originalLessonId: data.originalLessonId,
            makeupLessonId: data.makeupLessonId,
            otpId: otp.id,
            status: 'booked'
          },
          include: {
            student: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true
              }
            },
            originalLesson: {
              select: {
                id: true,
                title: true,
                scheduledDate: true
              }
            },
            makeupLesson: {
              select: {
                id: true,
                title: true,
                scheduledDate: true,
                class: {
                  select: {
                    name: true,
                    course: {
                      select: {
                        name: true
                      }
                    }
                  }
                }
              }
            }
          }
        });

        // Create an attendance record for the makeup lesson
        await tx.attendance.create({
          data: {
            studentId: data.studentId,
            lessonId: data.makeupLessonId,
            originalLessonId: data.originalLessonId,
            status: 'present', // Pre-mark as present for makeup
            markedAt: new Date(),
            notes: `Makeup lesson for absence on ${otp.attendance.lesson.scheduledDate.toLocaleDateString()}`
          }
        });

        return booking;
      });

      return result;
    } catch (error) {
      console.error('Error booking makeup lesson:', error);
      throw error;
    }
  }

  /**
   * Schedule recovery reminders for recent absences
   * This should be called after attendance is marked
   */
  async scheduleRecoveryReminders(lessonId: string) {
    try {
      // Get all absences for this lesson where the course allows makeup
      const absences = await prisma.attendance.findMany({
        where: {
          lessonId,
          status: 'absent'
        },
        include: {
          student: {
            select: {
              id: true,
              name: true,
              phone: true
            }
          },
          lesson: {
            include: {
              class: {
                include: {
                  course: {
                    select: {
                      allowsMakeup: true
                    }
                  }
                }
              }
            }
          },
          recoveryReminders: true // Check if reminder already exists
        }
      });

      const remindersToCreate = [];

      for (const absence of absences) {
        // Only create reminders for courses that allow makeup
        if (!absence.lesson.class.course.allowsMakeup) {
          continue;
        }

        // Skip if reminder already exists
        if (absence.recoveryReminders.length > 0) {
          continue;
        }

        // Calculate reminder date (9AM next day)
        const nextDay = addDays(startOfDay(new Date()), 1);
        const reminderDate = addHours(nextDay, 9); // 9AM

        remindersToCreate.push({
          studentId: absence.studentId,
          attendanceId: absence.id,
          reminderDate
        });
      }

      // Create all reminders
      const createdReminders = [];
      for (const reminderData of remindersToCreate) {
        try {
          const reminder = await this.createRecoveryReminder(reminderData);
          createdReminders.push(reminder);
        } catch (error) {
          console.error('Error creating individual reminder:', error);
          // Continue with other reminders
        }
      }

      return createdReminders;
    } catch (error) {
      console.error('Error scheduling recovery reminders:', error);
      throw error;
    }
  }

  /**
   * Get pending recovery reminders that need to be sent
   */
  async getPendingReminders() {
    try {
      const pendingReminders = await prisma.recoveryReminder.findMany({
        where: {
          status: 'pending',
          reminderDate: {
            lte: new Date()
          }
        },
        include: {
          student: {
            select: {
              id: true,
              name: true,
              phone: true
            }
          },
          attendance: {
            include: {
              lesson: {
                include: {
                  class: {
                    include: {
                      course: {
                        select: {
                          id: true,
                          name: true
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        orderBy: {
          reminderDate: 'asc'
        }
      });

      return pendingReminders;
    } catch (error) {
      console.error('Error getting pending reminders:', error);
      throw error;
    }
  }

  /**
   * Mark a reminder as sent
   */
  async markReminderAsSent(reminderId: string, messageText: string) {
    try {
      const reminder = await prisma.recoveryReminder.update({
        where: { id: reminderId },
        data: {
          status: 'sent',
          sentAt: new Date(),
          messageText
        }
      });

      return reminder;
    } catch (error) {
      console.error('Error marking reminder as sent:', error);
      throw error;
    }
  }

  /**
   * Mark a reminder as failed
   */
  async markReminderAsFailed(reminderId: string, errorMessage: string) {
    try {
      const reminder = await prisma.recoveryReminder.update({
        where: { id: reminderId },
        data: {
          status: 'failed',
          errorMessage
        }
      });

      return reminder;
    } catch (error) {
      console.error('Error marking reminder as failed:', error);
      throw error;
    }
  }

  /**
   * Get student's existing recovery bookings for display/validation
   */
  async getStudentRecoveryBookings(studentId: string) {
    try {
      const bookings = await prisma.recoveryBooking.findMany({
        where: {
          studentId,
          status: {
            in: ['booked', 'completed']
          }
        },
        include: {
          originalLesson: {
            select: {
              id: true,
              title: true,
              scheduledDate: true,
              lessonNumber: true,
              class: {
                select: {
                  name: true,
                  course: {
                    select: {
                      name: true
                    }
                  }
                }
              }
            }
          },
          makeupLesson: {
            select: {
              id: true,
              title: true,
              scheduledDate: true,
              lessonNumber: true,
              class: {
                select: {
                  name: true,
                  course: {
                    select: {
                      name: true
                    }
                  }
                }
              }
            }
          }
        },
        orderBy: {
          bookedAt: 'desc'
        }
      });

      return bookings;
    } catch (error) {
      console.error('Error getting student recovery bookings:', error);
      throw error;
    }
  }

  /**
   * Cancel a recovery booking
   */
  async cancelRecoveryBooking(bookingId: string, studentId: string) {
    try {
      // Verify the booking belongs to the student and can be cancelled
      const booking = await prisma.recoveryBooking.findFirst({
        where: {
          id: bookingId,
          studentId,
          status: 'booked'
        },
        include: {
          makeupLesson: {
            select: {
              scheduledDate: true,
              isCompleted: true
            }
          }
        }
      });

      if (!booking) {
        throw new Error('Booking not found or cannot be cancelled');
      }

      // Check if the makeup lesson has already happened
      if (booking.makeupLesson.scheduledDate <= new Date() || booking.makeupLesson.isCompleted) {
        throw new Error('Cannot cancel booking for a lesson that has already occurred');
      }

      const result = await prisma.$transaction(async (tx) => {
        // Cancel the booking
        const cancelledBooking = await tx.recoveryBooking.update({
          where: { id: bookingId },
          data: {
            status: 'cancelled',
            updatedAt: new Date()
          }
        });

        // Remove the pre-marked attendance for the makeup lesson
        await tx.attendance.deleteMany({
          where: {
            studentId,
            lessonId: booking.makeupLessonId,
            originalLessonId: booking.originalLessonId
          }
        });

        return cancelledBooking;
      });

      return result;
    } catch (error) {
      console.error('Error cancelling recovery booking:', error);
      throw error;
    }
  }
}

export const recoveryService = new RecoveryService();