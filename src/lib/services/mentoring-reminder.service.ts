import { prisma } from '@/lib/prisma';
import { TemplateProcessor } from '../template-processor';

export interface MentoringReminderJob {
  id: string;
  bookingId: string;
  studentId: string;
  teacherId: string;
  slotId: string;
  templateId: string;
  scheduledFor: Date;
  scheduledDateTime: Date; // The mentoring session date/time
  status: 'pending' | 'sent' | 'failed' | 'cancelled';
  attempts: number;
  lastAttempt?: Date;
  errorMessage?: string;
  messageText?: string;
}

export interface ReminderPreview {
  success: boolean;
  message?: string;
  variables?: Record<string, string>;
  error?: string;
}

export interface ReminderStatistics {
  totalReminders: number;
  sentReminders: number;
  failedReminders: number;
  pendingReminders: number;
  successRate: string;
}

export interface ServiceResult<T> {
  success: boolean;
  data?: T;
  error?: string;
}

class MentoringReminderService {
  private static readonly MAX_ATTEMPTS = 3;
  private static readonly RETRY_DELAY_MINUTES = 5;

  /**
   * Schedule a reminder for a specific mentoring booking
   */
  async scheduleReminderForBooking(bookingId: string): Promise<ServiceResult<MentoringReminderJob>> {
    try {
      // Get the booking with all necessary details
      const booking = await prisma.mentoringBooking.findUnique({
        where: { id: bookingId },
        include: {
          student: true,
          teacher: true,
          slot: {
            include: {
              reminderTemplate: true,
            },
          },
        },
      });

      if (!booking) {
        return {
          success: false,
          error: 'Booking not found'
        };
      }

      if (!booking.slot.reminderEnabled) {
        return {
          success: false,
          error: 'Reminders are not enabled for this mentoring slot'
        };
      }

      if (!booking.slot.reminderTemplateId || !booking.slot.reminderTemplate) {
        return {
          success: false,
          error: 'No reminder template configured for this mentoring slot'
        };
      }

      if (!booking.slot.reminderHoursBefore) {
        return {
          success: false,
          error: 'Reminder hours before not configured for this mentoring slot'
        };
      }

      if (!booking.student.phone) {
        return {
          success: false,
          error: 'Student does not have a phone number for reminders'
        };
      }

      // Calculate the reminder time
      const mentoringDateTime = new Date(booking.scheduledDate);
      const reminderDateTime = new Date(mentoringDateTime);
      reminderDateTime.setHours(reminderDateTime.getHours() - booking.slot.reminderHoursBefore);

      // Check if reminder time has already passed
      const now = new Date();
      if (reminderDateTime <= now) {
        return {
          success: false,
          error: 'Reminder time has already passed for this booking'
        };
      }

      // Check if reminder already exists
      const existingReminder = await prisma.messageQueue.findFirst({
        where: {
          recipientPhone: booking.student.phone,
          messageType: 'mentoria',
          scheduledFor: reminderDateTime,
          status: { in: ['pending', 'processing'] },
          metadata: {
            contains: `"bookingId":"${bookingId}"`
          }
        }
      });

      if (existingReminder) {
        return {
          success: false,
          error: 'Reminder already scheduled for this booking'
        };
      }

      // Create template context
      const context = await this.createTemplateContext(booking);

      // Process the template
      const processedTemplate = TemplateProcessor.processTemplate(
        booking.slot.reminderTemplate.template,
        context
      );

      // Create reminder in message queue
      const messageQueue = await prisma.messageQueue.create({
        data: {
          recipientPhone: booking.student.phone,
          messageText: processedTemplate.message,
          messageType: 'mentoria',
          priority: 2, // High priority for mentoring reminders
          scheduledFor: reminderDateTime,
          metadata: JSON.stringify({
            bookingId: booking.id,
            studentId: booking.studentId,
            teacherId: booking.teacherId,
            slotId: booking.slotId,
            templateId: booking.slot.reminderTemplateId,
            mentoringDateTime: mentoringDateTime.toISOString(),
            reminderType: 'mentoring'
          })
        }
      });

      // Update booking to mark reminder as scheduled
      await prisma.mentoringBooking.update({
        where: { id: bookingId },
        data: {
          reminderTemplate: booking.slot.reminderTemplate.name
        }
      });

      return {
        success: true,
        data: {
          id: messageQueue.id,
          bookingId: booking.id,
          studentId: booking.studentId,
          teacherId: booking.teacherId,
          slotId: booking.slotId,
          templateId: booking.slot.reminderTemplateId,
          scheduledFor: reminderDateTime,
          scheduledDateTime: mentoringDateTime,
          status: 'pending' as const,
          attempts: 0,
          messageText: processedTemplate.message
        }
      };

    } catch (error) {
      console.error('Error scheduling mentoring reminder:', error);
      return {
        success: false,
        error: 'Failed to schedule mentoring reminder'
      };
    }
  }

  /**
   * Schedule reminders for all upcoming mentoring sessions
   */
  async scheduleBulkReminders(hoursAhead: number = 24): Promise<ServiceResult<{
    scheduled: number;
    skipped: number;
    failed: number;
  }>> {
    try {
      console.log(`🔔 Scheduling mentoring reminders ${hoursAhead} hours ahead...`);

      // Calculate the time window for mentoring sessions to remind about
      const now = new Date();
      const reminderWindow = new Date(now.getTime() + hoursAhead * 60 * 60 * 1000);
      const windowStart = new Date(reminderWindow.getTime() - 30 * 60 * 1000); // 30 min before
      const windowEnd = new Date(reminderWindow.getTime() + 30 * 60 * 1000); // 30 min after

      // Find upcoming mentoring bookings that need reminders
      const upcomingBookings = await prisma.mentoringBooking.findMany({
        where: {
          scheduledDate: {
            gte: windowStart,
            lte: windowEnd
          },
          status: 'confirmed',
          slot: {
            reminderEnabled: true,
            reminderTemplateId: { not: null },
            reminderHoursBefore: { not: null }
          }
        },
        include: {
          student: true,
          teacher: true,
          slot: {
            include: {
              reminderTemplate: true,
            },
          },
        }
      });

      console.log(`📚 Found ${upcomingBookings.length} mentoring bookings to process for reminders`);

      let scheduled = 0;
      let skipped = 0;
      let failed = 0;

      for (const booking of upcomingBookings) {
        try {
          const result = await this.scheduleReminderForBooking(booking.id);
          if (result.success) {
            scheduled++;
            console.log(`✅ Reminder scheduled for ${booking.student.name} - ${booking.teacher.name}`);
          } else {
            skipped++;
            console.log(`⚠️ Skipped reminder for ${booking.student.name}: ${result.error}`);
          }
        } catch (error) {
          failed++;
          console.error(`❌ Failed to schedule reminder for ${booking.student.name}:`, error);
        }

        // Add small delay between scheduling
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      console.log(`✅ Bulk reminder scheduling completed: ${scheduled} scheduled, ${skipped} skipped, ${failed} failed`);

      return {
        success: true,
        data: { scheduled, skipped, failed }
      };

    } catch (error) {
      console.error('Error in bulk reminder scheduling:', error);
      return {
        success: false,
        error: 'Failed to schedule bulk reminders'
      };
    }
  }

  /**
   * Process pending mentoring reminders
   */
  async processPendingReminders(): Promise<{
    processed: number;
    sent: number;
    failed: number;
  }> {
    try {
      console.log('🔔 Processing pending mentoring reminders...');

      const now = new Date();
      
      // Get pending mentoring reminders that are due
      const pendingReminders = await prisma.messageQueue.findMany({
        where: {
          messageType: 'mentoria',
          status: 'pending',
          scheduledFor: { lte: now },
          attempts: { lt: MentoringReminderService.MAX_ATTEMPTS }
        },
        orderBy: [
          { priority: 'asc' },
          { scheduledFor: 'asc' }
        ],
        take: 50 // Process in batches
      });

      console.log(`📋 Found ${pendingReminders.length} pending mentoring reminders to process`);

      let sent = 0;
      let failed = 0;

      for (const reminder of pendingReminders) {
        try {
          // Update status to processing
          await prisma.messageQueue.update({
            where: { id: reminder.id },
            data: {
              status: 'processing',
              lastAttemptAt: now,
              attempts: { increment: 1 }
            }
          });

          // Send the reminder via WhatsApp
          const { sendWhatsAppReminder } = await import('@/lib/whatsapp');
          const result = await sendWhatsAppReminder(
            reminder.recipientPhone,
            reminder.messageText,
            JSON.parse(reminder.metadata || '{}')
          );

          if (result.success) {
            // Mark as sent
            await prisma.messageQueue.update({
              where: { id: reminder.id },
              data: {
                status: 'sent',
                sentAt: now,
                errorMessage: null
              }
            });

            // Update booking reminder status
            const metadata = JSON.parse(reminder.metadata || '{}');
            if (metadata.bookingId) {
              await prisma.mentoringBooking.update({
                where: { id: metadata.bookingId },
                data: {
                  reminderSent: true,
                  reminderSentAt: now
                }
              });
            }

            sent++;
            console.log(`✅ Mentoring reminder sent to ${reminder.recipientPhone}`);

          } else {
            // Mark as failed if max attempts reached, otherwise back to pending
            const newStatus = reminder.attempts >= MentoringReminderService.MAX_ATTEMPTS - 1 ? 'failed' : 'pending';
            
            await prisma.messageQueue.update({
              where: { id: reminder.id },
              data: {
                status: newStatus,
                errorMessage: result.error || 'Unknown error'
              }
            });

            if (newStatus === 'failed') {
              failed++;
              console.error(`❌ Mentoring reminder failed for ${reminder.recipientPhone}: ${result.error}`);
            } else {
              console.log(`⚠️ Mentoring reminder attempt failed for ${reminder.recipientPhone}, will retry`);
            }
          }

        } catch (error) {
          console.error(`❌ Error processing mentoring reminder ${reminder.id}:`, error);
          
          // Mark as failed if max attempts reached
          const newStatus = reminder.attempts >= MentoringReminderService.MAX_ATTEMPTS - 1 ? 'failed' : 'pending';
          
          await prisma.messageQueue.update({
            where: { id: reminder.id },
            data: {
              status: newStatus,
              errorMessage: error instanceof Error ? error.message : 'Unknown error'
            }
          });

          if (newStatus === 'failed') {
            failed++;
          }
        }

        // Add delay between messages to respect rate limiting
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      console.log(`✅ Mentoring reminder processing completed: ${sent} sent, ${failed} failed`);

      return {
        processed: pendingReminders.length,
        sent,
        failed
      };

    } catch (error) {
      console.error('Error processing mentoring reminders:', error);
      throw error;
    }
  }

  /**
   * Preview a reminder template with sample data
   */
  async previewReminderTemplate(templateId: string, slotId: string): Promise<ReminderPreview> {
    try {
      // Get the template
      const template = await prisma.reminderTemplate.findUnique({
        where: { id: templateId }
      });

      if (!template) {
        return {
          success: false,
          error: 'Template not found'
        };
      }

      // Get slot for context
      const slot = await prisma.mentoringSlot.findUnique({
        where: { id: slotId },
        include: {
          teacher: true
        }
      });

      if (!slot) {
        return {
          success: false,
          error: 'Mentoring slot not found'
        };
      }

      // Create sample context for preview
      const sampleDate = new Date();
      sampleDate.setDate(sampleDate.getDate() + 1); // Tomorrow
      sampleDate.setHours(parseInt(slot.startTime.split(':')[0]), parseInt(slot.startTime.split(':')[1]), 0, 0);

      const context = {
        student: {
          id: 'sample-student-id',
          name: 'João Silva',
          email: '<EMAIL>',
          phone: '+5511999999999'
        },
        teacher: {
          id: slot.teacherId,
          name: slot.teacher.name,
          email: slot.teacher.email
        },
        mentoring: {
          id: 'sample-booking-id',
          scheduledDate: sampleDate,
          startTime: slot.startTime,
          endTime: slot.endTime,
          duration: slot.duration
        },
        slot: {
          id: slot.id,
          dayOfWeek: slot.dayOfWeek,
          startTime: slot.startTime,
          endTime: slot.endTime
        }
      };

      // Process the template
      const processedTemplate = TemplateProcessor.processTemplate(template.template, context);

      return {
        success: true,
        message: processedTemplate.message,
        variables: processedTemplate.variables || {}
      };

    } catch (error) {
      console.error('Error previewing reminder template:', error);
      return {
        success: false,
        error: 'Failed to preview template'
      };
    }
  }

  /**
   * Get pending reminders
   */
  async getPendingReminders(): Promise<ServiceResult<MentoringReminderJob[]>> {
    try {
      const pendingReminders = await prisma.messageQueue.findMany({
        where: {
          messageType: 'mentoria',
          status: { in: ['pending', 'processing'] }
        },
        orderBy: [
          { priority: 'asc' },
          { scheduledFor: 'asc' }
        ]
      });

      const reminders: MentoringReminderJob[] = pendingReminders.map(reminder => {
        const metadata = JSON.parse(reminder.metadata || '{}');
        return {
          id: reminder.id,
          bookingId: metadata.bookingId || '',
          studentId: metadata.studentId || '',
          teacherId: metadata.teacherId || '',
          slotId: metadata.slotId || '',
          templateId: metadata.templateId || '',
          scheduledFor: reminder.scheduledFor,
          scheduledDateTime: metadata.mentoringDateTime ? new Date(metadata.mentoringDateTime) : new Date(),
          status: reminder.status as 'pending' | 'sent' | 'failed' | 'cancelled',
          attempts: reminder.attempts,
          lastAttempt: reminder.lastAttemptAt || undefined,
          errorMessage: reminder.errorMessage || undefined,
          messageText: reminder.messageText
        };
      });

      return {
        success: true,
        data: reminders
      };

    } catch (error) {
      console.error('Error getting pending reminders:', error);
      return {
        success: false,
        error: 'Failed to get pending reminders'
      };
    }
  }

  /**
   * Get scheduled reminders
   */
  async getScheduledReminders(): Promise<ServiceResult<MentoringReminderJob[]>> {
    try {
      const now = new Date();
      const next7Days = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);

      const scheduledReminders = await prisma.messageQueue.findMany({
        where: {
          messageType: 'mentoria',
          scheduledFor: {
            gte: now,
            lte: next7Days
          },
          status: { in: ['pending', 'processing', 'sent'] }
        },
        orderBy: [
          { scheduledFor: 'asc' }
        ]
      });

      const reminders: MentoringReminderJob[] = scheduledReminders.map(reminder => {
        const metadata = JSON.parse(reminder.metadata || '{}');
        return {
          id: reminder.id,
          bookingId: metadata.bookingId || '',
          studentId: metadata.studentId || '',
          teacherId: metadata.teacherId || '',
          slotId: metadata.slotId || '',
          templateId: metadata.templateId || '',
          scheduledFor: reminder.scheduledFor,
          scheduledDateTime: metadata.mentoringDateTime ? new Date(metadata.mentoringDateTime) : new Date(),
          status: reminder.status as 'pending' | 'sent' | 'failed' | 'cancelled',
          attempts: reminder.attempts,
          lastAttempt: reminder.lastAttemptAt || undefined,
          errorMessage: reminder.errorMessage || undefined,
          messageText: reminder.messageText
        };
      });

      return {
        success: true,
        data: reminders
      };

    } catch (error) {
      console.error('Error getting scheduled reminders:', error);
      return {
        success: false,
        error: 'Failed to get scheduled reminders'
      };
    }
  }

  /**
   * Cancel a reminder
   */
  async cancelReminder(reminderId: string): Promise<ServiceResult<boolean>> {
    try {
      await prisma.messageQueue.update({
        where: { id: reminderId },
        data: {
          status: 'cancelled'
        }
      });

      return {
        success: true,
        data: true
      };

    } catch (error) {
      console.error('Error cancelling reminder:', error);
      return {
        success: false,
        error: 'Failed to cancel reminder'
      };
    }
  }

  /**
   * Get reminder statistics
   */
  async getReminderStatistics(days: number = 7): Promise<ServiceResult<ReminderStatistics>> {
    try {
      const dateFrom = new Date();
      dateFrom.setDate(dateFrom.getDate() - days);

      const [totalReminders, sentReminders, failedReminders, pendingReminders] = await Promise.all([
        prisma.messageQueue.count({
          where: {
            messageType: 'mentoria',
            createdAt: { gte: dateFrom }
          }
        }),
        prisma.messageQueue.count({
          where: {
            messageType: 'mentoria',
            status: 'sent',
            createdAt: { gte: dateFrom }
          }
        }),
        prisma.messageQueue.count({
          where: {
            messageType: 'mentoria',
            status: 'failed',
            createdAt: { gte: dateFrom }
          }
        }),
        prisma.messageQueue.count({
          where: {
            messageType: 'mentoria',
            status: { in: ['pending', 'processing'] },
            createdAt: { gte: dateFrom }
          }
        })
      ]);

      const successRate = totalReminders > 0 
        ? ((sentReminders / totalReminders) * 100).toFixed(1)
        : '0';

      return {
        success: true,
        data: {
          totalReminders,
          sentReminders,
          failedReminders,
          pendingReminders,
          successRate
        }
      };

    } catch (error) {
      console.error('Error getting reminder statistics:', error);
      return {
        success: false,
        error: 'Failed to get reminder statistics'
      };
    }
  }

  /**
   * Create template context for a mentoring booking
   */
  private async createTemplateContext(booking: any): Promise<Record<string, any>> {
    return {
      student: {
        id: booking.student.id,
        name: booking.student.name,
        email: booking.student.email,
        phone: booking.student.phone
      },
      teacher: {
        id: booking.teacher.id,
        name: booking.teacher.name,
        email: booking.teacher.email
      },
      mentoring: {
        id: booking.id,
        scheduledDate: booking.scheduledDate,
        startTime: booking.startTime,
        endTime: booking.endTime,
        duration: booking.duration || booking.slot.duration
      },
      slot: {
        id: booking.slot.id,
        dayOfWeek: booking.slot.dayOfWeek,
        startTime: booking.slot.startTime,
        endTime: booking.slot.endTime,
        duration: booking.slot.duration
      }
    };
  }
}

export const mentoringReminderService = new MentoringReminderService();