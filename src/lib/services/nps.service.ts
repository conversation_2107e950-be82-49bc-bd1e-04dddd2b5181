import { prisma } from '@/lib/prisma';
import { randomBytes } from 'crypto';
import { addHours } from 'date-fns';

export interface NpsTokenData {
  id: string;
  token: string;
  lessonId: string;
  studentId: string;
  expiresAt: Date;
}

export interface NpsFeedbackData {
  rating: 'good' | 'neutral' | 'bad';
  anonymousText?: string;
}

export interface NpsFeedbackSubmission extends NpsFeedbackData {
  tokenId: string;
  lessonId: string;
  studentId: string;
}

// NPS token validity period in hours (default to 24 if not set in env)
const NPS_TOKEN_VALIDITY_HOURS = Number(process.env.NPS_TOKEN_VALIDITY_HOURS) || 24;

export class NpsService {
  /**
   * Generate a secure NPS feedback token for a student and lesson
   */
  static async generateNpsToken(lessonId: string, studentId: string): Promise<NpsTokenData> {
    try {
      // Check if token already exists for this lesson and student
      const existingToken = await prisma.npsToken.findUnique({
        where: {
          lessonId_studentId: {
            lessonId,
            studentId
          }
        }
      });

      if (existingToken && existingToken.expiresAt > new Date()) {
        // Return existing valid token
        return {
          id: existingToken.id,
          token: existingToken.token,
          lessonId: existingToken.lessonId,
          studentId: existingToken.studentId,
          expiresAt: existingToken.expiresAt
        };
      }

      // Generate new secure token
      const token = randomBytes(32).toString('hex');
      const expiresAt = addHours(new Date(), NPS_TOKEN_VALIDITY_HOURS);

      // Delete existing token if it exists (expired or used)
      if (existingToken) {
        await prisma.npsToken.delete({
          where: { id: existingToken.id }
        });
      }

      // Create new token
      const npsToken = await prisma.npsToken.create({
        data: {
          token,
          lessonId,
          studentId,
          expiresAt
        }
      });

      return {
        id: npsToken.id,
        token: npsToken.token,
        lessonId: npsToken.lessonId,
        studentId: npsToken.studentId,
        expiresAt: npsToken.expiresAt
      };
    } catch (error) {
      console.error('Error generating NPS token:', error);
      throw new Error('Failed to generate NPS feedback token');
    }
  }

  /**
   * Validate and retrieve NPS token data
   */
  static async validateNpsToken(token: string): Promise<NpsTokenData | null> {
    try {
      const npsToken = await prisma.npsToken.findUnique({
        where: { token },
        include: {
          lesson: {
            include: {
              class: {
                include: {
                  course: true
                }
              }
            }
          },
          student: true,
          feedback: true
        }
      });

      if (!npsToken) {
        return null;
      }

      // Check if token is expired
      if (npsToken.expiresAt < new Date()) {
        return null;
      }

      return {
        id: npsToken.id,
        token: npsToken.token,
        lessonId: npsToken.lessonId,
        studentId: npsToken.studentId,
        expiresAt: npsToken.expiresAt
      };
    } catch (error) {
      console.error('Error validating NPS token:', error);
      return null;
    }
  }

  /**
   * Submit NPS feedback
   */
  static async submitFeedback(data: NpsFeedbackSubmission): Promise<boolean> {
    try {
      // Validate token first
      const tokenData = await this.validateNpsToken(data.tokenId);
      if (!tokenData) {
        throw new Error('Invalid or expired NPS token');
      }

      // Check if feedback already exists
      const existingFeedback = await prisma.npsFeedback.findUnique({
        where: { tokenId: data.tokenId }
      });

      if (existingFeedback) {
        // Update existing feedback
        await prisma.npsFeedback.update({
          where: { tokenId: data.tokenId },
          data: {
            rating: data.rating,
            anonymousText: data.anonymousText || null,
            updatedAt: new Date()
          }
        });
      } else {
        // Create new feedback
        await prisma.npsFeedback.create({
          data: {
            tokenId: data.tokenId,
            lessonId: data.lessonId,
            studentId: data.studentId,
            rating: data.rating,
            anonymousText: data.anonymousText || null
          }
        });
      }

      // Mark token as used
      await prisma.npsToken.update({
        where: { id: tokenData.id },
        data: { usedAt: new Date() }
      });

      return true;
    } catch (error) {
      console.error('Error submitting NPS feedback:', error);
      throw error;
    }
  }

  /**
   * Get NPS feedback for a lesson
   */
  static async getLessonFeedback(lessonId: string) {
    try {
      return await prisma.npsFeedback.findMany({
        where: { lessonId },
        include: {
          student: {
            select: {
              id: true,
              name: true
            }
          },
          lesson: {
            include: {
              class: {
                include: {
                  course: true
                }
              }
            }
          }
        },
        orderBy: { submittedAt: 'desc' }
      });
    } catch (error) {
      console.error('Error getting lesson feedback:', error);
      throw error;
    }
  }

  /**
   * Get NPS statistics for a course
   */
  static async getCourseFeedbackStats(courseId: string) {
    try {
      const feedback = await prisma.npsFeedback.findMany({
        where: {
          lesson: {
            class: {
              courseId
            }
          }
        },
        select: {
          rating: true,
          submittedAt: true
        }
      });

      const total = feedback.length;
      const good = feedback.filter(f => f.rating === 'good').length;
      const neutral = feedback.filter(f => f.rating === 'neutral').length;
      const bad = feedback.filter(f => f.rating === 'bad').length;

      return {
        total,
        good,
        neutral,
        bad,
        goodPercentage: total > 0 ? Math.round((good / total) * 100) : 0,
        neutralPercentage: total > 0 ? Math.round((neutral / total) * 100) : 0,
        badPercentage: total > 0 ? Math.round((bad / total) * 100) : 0
      };
    } catch (error) {
      console.error('Error getting course feedback stats:', error);
      throw error;
    }
  }

  /**
   * Clean up expired NPS tokens
   */
  static async cleanupExpiredTokens(): Promise<number> {
    try {
      const result = await prisma.npsToken.deleteMany({
        where: {
          expiresAt: {
            lt: new Date()
          }
        }
      });

      console.log(`🧹 Cleaned up ${result.count} expired NPS tokens`);
      return result.count;
    } catch (error) {
      console.error('Error cleaning up expired NPS tokens:', error);
      throw error;
    }
  }

  /**
   * Get detailed token information for feedback form
   */
  static async getTokenDetails(token: string) {
    try {
      const npsToken = await prisma.npsToken.findUnique({
        where: { token },
        include: {
          lesson: {
            include: {
              class: {
                include: {
                  course: true
                }
              }
            }
          },
          student: {
            select: {
              id: true,
              name: true
            }
          },
          feedback: true
        }
      });

      if (!npsToken || npsToken.expiresAt < new Date()) {
        return null;
      }

      return npsToken;
    } catch (error) {
      console.error('Error getting token details:', error);
      return null;
    }
  }

  /**
   * Get NPS statistics for a teacher with detailed breakdown
   */
  static async getTeacherNpsStats(teacherId: string, filters?: {
    startDate?: Date;
    endDate?: Date;
    courseId?: string;
    classId?: string;
    studentId?: string;
  }) {
    try {
      const whereClause: any = {
        lesson: {
          OR: [
            { teacherId }, // Lesson has specific teacher
            {
              class: { teacherId }, // Class has teacher (lesson inherits)
              teacherId: null // But lesson doesn't override
            }
          ]
        }
      };

      // Apply filters
      if (filters?.startDate || filters?.endDate) {
        whereClause.submittedAt = {};
        if (filters.startDate) {
          whereClause.submittedAt.gte = filters.startDate;
        }
        if (filters.endDate) {
          whereClause.submittedAt.lte = filters.endDate;
        }
      }

      if (filters?.courseId) {
        whereClause.lesson.class.courseId = filters.courseId;
      }

      if (filters?.classId) {
        whereClause.lesson.classId = filters.classId;
      }

      if (filters?.studentId) {
        whereClause.studentId = filters.studentId;
      }

      const feedback = await prisma.npsFeedback.findMany({
        where: whereClause,
        include: {
          lesson: {
            include: {
              class: {
                include: {
                  course: true
                }
              },
              teacher: true
            }
          },
          student: true
        },
        orderBy: {
          submittedAt: 'desc'
        }
      });

      const total = feedback.length;
      const good = feedback.filter(f => f.rating === 'good').length;
      const neutral = feedback.filter(f => f.rating === 'neutral').length;
      const bad = feedback.filter(f => f.rating === 'bad').length;

      // Calculate NPS score (good - bad) / total * 100
      const npsScore = total > 0 ? Math.round(((good - bad) / total) * 100) : 0;

      return {
        total,
        good,
        neutral,
        bad,
        goodPercentage: total > 0 ? Math.round((good / total) * 100) : 0,
        neutralPercentage: total > 0 ? Math.round((neutral / total) * 100) : 0,
        badPercentage: total > 0 ? Math.round((bad / total) * 100) : 0,
        npsScore,
        feedback: feedback.map(f => ({
          id: f.id,
          rating: f.rating,
          anonymousText: f.anonymousText,
          submittedAt: f.submittedAt,
          lesson: {
            id: f.lesson.id,
            title: f.lesson.title,
            scheduledDate: f.lesson.scheduledDate,
            class: {
              id: f.lesson.class.id,
              name: f.lesson.class.name,
              course: {
                id: f.lesson.class.course.id,
                name: f.lesson.class.course.name
              }
            },
            teacher: f.lesson.teacher
          },
          student: {
            id: f.student.id,
            name: f.student.name
          }
        }))
      };
    } catch (error) {
      console.error('Error getting teacher NPS stats:', error);
      throw error;
    }
  }

  /**
   * Get NPS statistics for all teachers
   */
  static async getAllTeachersNpsStats(filters?: {
    startDate?: Date;
    endDate?: Date;
    courseId?: string;
    classId?: string;
  }) {
    try {
      const teachers = await prisma.teacher.findMany({
        where: {
          status: 'active'
        },
        include: {
          classes: {
            include: {
              course: true
            }
          }
        }
      });

      const teacherStats = await Promise.all(
        teachers.map(async (teacher) => {
          const stats = await this.getTeacherNpsStats(teacher.id, filters);
          return {
            teacher: {
              id: teacher.id,
              name: teacher.name,
              email: teacher.email,
              specialization: teacher.specialization
            },
            stats
          };
        })
      );

      // Sort by NPS score descending
      teacherStats.sort((a, b) => b.stats.npsScore - a.stats.npsScore);

      return teacherStats;
    } catch (error) {
      console.error('Error getting all teachers NPS stats:', error);
      throw error;
    }
  }

  /**
   * Get NPS breakdown by lesson for a teacher
   */
  static async getTeacherNpsByLesson(teacherId: string, filters?: {
    startDate?: Date;
    endDate?: Date;
    courseId?: string;
    classId?: string;
  }) {
    try {
      const whereClause: any = {
        lesson: {
          OR: [
            { teacherId },
            {
              class: { teacherId },
              teacherId: null
            }
          ]
        }
      };

      // Apply filters
      if (filters?.startDate || filters?.endDate) {
        whereClause.submittedAt = {};
        if (filters.startDate) {
          whereClause.submittedAt.gte = filters.startDate;
        }
        if (filters.endDate) {
          whereClause.submittedAt.lte = filters.endDate;
        }
      }

      if (filters?.courseId) {
        whereClause.lesson.class.courseId = filters.courseId;
      }

      if (filters?.classId) {
        whereClause.lesson.classId = filters.classId;
      }

      const feedback = await prisma.npsFeedback.findMany({
        where: whereClause,
        include: {
          lesson: {
            include: {
              class: {
                include: {
                  course: true
                }
              }
            }
          }
        }
      });

      // Group by lesson
      const lessonGroups = feedback.reduce((acc, f) => {
        const lessonId = f.lesson.id;
        if (!acc[lessonId]) {
          acc[lessonId] = {
            lesson: f.lesson,
            feedback: []
          };
        }
        acc[lessonId].feedback.push(f);
        return acc;
      }, {} as Record<string, any>);

      // Calculate stats for each lesson
      const lessonStats = Object.values(lessonGroups).map((group: any) => {
        const total = group.feedback.length;
        const good = group.feedback.filter((f: any) => f.rating === 'good').length;
        const neutral = group.feedback.filter((f: any) => f.rating === 'neutral').length;
        const bad = group.feedback.filter((f: any) => f.rating === 'bad').length;
        const npsScore = total > 0 ? Math.round(((good - bad) / total) * 100) : 0;

        return {
          lesson: {
            id: group.lesson.id,
            title: group.lesson.title,
            scheduledDate: group.lesson.scheduledDate,
            class: {
              id: group.lesson.class.id,
              name: group.lesson.class.name,
              course: {
                id: group.lesson.class.course.id,
                name: group.lesson.class.course.name
              }
            }
          },
          total,
          good,
          neutral,
          bad,
          goodPercentage: total > 0 ? Math.round((good / total) * 100) : 0,
          neutralPercentage: total > 0 ? Math.round((neutral / total) * 100) : 0,
          badPercentage: total > 0 ? Math.round((bad / total) * 100) : 0,
          npsScore
        };
      });

      // Sort by scheduled date descending
      lessonStats.sort((a, b) => new Date(b.lesson.scheduledDate).getTime() - new Date(a.lesson.scheduledDate).getTime());

      return lessonStats;
    } catch (error) {
      console.error('Error getting teacher NPS by lesson:', error);
      throw error;
    }
  }

  /**
   * Get NPS breakdown by class for a teacher
   */
  static async getTeacherNpsByClass(teacherId: string, filters?: {
    startDate?: Date;
    endDate?: Date;
    courseId?: string;
  }) {
    try {
      const whereClause: any = {
        lesson: {
          OR: [
            { teacherId },
            {
              class: { teacherId },
              teacherId: null
            }
          ]
        }
      };

      // Apply filters
      if (filters?.startDate || filters?.endDate) {
        whereClause.submittedAt = {};
        if (filters.startDate) {
          whereClause.submittedAt.gte = filters.startDate;
        }
        if (filters.endDate) {
          whereClause.submittedAt.lte = filters.endDate;
        }
      }

      if (filters?.courseId) {
        whereClause.lesson.class.courseId = filters.courseId;
      }

      const feedback = await prisma.npsFeedback.findMany({
        where: whereClause,
        include: {
          lesson: {
            include: {
              class: {
                include: {
                  course: true
                }
              }
            }
          }
        }
      });

      // Group by class
      const classGroups = feedback.reduce((acc, f) => {
        const classId = f.lesson.class.id;
        if (!acc[classId]) {
          acc[classId] = {
            class: f.lesson.class,
            feedback: []
          };
        }
        acc[classId].feedback.push(f);
        return acc;
      }, {} as Record<string, any>);

      // Calculate stats for each class
      const classStats = Object.values(classGroups).map((group: any) => {
        const total = group.feedback.length;
        const good = group.feedback.filter((f: any) => f.rating === 'good').length;
        const neutral = group.feedback.filter((f: any) => f.rating === 'neutral').length;
        const bad = group.feedback.filter((f: any) => f.rating === 'bad').length;
        const npsScore = total > 0 ? Math.round(((good - bad) / total) * 100) : 0;

        return {
          class: {
            id: group.class.id,
            name: group.class.name,
            startDate: group.class.startDate,
            course: {
              id: group.class.course.id,
              name: group.class.course.name
            }
          },
          total,
          good,
          neutral,
          bad,
          goodPercentage: total > 0 ? Math.round((good / total) * 100) : 0,
          neutralPercentage: total > 0 ? Math.round((neutral / total) * 100) : 0,
          badPercentage: total > 0 ? Math.round((bad / total) * 100) : 0,
          npsScore
        };
      });

      // Sort by start date descending
      classStats.sort((a, b) => new Date(b.class.startDate).getTime() - new Date(a.class.startDate).getTime());

      return classStats;
    } catch (error) {
      console.error('Error getting teacher NPS by class:', error);
      throw error;
    }
  }
}
