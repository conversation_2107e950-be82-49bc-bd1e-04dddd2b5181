import { google } from 'googleapis';

export interface GoogleCalendarEvent {
  id?: string;
  summary: string;
  description?: string;
  start: {
    dateTime: string;
    timeZone?: string;
  };
  end: {
    dateTime: string;
    timeZone?: string;
  };
  attendees?: Array<{
    email: string;
    displayName?: string;
  }>;
  location?: string;
  conferenceData?: {
    createRequest: {
      requestId: string;
      conferenceSolutionKey: {
        type: string;
      };
    };
  };
}

export interface CalendarAvailability {
  start: string;
  end: string;
  busy: Array<{
    start: string;
    end: string;
  }>;
}

class GoogleCalendarService {
  private auth: any;
  private calendar: any;

  constructor() {
    // Initialize Google Auth - in production this should use service account or OAuth
    this.auth = new google.auth.GoogleAuth({
      keyFile: process.env.GOOGLE_SERVICE_ACCOUNT_KEY_FILE,
      scopes: [
        'https://www.googleapis.com/auth/calendar',
        'https://www.googleapis.com/auth/calendar.events'
      ],
    });

    this.calendar = google.calendar({ version: 'v3', auth: this.auth });
  }

  // Create a new calendar event
  async createEvent(calendarId: string, event: GoogleCalendarEvent): Promise<{ success: boolean; eventId?: string; error?: string }> {
    try {
      const response = await this.calendar.events.insert({
        calendarId,
        requestBody: event,
        conferenceDataVersion: event.conferenceData ? 1 : undefined,
      });

      return {
        success: true,
        eventId: response.data.id
      };
    } catch (error) {
      console.error('Error creating Google Calendar event:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create event'
      };
    }
  }

  // Update an existing calendar event
  async updateEvent(calendarId: string, eventId: string, event: GoogleCalendarEvent): Promise<{ success: boolean; error?: string }> {
    try {
      await this.calendar.events.update({
        calendarId,
        eventId,
        requestBody: event,
        conferenceDataVersion: event.conferenceData ? 1 : undefined,
      });

      return { success: true };
    } catch (error) {
      console.error('Error updating Google Calendar event:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update event'
      };
    }
  }

  // Delete a calendar event
  async deleteEvent(calendarId: string, eventId: string): Promise<{ success: boolean; error?: string }> {
    try {
      await this.calendar.events.delete({
        calendarId,
        eventId,
      });

      return { success: true };
    } catch (error) {
      console.error('Error deleting Google Calendar event:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete event'
      };
    }
  }

  // Get calendar availability for a date range
  async getAvailability(calendarId: string, timeMin: string, timeMax: string): Promise<{ success: boolean; data?: CalendarAvailability; error?: string }> {
    try {
      const response = await this.calendar.freebusy.query({
        requestBody: {
          timeMin,
          timeMax,
          items: [{ id: calendarId }],
        },
      });

      const busyTimes = response.data.calendars?.[calendarId]?.busy || [];

      return {
        success: true,
        data: {
          start: timeMin,
          end: timeMax,
          busy: busyTimes.map((busy: any) => ({
            start: busy.start,
            end: busy.end,
          })),
        }
      };
    } catch (error) {
      console.error('Error checking calendar availability:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to check availability'
      };
    }
  }

  // List events in a date range
  async listEvents(calendarId: string, timeMin: string, timeMax: string): Promise<{ success: boolean; events?: any[]; error?: string }> {
    try {
      const response = await this.calendar.events.list({
        calendarId,
        timeMin,
        timeMax,
        singleEvents: true,
        orderBy: 'startTime',
      });

      return {
        success: true,
        events: response.data.items || []
      };
    } catch (error) {
      console.error('Error listing calendar events:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to list events'
      };
    }
  }

  // Generate Google Meet link for mentoring session
  createMentoringEvent(
    teacherEmail: string,
    studentEmail: string,
    studentName: string,
    startDateTime: string,
    endDateTime: string,
    courseName?: string
  ): GoogleCalendarEvent {
    const summary = courseName 
      ? `Mentoria - ${studentName} (${courseName})`
      : `Mentoria - ${studentName}`;

    const description = `
Sessão de mentoria individual

👨‍🎓 Aluno: ${studentName}
📧 Email: ${studentEmail}
${courseName ? `📚 Curso: ${courseName}\n` : ''}
🔗 Link da reunião será gerado automaticamente

Lembre-se:
• Prepare-se com antecedência
• Tenha materiais de apoio prontos
• Conecte-se 5 minutos antes do horário
    `.trim();

    return {
      summary,
      description,
      start: {
        dateTime: startDateTime,
        timeZone: 'America/Sao_Paulo',
      },
      end: {
        dateTime: endDateTime,
        timeZone: 'America/Sao_Paulo',
      },
      attendees: [
        {
          email: teacherEmail,
          displayName: 'Professor',
        },
        {
          email: studentEmail,
          displayName: studentName,
        },
      ],
      conferenceData: {
        createRequest: {
          requestId: `mentoring_${Date.now()}`,
          conferenceSolutionKey: {
            type: 'hangoutsMeet',
          },
        },
      },
    };
  }

  // Utility to format date for Google Calendar
  formatDateTimeForCalendar(date: Date): string {
    return date.toISOString();
  }

  // Get next available slot for a teacher
  async getNextAvailableSlot(
    calendarId: string,
    dayOfWeek: number, // 0 = Sunday, 1 = Monday, etc.
    startTime: string, // "HH:MM"
    duration: number, // minutes
    weeksAhead: number = 4
  ): Promise<{ success: boolean; availableSlots?: Date[]; error?: string }> {
    try {
      const now = new Date();
      const endDate = new Date();
      endDate.setDate(now.getDate() + (weeksAhead * 7));

      const availableSlots: Date[] = [];

      // Find all occurrences of the specified day of week
      for (let d = new Date(now); d <= endDate; d.setDate(d.getDate() + 1)) {
        if (d.getDay() === dayOfWeek && d >= now) {
          const [hours, minutes] = startTime.split(':').map(Number);
          const slotStart = new Date(d);
          slotStart.setHours(hours, minutes, 0, 0);

          // Skip if slot is in the past
          if (slotStart <= now) continue;

          const slotEnd = new Date(slotStart);
          slotEnd.setMinutes(slotEnd.getMinutes() + duration);

          // Check availability
          const availability = await this.getAvailability(
            calendarId,
            slotStart.toISOString(),
            slotEnd.toISOString()
          );

          if (availability.success && availability.data) {
            const isAvailable = availability.data.busy.length === 0;
            if (isAvailable) {
              availableSlots.push(slotStart);
            }
          }
        }
      }

      return {
        success: true,
        availableSlots
      };
    } catch (error) {
      console.error('Error finding available slots:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to find available slots'
      };
    }
  }
}

// Mock implementation for development when Google credentials are not available
class MockGoogleCalendarService {
  async createEvent(calendarId: string, event: GoogleCalendarEvent): Promise<{ success: boolean; eventId?: string; error?: string }> {
    console.log('Mock: Creating calendar event', { calendarId, event });
    return {
      success: true,
      eventId: `mock_event_${Date.now()}`
    };
  }

  async updateEvent(calendarId: string, eventId: string, event: GoogleCalendarEvent): Promise<{ success: boolean; error?: string }> {
    console.log('Mock: Updating calendar event', { calendarId, eventId, event });
    return { success: true };
  }

  async deleteEvent(calendarId: string, eventId: string): Promise<{ success: boolean; error?: string }> {
    console.log('Mock: Deleting calendar event', { calendarId, eventId });
    return { success: true };
  }

  async getAvailability(calendarId: string, timeMin: string, timeMax: string): Promise<{ success: boolean; data?: CalendarAvailability; error?: string }> {
    console.log('Mock: Checking availability', { calendarId, timeMin, timeMax });
    return {
      success: true,
      data: {
        start: timeMin,
        end: timeMax,
        busy: [] // Mock as always available
      }
    };
  }

  async listEvents(calendarId: string, timeMin: string, timeMax: string): Promise<{ success: boolean; events?: any[]; error?: string }> {
    console.log('Mock: Listing events', { calendarId, timeMin, timeMax });
    return {
      success: true,
      events: []
    };
  }

  createMentoringEvent(
    teacherEmail: string,
    studentEmail: string,
    studentName: string,
    startDateTime: string,
    endDateTime: string,
    courseName?: string
  ): GoogleCalendarEvent {
    const summary = courseName 
      ? `Mentoria - ${studentName} (${courseName})`
      : `Mentoria - ${studentName}`;

    return {
      summary,
      description: `Mock mentoring session with ${studentName}`,
      start: {
        dateTime: startDateTime,
        timeZone: 'America/Sao_Paulo',
      },
      end: {
        dateTime: endDateTime,
        timeZone: 'America/Sao_Paulo',
      },
      attendees: [
        { email: teacherEmail },
        { email: studentEmail, displayName: studentName },
      ],
    };
  }

  formatDateTimeForCalendar(date: Date): string {
    return date.toISOString();
  }

  async getNextAvailableSlot(
    calendarId: string,
    dayOfWeek: number,
    startTime: string,
    duration: number,
    weeksAhead: number = 4
  ): Promise<{ success: boolean; availableSlots?: Date[]; error?: string }> {
    console.log('Mock: Finding available slots', { calendarId, dayOfWeek, startTime, duration, weeksAhead });
    
    // Generate mock available slots
    const now = new Date();
    const availableSlots: Date[] = [];
    
    for (let week = 0; week < weeksAhead; week++) {
      const targetDate = new Date(now);
      targetDate.setDate(now.getDate() + (week * 7) + (dayOfWeek - now.getDay() + 7) % 7);
      
      const [hours, minutes] = startTime.split(':').map(Number);
      targetDate.setHours(hours, minutes, 0, 0);
      
      if (targetDate > now) {
        availableSlots.push(new Date(targetDate));
      }
    }
    
    return {
      success: true,
      availableSlots
    };
  }
}

// Export the appropriate service based on environment
export const googleCalendarService = process.env.GOOGLE_SERVICE_ACCOUNT_KEY_FILE 
  ? new GoogleCalendarService()
  : new MockGoogleCalendarService();