import dashboardService from '../dashboard.service';

// Mock fetch function
global.fetch = jest.fn();

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  clear: jest.fn()
};
Object.defineProperty(window, 'localStorage', { value: localStorageMock });

describe('DashboardService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue('mock-auth-token');
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('getDashboardData', () => {
    it('should return correct dashboard data structure with new metrics', async () => {
      const mockApiResponse = {
        success: true,
        data: {
          stats: {
            totalStudents: 150,
            newStudentsThisMonth: 12,
            activeClasses: 8,
            averageAttendanceRate: 87,
            upcomingLessons: 3,
            scheduledMentoringSessions: 25,
            mentoringCompletionPercentage: 65,
            pendingMentoringSessions: 48
          },
          classPerformance: [
            {
              name: 'Turma A',
              attendance: 92,
              students: 20,
              color: 'bg-green-500'
            },
            {
              name: 'Turma B',
              attendance: 85,
              students: 18,
              color: 'bg-blue-500'
            }
          ],
          upcomingLessons: [
            {
              time: '09:00',
              class: 'Turma A',
              students: 20
            },
            {
              time: '14:00',
              class: 'Turma B',
              students: 18
            }
          ],
          recentActivity: [
            {
              action: 'Novo aluno cadastrado',
              user: 'Sistema',
              time: '5 min atrás',
              icon: 'Users',
              color: 'text-blue-600'
            }
          ]
        }
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponse
      });

      const result = await dashboardService.getDashboardData();

      expect(fetch).toHaveBeenCalledWith('/api/dashboard', {
        headers: {
          'Authorization': 'Bearer mock-auth-token'
        }
      });

      expect(result).toEqual(mockApiResponse.data);
      expect(result.stats.totalStudents).toBe(150);
      expect(result.stats.newStudentsThisMonth).toBe(12);
      expect(result.stats.averageAttendanceRate).toBe(87);
      expect(result.stats.scheduledMentoringSessions).toBe(25);
      expect(result.stats.mentoringCompletionPercentage).toBe(65);
      expect(result.stats.pendingMentoringSessions).toBe(48);
    });

    it('should return fallback data when API call fails', async () => {
      (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      const result = await dashboardService.getDashboardData();

      expect(result).toEqual({
        stats: {
          totalStudents: 0,
          newStudentsThisMonth: 0,
          activeClasses: 0,
          averageAttendanceRate: 0,
          upcomingLessons: 0,
          scheduledMentoringSessions: 0,
          mentoringCompletionPercentage: 0,
          pendingMentoringSessions: 0
        },
        classPerformance: [],
        upcomingLessons: [],
        recentActivity: []
      });
    });

    it('should return fallback data when API returns invalid response', async () => {
      const mockApiResponse = {
        success: false,
        error: 'Unauthorized'
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponse
      });

      const result = await dashboardService.getDashboardData();

      expect(result.stats.totalStudents).toBe(0);
      expect(result.stats.newStudentsThisMonth).toBe(0);
      expect(result.stats.scheduledMentoringSessions).toBe(0);
    });

    it('should handle missing auth token', async () => {
      localStorageMock.getItem.mockReturnValue(null);

      const result = await dashboardService.getDashboardData();

      expect(fetch).toHaveBeenCalledWith('/api/dashboard', {
        headers: {
          'Authorization': 'Bearer null'
        }
      });
    });

    it('should validate mentoring metrics structure', async () => {
      const mockApiResponse = {
        success: true,
        data: {
          stats: {
            totalStudents: 100,
            newStudentsThisMonth: 8,
            activeClasses: 5,
            averageAttendanceRate: 90,
            upcomingLessons: 2,
            scheduledMentoringSessions: 30,
            mentoringCompletionPercentage: 75,
            pendingMentoringSessions: 20
          },
          classPerformance: [],
          upcomingLessons: [],
          recentActivity: []
        }
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponse
      });

      const result = await dashboardService.getDashboardData();

      // Validate that mentoring metrics are properly structured
      expect(typeof result.stats.scheduledMentoringSessions).toBe('number');
      expect(typeof result.stats.mentoringCompletionPercentage).toBe('number');
      expect(typeof result.stats.pendingMentoringSessions).toBe('number');
      
      // Validate ranges
      expect(result.stats.mentoringCompletionPercentage).toBeGreaterThanOrEqual(0);
      expect(result.stats.mentoringCompletionPercentage).toBeLessThanOrEqual(100);
      expect(result.stats.pendingMentoringSessions).toBeGreaterThanOrEqual(0);
      expect(result.stats.scheduledMentoringSessions).toBeGreaterThanOrEqual(0);
    });
  });

  describe('fetchWithAuth', () => {
    it('should include authorization header', async () => {
      const mockResponse = { data: 'test' };
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      });

      // Access private method for testing purposes
      const service = dashboardService as any;
      const result = await service.fetchWithAuth('/api/test');

      expect(fetch).toHaveBeenCalledWith('/api/test', {
        headers: {
          'Authorization': 'Bearer mock-auth-token'
        }
      });
      expect(result).toEqual(mockResponse);
    });

    it('should throw error when response is not ok', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 401
      });

      const service = dashboardService as any;
      
      await expect(service.fetchWithAuth('/api/test')).rejects.toThrow('HTTP error! status: 401');
    });
  });
});