import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { mentoringReminderService } from '../mentoring-reminder.service';

// Mock Prisma
vi.mock('../../prisma', () => ({
  prisma: {
    mentoringBooking: {
      findUnique: vi.fn(),
      update: vi.fn(),
      findMany: vi.fn(),
    },
    messageQueue: {
      create: vi.fn(),
      findFirst: vi.fn(),
      findMany: vi.fn(),
      update: vi.fn(),
      count: vi.fn(),
    },
    reminderTemplate: {
      findUnique: vi.fn(),
    },
    mentoringSlot: {
      findUnique: vi.fn(),
    },
  },
}));

// Mock TemplateProcessor
vi.mock('../../template-processor', () => ({
  TemplateProcessor: {
    processTemplate: vi.fn(() => ({
      message: 'Mocked processed message',
      variables: { nome_do_aluno: '<PERSON>' }
    })),
  },
}));

// Mock WhatsApp service
vi.mock('../../whatsapp', () => ({
  sendWhatsAppReminder: vi.fn(() => Promise.resolve({ 
    success: true, 
    messageId: 'mock-message-id' 
  })),
}));

describe('MentoringReminderService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('previewReminderTemplate', () => {
    it('should generate preview for valid template and slot', async () => {
      const mockTemplate = {
        id: 'template-1',
        template: 'Olá {{nome_do_aluno}}, sua mentoria com {{nome_professor}} está agendada para {{data_mentoria}} às {{hora_inicio_mentoria}}.',
      };

      const mockSlot = {
        id: 'slot-1',
        teacherId: 'teacher-1',
        startTime: '14:00',
        endTime: '15:00',
        teacher: {
          name: 'Prof. Silva',
          email: '<EMAIL>',
        },
      };

      const { prisma } = await import('../../prisma');
      
      // @ts-ignore
      prisma.reminderTemplate.findUnique.mockResolvedValue(mockTemplate);
      // @ts-ignore
      prisma.mentoringSlot.findUnique.mockResolvedValue(mockSlot);

      const result = await mentoringReminderService.previewReminderTemplate(
        'template-1',
        'slot-1'
      );

      expect(result.success).toBe(true);
      expect(result.message).toBe('Mocked processed message');
      expect(result.variables).toEqual({ nome_do_aluno: 'João Silva' });
    });

    it('should return error for non-existent template', async () => {
      const { prisma } = await import('../../prisma');
      
      // @ts-ignore
      prisma.reminderTemplate.findUnique.mockResolvedValue(null);

      const result = await mentoringReminderService.previewReminderTemplate(
        'nonexistent-template',
        'slot-1'
      );

      expect(result.success).toBe(false);
      expect(result.error).toBe('Template not found');
    });

    it('should return error for non-existent slot', async () => {
      const mockTemplate = {
        id: 'template-1',
        template: 'Test template',
      };

      const { prisma } = await import('../../prisma');
      
      // @ts-ignore
      prisma.reminderTemplate.findUnique.mockResolvedValue(mockTemplate);
      // @ts-ignore
      prisma.mentoringSlot.findUnique.mockResolvedValue(null);

      const result = await mentoringReminderService.previewReminderTemplate(
        'template-1',
        'nonexistent-slot'
      );

      expect(result.success).toBe(false);
      expect(result.error).toBe('Mentoring slot not found');
    });
  });

  describe('scheduleReminderForBooking', () => {
    it('should schedule reminder for valid booking', async () => {
      const mockBooking = {
        id: 'booking-1',
        studentId: 'student-1',
        teacherId: 'teacher-1',
        slotId: 'slot-1',
        scheduledDate: new Date('2024-01-20T14:00:00Z'),
        student: {
          id: 'student-1',
          name: 'João Silva',
          phone: '+5511999999999',
        },
        teacher: {
          id: 'teacher-1',
          name: 'Prof. Silva',
        },
        slot: {
          id: 'slot-1',
          reminderEnabled: true,
          reminderHoursBefore: 2,
          reminderTemplateId: 'template-1',
          reminderTemplate: {
            id: 'template-1',
            name: 'Lembrete Mentoria',
            template: 'Sua mentoria está agendada para hoje às {{hora_inicio_mentoria}}.',
          },
        },
      };

      const mockMessageQueue = {
        id: 'queue-1',
        recipientPhone: '+5511999999999',
        messageText: 'Mocked processed message',
        messageType: 'mentoria',
        scheduledFor: new Date('2024-01-20T12:00:00Z'),
      };

      const { prisma } = await import('../../prisma');
      
      // @ts-ignore
      prisma.mentoringBooking.findUnique.mockResolvedValue(mockBooking);
      // @ts-ignore
      prisma.messageQueue.findFirst.mockResolvedValue(null); // No existing reminder
      // @ts-ignore
      prisma.messageQueue.create.mockResolvedValue(mockMessageQueue);
      // @ts-ignore
      prisma.mentoringBooking.update.mockResolvedValue(mockBooking);

      const result = await mentoringReminderService.scheduleReminderForBooking('booking-1');

      expect(result.success).toBe(true);
      expect(result.data?.bookingId).toBe('booking-1');
      expect(result.data?.status).toBe('pending');
    });

    it('should return error for booking without reminder enabled', async () => {
      const mockBooking = {
        id: 'booking-1',
        slot: {
          reminderEnabled: false,
        },
      };

      const { prisma } = await import('../../prisma');
      
      // @ts-ignore
      prisma.mentoringBooking.findUnique.mockResolvedValue(mockBooking);

      const result = await mentoringReminderService.scheduleReminderForBooking('booking-1');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Reminders are not enabled for this mentoring slot');
    });

    it('should return error for student without phone', async () => {
      const mockBooking = {
        id: 'booking-1',
        student: {
          phone: null,
        },
        slot: {
          reminderEnabled: true,
          reminderTemplateId: 'template-1',
          reminderTemplate: { id: 'template-1' },
          reminderHoursBefore: 2,
        },
      };

      const { prisma } = await import('../../prisma');
      
      // @ts-ignore
      prisma.mentoringBooking.findUnique.mockResolvedValue(mockBooking);

      const result = await mentoringReminderService.scheduleReminderForBooking('booking-1');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Student does not have a phone number for reminders');
    });

    it('should return error for existing reminder', async () => {
      const mockBooking = {
        id: 'booking-1',
        scheduledDate: new Date('2024-01-20T14:00:00Z'),
        student: {
          phone: '+5511999999999',
        },
        slot: {
          reminderEnabled: true,
          reminderTemplateId: 'template-1',
          reminderTemplate: { id: 'template-1' },
          reminderHoursBefore: 2,
        },
      };

      const mockExistingReminder = {
        id: 'existing-reminder',
      };

      const { prisma } = await import('../../prisma');
      
      // @ts-ignore
      prisma.mentoringBooking.findUnique.mockResolvedValue(mockBooking);
      // @ts-ignore
      prisma.messageQueue.findFirst.mockResolvedValue(mockExistingReminder);

      const result = await mentoringReminderService.scheduleReminderForBooking('booking-1');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Reminder already scheduled for this booking');
    });
  });

  describe('getReminderStatistics', () => {
    it('should return statistics for the specified period', async () => {
      const { prisma } = await import('../../prisma');
      
      // @ts-ignore
      prisma.messageQueue.count
        .mockResolvedValueOnce(100) // total
        .mockResolvedValueOnce(85)  // sent
        .mockResolvedValueOnce(10)  // failed
        .mockResolvedValueOnce(5);  // pending

      const result = await mentoringReminderService.getReminderStatistics(7);

      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        totalReminders: 100,
        sentReminders: 85,
        failedReminders: 10,
        pendingReminders: 5,
        successRate: '85.0',
      });
    });

    it('should handle zero reminders', async () => {
      const { prisma } = await import('../../prisma');
      
      // @ts-ignore
      prisma.messageQueue.count
        .mockResolvedValueOnce(0)  // total
        .mockResolvedValueOnce(0)  // sent
        .mockResolvedValueOnce(0)  // failed
        .mockResolvedValueOnce(0); // pending

      const result = await mentoringReminderService.getReminderStatistics(7);

      expect(result.success).toBe(true);
      expect(result.data?.successRate).toBe('0');
    });
  });

  describe('cancelReminder', () => {
    it('should successfully cancel a reminder', async () => {
      const { prisma } = await import('../../prisma');
      
      // @ts-ignore
      prisma.messageQueue.update.mockResolvedValue({ 
        id: 'reminder-1', 
        status: 'cancelled' 
      });

      const result = await mentoringReminderService.cancelReminder('reminder-1');

      expect(result.success).toBe(true);
      expect(result.data).toBe(true);
      expect(prisma.messageQueue.update).toHaveBeenCalledWith({
        where: { id: 'reminder-1' },
        data: { status: 'cancelled' },
      });
    });
  });
});