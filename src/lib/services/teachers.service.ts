// Client-side service for teachers management

export interface Teacher {
  id: string;
  name: string;
  email: string | null;
  phone: string | null;
  specialization: string | null;
  status: string;
  notes: string | null;
  createdAt: string;
  updatedAt: string;
  _count?: {
    classes: number;
    lessons: number;
  };
}

export interface CreateTeacherData {
  name: string;
  email?: string;
  phone?: string;
  specialization?: string;
  status?: string;
  notes?: string;
}

export interface UpdateTeacherData {
  name?: string;
  email?: string;
  phone?: string;
  specialization?: string;
  status?: string;
  notes?: string;
}

export interface ServiceResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

class TeachersService {
  private async fetchWithAuth(url: string, options: RequestInit = {}) {
    const token = localStorage.getItem('auth_token');
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers
    };
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    const response = await fetch(url, {
      credentials: 'include',
      headers,
      ...options
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return response.json();
  }

  // Get all teachers with optional search
  async getTeachers(search?: string): Promise<ServiceResponse<Teacher[]>> {
    try {
      const url = search ? `/api/teachers?search=${encodeURIComponent(search)}` : '/api/teachers';
      const response = await this.fetchWithAuth(url);
      return response;
    } catch (error) {
      console.error('Error fetching teachers:', error);
      return {
        success: false,
        error: 'Failed to fetch teachers'
      };
    }
  }

  // Get teacher by ID
  async getTeacher(id: string): Promise<ServiceResponse<Teacher>> {
    try {
      const response = await this.fetchWithAuth(`/api/teachers/${id}`);
      return response;
    } catch (error) {
      console.error('Error fetching teacher:', error);
      return {
        success: false,
        error: 'Failed to fetch teacher'
      };
    }
  }

  // Create new teacher
  async createTeacher(data: CreateTeacherData): Promise<ServiceResponse<Teacher>> {
    try {
      const response = await this.fetchWithAuth('/api/teachers', {
        method: 'POST',
        body: JSON.stringify(data)
      });
      return response;
    } catch (error) {
      console.error('Error creating teacher:', error);
      return {
        success: false,
        error: 'Failed to create teacher'
      };
    }
  }

  // Update teacher
  async updateTeacher(id: string, data: UpdateTeacherData): Promise<ServiceResponse<Teacher>> {
    try {
      const response = await this.fetchWithAuth(`/api/teachers/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data)
      });
      return response;
    } catch (error) {
      console.error('Error updating teacher:', error);
      return {
        success: false,
        error: 'Failed to update teacher'
      };
    }
  }

  // Delete teacher
  async deleteTeacher(id: string): Promise<ServiceResponse<boolean>> {
    try {
      const response = await this.fetchWithAuth(`/api/teachers/${id}`, {
        method: 'DELETE'
      });
      return response;
    } catch (error) {
      console.error('Error deleting teacher:', error);
      return {
        success: false,
        error: 'Failed to delete teacher'
      };
    }
  }

  // Get teacher statistics
  async getTeacherStats(): Promise<ServiceResponse<{
    total: number;
    active: number;
    inactive: number;
    withClasses: number;
    withoutClasses: number;
  }>> {
    try {
      const response = await this.fetchWithAuth('/api/teachers/stats');
      return response;
    } catch (error) {
      console.error('Error fetching teacher stats:', error);
      return {
        success: false,
        error: 'Failed to fetch teacher statistics'
      };
    }
  }
}

export const teachersService = new TeachersService();