import { recoveryService } from './recovery.service';
import { prisma } from '@/lib/prisma';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface ReminderMessageData {
  studentName: string;
  courseName: string;
  missedLessonTitle: string;
  missedLessonDate: string;
  availableLessons: Array<{
    title: string;
    date: string;
    time: string;
  }>;
  recoveryLink: string;
}

class RecoverySchedulerService {
  /**
   * Process all pending recovery reminders
   * This should be called by a cron job or scheduled task
   */
  async processPendingReminders() {
    try {
      const pendingReminders = await recoveryService.getPendingReminders();
      
      if (pendingReminders.length === 0) {
        console.log('No pending recovery reminders to process');
        return [];
      }

      console.log(`Processing ${pendingReminders.length} pending recovery reminders`);
      
      const results = [];
      
      for (const reminder of pendingReminders) {
        try {
          const result = await this.processIndividualReminder(reminder);
          results.push(result);
        } catch (error) {
          console.error(`Error processing reminder ${reminder.id}:`, error);
          
          // Mark reminder as failed
          await recoveryService.markReminderAsFailed(
            reminder.id,
            error instanceof Error ? error.message : 'Unknown error'
          );
          
          results.push({
            reminderId: reminder.id,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
      
      return results;
    } catch (error) {
      console.error('Error processing pending reminders:', error);
      throw error;
    }
  }

  /**
   * Process an individual recovery reminder
   */
  private async processIndividualReminder(reminder: any) {
    try {
      // Generate OTP for this absence
      const otp = await recoveryService.generateRecoveryOtp(
        reminder.studentId,
        reminder.attendanceId
      );

      // Get available makeup lessons
      const availableLessons = await recoveryService.getAvailableMakeupLessons(
        reminder.attendance.lesson.class.course.id,
        reminder.attendance.lesson.scheduledDate
      );

      if (availableLessons.length === 0) {
        throw new Error('No available makeup lessons found');
      }

      // Build recovery link
      const baseUrl = process.env.NEXTAUTH_URL;
      if (!baseUrl) {
        if (process.env.NODE_ENV === 'production') {
          throw new Error('NEXTAUTH_URL environment variable must be set in production');
        }
      }
      const recoveryLink = `${baseUrl}/recovery?token=${otp.token}`;

      // Format lesson data for message
      const formattedLessons = availableLessons.slice(0, 5).map(lesson => ({
        title: lesson.title,
        date: format(lesson.scheduledDate, "dd/MM/yyyy", { locale: ptBR }),
        time: format(lesson.scheduledDate, "HH:mm", { locale: ptBR })
      }));

      // Prepare message data
      const messageData: ReminderMessageData = {
        studentName: reminder.student.name,
        courseName: reminder.attendance.lesson.class.course.name,
        missedLessonTitle: reminder.attendance.lesson.title,
        missedLessonDate: format(
          reminder.attendance.lesson.scheduledDate, 
          "dd/MM/yyyy", 
          { locale: ptBR }
        ),
        availableLessons: formattedLessons,
        recoveryLink
      };

      // Create message text
      const messageText = this.buildReminderMessage(messageData);

      // Queue WhatsApp message
      await this.queueWhatsAppMessage(reminder.student.phone, messageText);

      // Mark reminder as sent
      await recoveryService.markReminderAsSent(reminder.id, messageText);

      return {
        reminderId: reminder.id,
        success: true,
        studentName: reminder.student.name,
        phoneNumber: reminder.student.phone,
        messageLength: messageText.length
      };
    } catch (error) {
      console.error(`Error processing individual reminder:`, error);
      throw error;
    }
  }

  /**
   * Build the WhatsApp reminder message text
   */
  private buildReminderMessage(data: ReminderMessageData): string {
    const message = `🎓 *VOX STUDENT - Reposição de Aula*

Olá, ${data.studentName}!

Notamos que você faltou na aula de *${data.courseName}*:
📚 ${data.missedLessonTitle}
📅 ${data.missedLessonDate}

Você pode repor esta aula em uma das próximas turmas disponíveis:

${data.availableLessons.map((lesson, index) => 
  `${index + 1}. *${lesson.title}*\n   📅 ${lesson.date} às ${lesson.time}`
).join('\n\n')}

⏰ *Link para agendamento (válido por 24h):*
${data.recoveryLink}

✅ Clique no link para escolher sua aula de reposição.

Dúvidas? Entre em contato conosco!`;

    return message;
  }

  /**
   * Queue a WhatsApp message to be sent
   */
  private async queueWhatsAppMessage(phoneNumber: string, messageText: string) {
    try {
      // Add message to queue with priority for recovery reminders
      await prisma.messageQueue.create({
        data: {
          recipientPhone: phoneNumber,
          messageText,
          messageType: 'recovery_reminder',
          priority: 2, // High priority
          scheduledFor: new Date(),
          metadata: JSON.stringify({
            type: 'recovery_reminder',
            timestamp: new Date().toISOString()
          })
        }
      });

      console.log(`Queued recovery reminder message for ${phoneNumber}`);
    } catch (error) {
      console.error('Error queueing WhatsApp message:', error);
      throw error;
    }
  }

  /**
   * Manual trigger to process reminders (for testing)
   */
  async triggerManualProcessing() {
    console.log('Manually triggering recovery reminder processing...');
    return await this.processPendingReminders();
  }

  /**
   * Get statistics about recovery reminders
   */
  async getRecoveryReminderStats() {
    try {
      const [total, pending, sent, failed] = await Promise.all([
        prisma.recoveryReminder.count(),
        prisma.recoveryReminder.count({ where: { status: 'pending' } }),
        prisma.recoveryReminder.count({ where: { status: 'sent' } }),
        prisma.recoveryReminder.count({ where: { status: 'failed' } })
      ]);

      const successRate = total > 0 ? ((sent / total) * 100).toFixed(1) : '0';

      return {
        total,
        pending,
        sent,
        failed,
        successRate: `${successRate}%`
      };
    } catch (error) {
      console.error('Error getting recovery reminder stats:', error);
      throw error;
    }
  }

  /**
   * Clean up old reminders and OTPs
   */
  async cleanupOldData() {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      // Delete old sent/failed reminders
      const deletedReminders = await prisma.recoveryReminder.deleteMany({
        where: {
          OR: [
            {
              status: 'sent',
              sentAt: {
                lt: thirtyDaysAgo
              }
            },
            {
              status: 'failed',
              createdAt: {
                lt: thirtyDaysAgo
              }
            }
          ]
        }
      });

      // Delete expired/used OTPs
      const deletedOtps = await prisma.recoveryOtp.deleteMany({
        where: {
          OR: [
            {
              expiresAt: {
                lt: new Date()
              }
            },
            {
              usedAt: {
                not: null,
                lt: thirtyDaysAgo
              }
            }
          ]
        }
      });

      console.log(`Cleanup completed: ${deletedReminders.count} reminders, ${deletedOtps.count} OTPs deleted`);

      return {
        deletedReminders: deletedReminders.count,
        deletedOtps: deletedOtps.count
      };
    } catch (error) {
      console.error('Error during cleanup:', error);
      throw error;
    }
  }
}

export const recoverySchedulerService = new RecoverySchedulerService();