// import { ApiClient } from './api-client'; // TODO: Implement api-client

export interface TeacherNpsFilters {
  startDate?: string;
  endDate?: string;
  courseId?: string;
  classId?: string;
  studentId?: string;
}

export interface NpsStats {
  total: number;
  good: number;
  neutral: number;
  bad: number;
  goodPercentage: number;
  neutralPercentage: number;
  badPercentage: number;
  npsScore: number;
}

export interface TeacherNpsData {
  teacher: {
    id: string;
    name: string;
    email?: string;
    specialization?: string;
  };
  stats: NpsStats & {
    feedback: Array<{
      id: string;
      rating: 'good' | 'neutral' | 'bad';
      anonymousText?: string;
      submittedAt: string;
      lesson: {
        id: string;
        title: string;
        scheduledDate: string;
        class: {
          id: string;
          name: string;
          course: {
            id: string;
            name: string;
          };
        };
        teacher?: {
          id: string;
          name: string;
        };
      };
      student: {
        id: string;
        name: string;
      };
    }>;
  };
}

export interface LessonNpsData {
  lesson: {
    id: string;
    title: string;
    scheduledDate: string;
    class: {
      id: string;
      name: string;
      course: {
        id: string;
        name: string;
      };
    };
  };
  total: number;
  good: number;
  neutral: number;
  bad: number;
  goodPercentage: number;
  neutralPercentage: number;
  badPercentage: number;
  npsScore: number;
}

export interface ClassNpsData {
  class: {
    id: string;
    name: string;
    startDate: string;
    course: {
      id: string;
      name: string;
    };
  };
  total: number;
  good: number;
  neutral: number;
  bad: number;
  goodPercentage: number;
  neutralPercentage: number;
  badPercentage: number;
  npsScore: number;
}

export class TeacherNpsService extends ApiClient {
  /**
   * Get NPS statistics for all teachers
   */
  async getAllTeachersNps(filters?: TeacherNpsFilters): Promise<{
    success: boolean;
    data?: TeacherNpsData[];
    error?: string;
  }> {
    try {
      const params = new URLSearchParams();
      if (filters?.startDate) params.append('startDate', filters.startDate);
      if (filters?.endDate) params.append('endDate', filters.endDate);
      if (filters?.courseId) params.append('courseId', filters.courseId);
      if (filters?.classId) params.append('classId', filters.classId);
      if (filters?.studentId) params.append('studentId', filters.studentId);

      const url = `/api/nps/teachers${params.toString() ? `?${params.toString()}` : ''}`;
      return await this.fetchWithAuth(url);
    } catch (error) {
      console.error('Error fetching all teachers NPS:', error);
      return {
        success: false,
        error: 'Failed to fetch teachers NPS data'
      };
    }
  }

  /**
   * Get NPS statistics for a specific teacher
   */
  async getTeacherNps(teacherId: string, filters?: TeacherNpsFilters): Promise<{
    success: boolean;
    data?: NpsStats & { feedback: any[] };
    error?: string;
  }> {
    try {
      const params = new URLSearchParams();
      params.append('teacherId', teacherId);
      if (filters?.startDate) params.append('startDate', filters.startDate);
      if (filters?.endDate) params.append('endDate', filters.endDate);
      if (filters?.courseId) params.append('courseId', filters.courseId);
      if (filters?.classId) params.append('classId', filters.classId);
      if (filters?.studentId) params.append('studentId', filters.studentId);

      const url = `/api/nps/teachers?${params.toString()}`;
      return await this.fetchWithAuth(url);
    } catch (error) {
      console.error('Error fetching teacher NPS:', error);
      return {
        success: false,
        error: 'Failed to fetch teacher NPS data'
      };
    }
  }

  /**
   * Get NPS breakdown by lesson for a teacher
   */
  async getTeacherNpsByLesson(teacherId: string, filters?: TeacherNpsFilters): Promise<{
    success: boolean;
    data?: LessonNpsData[];
    error?: string;
  }> {
    try {
      const params = new URLSearchParams();
      params.append('teacherId', teacherId);
      params.append('breakdown', 'lesson');
      if (filters?.startDate) params.append('startDate', filters.startDate);
      if (filters?.endDate) params.append('endDate', filters.endDate);
      if (filters?.courseId) params.append('courseId', filters.courseId);
      if (filters?.classId) params.append('classId', filters.classId);

      const url = `/api/nps/teachers?${params.toString()}`;
      return await this.fetchWithAuth(url);
    } catch (error) {
      console.error('Error fetching teacher NPS by lesson:', error);
      return {
        success: false,
        error: 'Failed to fetch teacher NPS by lesson'
      };
    }
  }

  /**
   * Get NPS breakdown by class for a teacher
   */
  async getTeacherNpsByClass(teacherId: string, filters?: TeacherNpsFilters): Promise<{
    success: boolean;
    data?: ClassNpsData[];
    error?: string;
  }> {
    try {
      const params = new URLSearchParams();
      params.append('teacherId', teacherId);
      params.append('breakdown', 'class');
      if (filters?.startDate) params.append('startDate', filters.startDate);
      if (filters?.endDate) params.append('endDate', filters.endDate);
      if (filters?.courseId) params.append('courseId', filters.courseId);

      const url = `/api/nps/teachers?${params.toString()}`;
      return await this.fetchWithAuth(url);
    } catch (error) {
      console.error('Error fetching teacher NPS by class:', error);
      return {
        success: false,
        error: 'Failed to fetch teacher NPS by class'
      };
    }
  }
}

// Export singleton instance
export const teacherNpsService = new TeacherNpsService();
