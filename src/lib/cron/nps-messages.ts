import { NpsMessagingService } from '@/lib/services/nps-messaging.service';

/**
 * Cron job to process pending NPS feedback messages
 * This should be scheduled to run every 15 minutes
 * 
 * Example usage with node-cron:
 * import cron from 'node-cron';
 * cron.schedule('*/15 * * * *', processNpsMessages); // Every 15 minutes
 */
export async function processNpsMessages() {
  try {
    console.log('Starting NPS messages processing job...');
    
    const startTime = Date.now();
    const results = await NpsMessagingService.processPendingNpsMessages();
    const endTime = Date.now();
    
    console.log('NPS messages processing completed:', {
      processed: results.processed,
      sent: results.sent,
      failed: results.failed,
      executionTime: `${endTime - startTime}ms`
    });
    
    return {
      success: true,
      ...results,
      executionTime: endTime - startTime
    };
  } catch (error) {
    console.error('❌ NPS messages processing failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      processed: 0,
      sent: 0,
      failed: 0
    };
  }
}

/**
 * Cleanup job to remove old NPS data
 * This should be scheduled to run daily
 * 
 * Example usage with node-cron:
 * cron.schedule('0 3 * * *', cleanupNpsData); // Daily at 3 AM
 */
export async function cleanupNpsData() {
  try {
    console.log('Starting NPS data cleanup job...');
    
    const startTime = Date.now();
    const result = await NpsMessagingService.cleanupOldNpsData();
    const endTime = Date.now();
    
    console.log('NPS data cleanup completed:', {
      deletedMessages: result.deletedMessages,
      deletedTokens: result.deletedTokens,
      executionTime: `${endTime - startTime}ms`
    });
    
    return {
      success: true,
      ...result,
      executionTime: endTime - startTime
    };
  } catch (error) {
    console.error('❌ NPS data cleanup failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      deletedMessages: 0,
      deletedTokens: 0
    };
  }
}

// Export combined jobs object for easier import
export const npsJobs = {
  processMessages: processNpsMessages,
  cleanupData: cleanupNpsData
};
