import { recoverySchedulerService } from '@/lib/services/recovery-scheduler.service';

/**
 * Cron job to process pending recovery reminders
 * This should be scheduled to run every hour (or as needed)
 * 
 * Example usage with node-cron:
 * import cron from 'node-cron';
 * cron.schedule('0 * * * *', processRecoveryReminders); // Every hour
 */
export async function processRecoveryReminders() {
  try {
    console.log('Starting recovery reminders processing job...');
    
    const startTime = Date.now();
    const results = await recoverySchedulerService.processPendingReminders();
    const endTime = Date.now();
    
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;
    
    console.log('Recovery reminders processing completed:', {
      totalProcessed: results.length,
      successful: successCount,
      failed: failureCount,
      executionTime: `${endTime - startTime}ms`
    });
    
    return {
      success: true,
      totalProcessed: results.length,
      successful: successCount,
      failed: failureCount,
      executionTime: endTime - startTime,
      results
    };
  } catch (error) {
    console.error('Recovery reminders cron job failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Cleanup job to remove old recovery data
 * This should be scheduled to run daily
 * 
 * Example usage with node-cron:
 * cron.schedule('0 2 * * *', cleanupRecoveryData); // Daily at 2 AM
 */
export async function cleanupRecoveryData() {
  try {
    console.log('Starting recovery data cleanup job...');
    
    const startTime = Date.now();
    const result = await recoverySchedulerService.cleanupOldData();
    const endTime = Date.now();
    
    console.log('Recovery data cleanup completed:', {
      deletedReminders: result.deletedReminders,
      deletedOtps: result.deletedOtps,
      executionTime: `${endTime - startTime}ms`
    });
    
    return {
      success: true,
      ...result,
      executionTime: endTime - startTime
    };
  } catch (error) {
    console.error('Recovery cleanup cron job failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Export for manual execution or testing
export const recoveryJobs = {
  processReminders: processRecoveryReminders,
  cleanupData: cleanupRecoveryData
};