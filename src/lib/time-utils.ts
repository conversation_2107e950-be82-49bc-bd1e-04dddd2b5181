import { format, addMinutes } from 'date-fns';
import { ptBR } from 'date-fns/locale';

/**
 * Formata uma data para exibir apenas o horário (HH:mm)
 */
export function formatTime(dateString: string): string {
  return format(new Date(dateString), "HH:mm", { locale: ptBR });
}

/**
 * Calcula e retorna o horário final da aula baseado na duração
 */
export function calculateEndTime(startTime: string, durationMinutes: number): string {
  const startDate = new Date(startTime);
  const endDate = addMinutes(startDate, durationMinutes);
  return format(endDate, "HH:mm", { locale: ptBR });
}

/**
 * Formata horário de início e fim da aula
 */
export function formatLessonTimeRange(startTime: string, durationMinutes: number): string {
  const start = formatTime(startTime);
  const end = calculateEndTime(startTime, durationMinutes);
  return `${start} - ${end}`;
}

/**
 * Converte minutos em formato mais legível (ex: 180min -> 3h)
 */
export function formatDuration(minutes: number): string {
  if (minutes < 60) {
    return `${minutes}min`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (remainingMinutes === 0) {
    return `${hours}h`;
  }
  
  return `${hours}h${remainingMinutes}min`;
}

/**
 * Obtém a duração padrão da aula baseada no curso
 */
export function getDefaultLessonDuration(course?: { lessonDuration?: number }): number {
  return course?.lessonDuration || 180; // 3 horas por padrão
}