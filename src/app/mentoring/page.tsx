'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState, useCallback } from 'react';
import { toast } from 'sonner';
import { mentoringService, MentoringSlot } from '@/lib/services/mentoring.service';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';

// Components
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// Icons
import { 
  Loader2, Calendar, Clock, User, MessageCircle, CheckCircle,
  CalendarDays, ArrowLeft, Plus, Search
} from 'lucide-react';

interface AvailableDate {
  date: string;
  dayOfWeek: string;
  slots: Array<{
    startTime: string;
    endTime: string;
    available: boolean;
  }>;
}

interface BookingFormData {
  slotId: string;
  courseId: string;
  scheduledDate: string;
  scheduledTime: string;
  studentMessage: string;
  courseContext: string;
}

export default function MentoringPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const authenticatedFetch = useAuthenticatedFetch();
  
  // State
  const [availableSlots, setAvailableSlots] = useState<MentoringSlot[]>([]);
  const [selectedSlot, setSelectedSlot] = useState<MentoringSlot | null>(null);
  const [availableDates, setAvailableDates] = useState<AvailableDate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isBooking, setIsBooking] = useState(false);
  const [showBookingForm, setShowBookingForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const [bookingForm, setBookingForm] = useState<BookingFormData>({
    slotId: '',
    courseId: '',
    scheduledDate: '',
    scheduledTime: '',
    studentMessage: '',
    courseContext: ''
  });

  // Data fetching
  const fetchAvailableSlots = useCallback(async (search?: string) => {
    try {
      setIsLoading(true);

      const url = search 
        ? `/api/mentoring/slots?search=${encodeURIComponent(search)}&status=active`
        : '/api/mentoring/slots?status=active';

      const response = await authenticatedFetch(url);

      if (!response.ok) {
        throw new Error('Falha ao carregar mentorias disponíveis');
      }

      const result = await response.json();
      setAvailableSlots(result.data || []);
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Erro ao carregar mentorias');
    } finally {
      setIsLoading(false);
    }
  }, [authenticatedFetch]);

  const fetchAvailableDates = useCallback(async (slotId: string) => {
    try {
      const response = await authenticatedFetch(`/api/mentoring/slots/${slotId}/available-dates?weeksAhead=4`);

      if (!response.ok) {
        throw new Error('Falha ao carregar datas disponíveis');
      }

      const result = await response.json();
      setAvailableDates(result.data || []);
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Erro ao carregar datas');
    }
  }, [authenticatedFetch]);

  // Effects
  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
      return;
    }

    if (user) {
      fetchAvailableSlots();
    }
  }, [user, loading, router, fetchAvailableSlots]);

  // Debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchAvailableSlots(searchTerm);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, fetchAvailableSlots]);

  // Handlers
  const handleSelectSlot = (slot: MentoringSlot) => {
    setSelectedSlot(slot);
    setBookingForm(prev => ({ ...prev, slotId: slot.id }));
    fetchAvailableDates(slot.id);
    setShowBookingForm(true);
  };

  const handleBooking = async () => {
    if (!bookingForm.slotId || !bookingForm.scheduledDate || !bookingForm.scheduledTime) {
      toast.error('Por favor, preencha todos os campos obrigatórios');
      return;
    }

    try {
      setIsBooking(true);

      const scheduledDateTime = `${bookingForm.scheduledDate}T${bookingForm.scheduledTime}:00`;

      const payload = {
        slotId: bookingForm.slotId,
        studentId: user?.id,
        courseId: bookingForm.courseId || undefined,
        scheduledDateTime,
        studentMessage: bookingForm.studentMessage.trim() || undefined,
        courseContext: bookingForm.courseContext.trim() || undefined,
      };

      const response = await authenticatedFetch('/api/mentoring/bookings', {
        method: 'POST',
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Falha ao agendar mentoria');
      }

      toast.success('Mentoria agendada com sucesso!');
      
      // Reset form and go back to slots
      setShowBookingForm(false);
      setSelectedSlot(null);
      setBookingForm({
        slotId: '',
        courseId: '',
        scheduledDate: '',
        scheduledTime: '',
        studentMessage: '',
        courseContext: ''
      });
      
      // Refresh available slots
      fetchAvailableSlots(searchTerm);
      
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Erro ao agendar mentoria');
    } finally {
      setIsBooking(false);
    }
  };

  // Utility functions
  const getDayOfWeekLabel = (dayOfWeek: string) => {
    const days = {
      'monday': 'Segunda-feira',
      'tuesday': 'Terça-feira', 
      'wednesday': 'Quarta-feira',
      'thursday': 'Quinta-feira',
      'friday': 'Sexta-feira',
      'saturday': 'Sábado',
      'sunday': 'Domingo'
    };
    return days[dayOfWeek as keyof typeof days] || dayOfWeek;
  };

  const formatTime = (time: string) => {
    return time.slice(0, 5);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="mx-auto animate-spin" size={48} />
          <p className="mt-4 text-muted-foreground">Carregando...</p>
        </div>
      </div>
    );
  }

  // Booking form view
  if (showBookingForm && selectedSlot) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
        <div className="w-full max-w-4xl mx-auto">
          {/* Header */}
          <div className="bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-2xl p-8 text-white shadow-xl mb-8">
            <div className="flex items-center space-x-4">
              <Button 
                onClick={() => setShowBookingForm(false)} 
                variant="outline" 
                size="sm" 
                className="bg-white/20 text-white border-white/30 hover:bg-white/30"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar
              </Button>
              <div>
                <h1 className="text-3xl font-bold mb-2">Agendar Mentoria</h1>
                <p className="text-white/90">
                  Com {selectedSlot.teacher?.name} - {getDayOfWeekLabel(selectedSlot.dayOfWeek)}
                </p>
              </div>
            </div>
          </div>

          {/* Booking Form */}
          <Card className="shadow-xl border-0">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="w-6 h-6 mr-2 text-[#667eea]" />
                Agendar Sua Mentoria
              </CardTitle>
              <CardDescription>
                Selecione uma data e horário disponível para sua mentoria
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Available Dates */}
              <div>
                <label className="text-sm font-medium mb-3 block">Datas Disponíveis</label>
                <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
                  {availableDates.map((dateOption) => (
                    <Card 
                      key={dateOption.date}
                      className={`cursor-pointer transition-all ${
                        bookingForm.scheduledDate === dateOption.date
                          ? 'ring-2 ring-[#667eea] bg-blue-50'
                          : 'hover:shadow-md'
                      }`}
                      onClick={() => {
                        setBookingForm(prev => ({ 
                          ...prev, 
                          scheduledDate: dateOption.date,
                          scheduledTime: '' // Reset time when date changes
                        }));
                      }}
                    >
                      <CardContent className="p-4">
                        <div className="text-sm font-medium">
                          {formatDate(dateOption.date)}
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          {dateOption.slots.filter(s => s.available).length} horários disponíveis
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Available Times */}
              {bookingForm.scheduledDate && (
                <div>
                  <label className="text-sm font-medium mb-3 block">Horários Disponíveis</label>
                  <div className="grid gap-2 grid-cols-3 md:grid-cols-4 lg:grid-cols-6">
                    {availableDates
                      .find(d => d.date === bookingForm.scheduledDate)
                      ?.slots.filter(slot => slot.available)
                      .map((timeSlot) => (
                        <Button
                          key={timeSlot.startTime}
                          variant={bookingForm.scheduledTime === timeSlot.startTime ? "default" : "outline"}
                          size="sm"
                          onClick={() => setBookingForm(prev => ({ 
                            ...prev, 
                            scheduledTime: timeSlot.startTime 
                          }))}
                          className="text-xs"
                        >
                          {formatTime(timeSlot.startTime)}
                        </Button>
                      ))
                    }
                  </div>
                </div>
              )}

              {/* Course Context */}
              <div>
                <label className="text-sm font-medium mb-2 block">Contexto do Curso</label>
                <Input
                  placeholder="Ex: React avançado, JavaScript básico..."
                  value={bookingForm.courseContext}
                  onChange={(e) => setBookingForm(prev => ({ 
                    ...prev, 
                    courseContext: e.target.value 
                  }))}
                />
              </div>

              {/* Student Message */}
              <div>
                <label className="text-sm font-medium mb-2 block">Mensagem (Opcional)</label>
                <Textarea
                  placeholder="Descreva suas dúvidas ou o que gostaria de revisar na mentoria..."
                  value={bookingForm.studentMessage}
                  onChange={(e) => setBookingForm(prev => ({ 
                    ...prev, 
                    studentMessage: e.target.value 
                  }))}
                  rows={3}
                />
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-4">
                <Button
                  variant="outline"
                  onClick={() => setShowBookingForm(false)}
                >
                  Cancelar
                </Button>
                <Button
                  onClick={handleBooking}
                  disabled={!bookingForm.scheduledDate || !bookingForm.scheduledTime || isBooking}
                  className="bg-gradient-to-r from-[#667eea] to-[#764ba2] hover:from-[#5a6fd8] hover:to-[#6b4190]"
                >
                  {isBooking ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Agendando...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Confirmar Agendamento
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Main slots view
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="w-full max-w-[1600px] mx-auto">
        {/* Header */}
        <div className="bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-2xl p-8 text-white shadow-xl mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button 
                onClick={() => router.push('/')} 
                variant="outline" 
                size="sm" 
                className="bg-white/20 text-white border-white/30 hover:bg-white/30"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Início
              </Button>
              <div>
                <h1 className="text-3xl font-bold mb-2 flex items-center">
                  <CalendarDays className="w-8 h-8 mr-3" />
                  Mentorias Disponíveis
                </h1>
                <p className="text-white/90">
                  Agende uma mentoria individual com nossos professores
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Search */}
        <Card className="mb-6 shadow-lg border-0">
          <CardContent className="pt-6">
            <div className="flex gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Buscar por professor ou matéria..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="border-2 border-gray-200 focus:border-[#667eea] focus:ring-4 focus:ring-[#667eea]/20 transition-all duration-300"
                />
              </div>
              <Button 
                onClick={() => fetchAvailableSlots(searchTerm)}
                className="bg-gradient-to-r from-[#667eea] to-[#764ba2] hover:from-[#5a6fd8] hover:to-[#6b4190] text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300"
              >
                <Search className="w-4 h-4 mr-2" />
                Buscar
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Available Slots */}
        <Card className="shadow-xl border-0">
          <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg">
            <CardTitle className="text-2xl font-bold text-gray-900 flex items-center">
              <CalendarDays className="w-6 h-6 mr-2 text-[#667eea]" />
              Professores Disponíveis
            </CardTitle>
            <CardDescription className="text-lg">
              {availableSlots.length} professor{availableSlots.length !== 1 ? 'es' : ''} disponível{availableSlots.length !== 1 ? 'is' : ''} para mentoria
            </CardDescription>
          </CardHeader>
          <CardContent className="p-6">
            {isLoading ? (
              <div className="text-center py-12">
                <Loader2 className="mx-auto animate-spin" size={32} />
                <p className="mt-4 text-muted-foreground">Carregando professores...</p>
              </div>
            ) : availableSlots.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-20 h-20 bg-gradient-to-br from-[#667eea] to-[#764ba2] rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-xl">
                  <CalendarDays className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  Nenhum professor disponível
                </h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  {searchTerm ? 'Tente uma busca diferente ou aguarde novas disponibilidades.' : 'Aguarde enquanto os professores configuram suas agendas.'}
                </p>
              </div>
            ) : (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {availableSlots.map((slot) => (
                  <Card key={slot.id} className="hover:shadow-lg transition-shadow border-2 hover:border-[#667eea]/30">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <User className="w-5 h-5 mr-2 text-[#667eea]" />
                          <CardTitle className="text-lg">{slot.teacher?.name}</CardTitle>
                        </div>
                        <Badge variant="outline" className="text-[#667eea] border-[#667eea]">
                          {getDayOfWeekLabel(slot.dayOfWeek)}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Clock className="w-4 h-4 mr-2" />
                          {formatTime(slot.startTime)} - {formatTime(slot.endTime)} ({slot.duration} min)
                        </div>
                        
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Calendar className="w-4 h-4 mr-2" />
                          Máx. {slot.maxBookingsPerWeek} agendamentos/semana
                        </div>

                        {slot.teacher?.email && (
                          <div className="flex items-center text-sm text-muted-foreground">
                            <MessageCircle className="w-4 h-4 mr-2" />
                            {slot.teacher.email}
                          </div>
                        )}

                        <Button
                          onClick={() => handleSelectSlot(slot)}
                          className="w-full bg-gradient-to-r from-[#667eea] to-[#764ba2] hover:from-[#5a6fd8] hover:to-[#6b4190] text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300"
                        >
                          <Plus className="w-4 h-4 mr-2" />
                          Agendar Mentoria
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}