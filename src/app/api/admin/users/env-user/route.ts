import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { adminConfigService } from '@/lib/services/admin-config.service';

/**
 * Handle updates to environment users
 * POST /api/admin/users/env-user
 */
export async function POST(request: NextRequest) {
  try {
    // Verify admin access
    const user = await verifyAdminAccess(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Acesso negado' },
        { status: 401 }
      );
    }

    const { email, fullName } = await request.json();

    // Validate input
    if (!email || !fullName) {
      return NextResponse.json(
        { error: 'Email e nome completo são obrigatórios' },
        { status: 400 }
      );
    }

    // Verify this is actually an environment user
    if (!adminConfigService.isEnvUser(email)) {
      return NextResponse.json(
        { error: 'Este usuário não está definido nas configurações do sistema' },
        { status: 400 }
      );
    }

    // Get the tuple data for role and phone
    const tuple = adminConfigService.getSuperAdminTupleByEmail(email);
    if (!tuple) {
      return NextResponse.json(
        { error: 'Configuração de usuário não encontrada' },
        { status: 400 }
      );
    }

    // Check if user already exists in database
    let existingUser = await prisma.user.findUnique({
      where: { email },
      include: { profile: true }
    });

    if (existingUser) {
      // Update existing user's profile
      await prisma.userProfile.update({
        where: { userId: existingUser.id },
        data: { fullName }
      });
    } else {
      // Create new user and profile
      existingUser = await prisma.user.create({
        data: {
          email,
          profile: {
            create: {
              fullName,
              role: 'super_admin', // Environment users are always super admins
              phone: tuple.phone || undefined
            }
          }
        },
        include: { profile: true }
      });
    }

    return NextResponse.json({
      message: 'Usuário atualizado com sucesso',
      user: existingUser
    });

  } catch (error) {
    console.error('Error updating environment user:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}