import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import jwt from 'jsonwebtoken';

// Super admin email - only this user can manage other users
const SUPER_ADMIN_EMAIL = '<EMAIL>';

async function verifyAdminAccess(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.substring(7);

  try {
    // Verify JWT
    jwt.verify(token, process.env.JWT_SECRET!);

    // Check if session exists
    const session = await prisma.session.findUnique({
      where: { token },
      include: {
        user: {
          include: {
            profile: true
          }
        }
      }
    });

    if (!session || new Date() > session.expiresAt) {
      return null;
    }

    // Check if user is super admin
    if (session.user.email !== SUPER_ADMIN_EMAIL) {
      return null;
    }

    return session.user;
  } catch (error) {
    return null;
  }
}

// PUT - Update user (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adminUser = await verifyAdminAccess(request);
    if (!adminUser) {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }

    const userId = params.id;
    const { fullName, role, email } = await request.json();

    if (!fullName || fullName.trim().length === 0) {
      return NextResponse.json({ error: 'Nome completo é obrigatório' }, { status: 400 });
    }

    if (!role) {
      return NextResponse.json({ error: 'Função é obrigatória' }, { status: 400 });
    }

    // Validate role
    const validRoles = ['student', 'teacher', 'admin', 'super_admin'];
    if (!validRoles.includes(role)) {
      return NextResponse.json({ error: 'Função inválida' }, { status: 400 });
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: userId },
      include: { profile: true }
    });

    if (!existingUser) {
      return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });
    }

    // Prevent changing super admin role
    if (existingUser.email === SUPER_ADMIN_EMAIL && role !== 'super_admin') {
      return NextResponse.json({ error: 'Não é possível alterar a função do super admin' }, { status: 400 });
    }

    // If email is being changed, check for conflicts
    if (email && email !== existingUser.email) {
      const emailConflict = await prisma.user.findUnique({
        where: { email: email.toLowerCase() }
      });
      
      if (emailConflict) {
        return NextResponse.json({ error: 'Este email já está em uso' }, { status: 400 });
      }
    }

    // Update user
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        ...(email && { email: email.toLowerCase() }),
        profile: {
          update: {
            fullName: fullName.trim(),
            role: role
          }
        }
      },
      include: {
        profile: true
      }
    });

    console.log(`✅ User updated by admin: ${updatedUser.email} (${fullName})`);

    return NextResponse.json({
      message: 'Usuário atualizado com sucesso',
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        profile: updatedUser.profile
      }
    });

  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}

// GET - Get specific user (admin only)
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adminUser = await verifyAdminAccess(request);
    if (!adminUser) {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }

    const userId = params.id;

    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        profile: true
      }
    });

    if (!user) {
      return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });
    }

    return NextResponse.json({ user });

  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}