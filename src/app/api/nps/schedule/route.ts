import { NextRequest, NextResponse } from 'next/server';
import { verifyAuth } from '@/lib/auth';
import { NpsMessagingService } from '@/lib/services/nps-messaging.service';
import { prisma } from '@/lib/prisma';

// POST /api/nps/schedule - Manually schedule NPS feedback for a lesson (admin only)
export async function POST(request: NextRequest) {
  try {
    const authResult = await verifyAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const { lessonId, delayMinutes = 0 } = body;

    // Validate required fields
    if (!lessonId) {
      return NextResponse.json(
        { error: 'ID da aula é obrigatório' },
        { status: 400 }
      );
    }

    // Validate delay
    if (typeof delayMinutes !== 'number' || delayMinutes < 0 || delayMinutes > 1440) {
      return NextResponse.json(
        { error: 'Atraso deve estar entre 0 e 1440 minutos (24 horas)' },
        { status: 400 }
      );
    }

    // Verify lesson exists
    const lesson = await prisma.lesson.findUnique({
      where: { id: lessonId },
      include: {
        class: {
          include: {
            course: true
          }
        }
      }
    });

    if (!lesson) {
      return NextResponse.json(
        { error: 'Aula não encontrada' },
        { status: 404 }
      );
    }

    // Schedule NPS feedback
    await NpsMessagingService.scheduleNpsFeedbackForLesson(lessonId, delayMinutes);

    // Log audit event
    await prisma.auditLog.create({
      data: {
        userId: authResult.user.id,
        action: 'SCHEDULE_NPS_FEEDBACK',
        tableName: 'message_queue',
        recordId: lessonId,
        newValues: JSON.stringify({
          lessonId,
          lessonTitle: lesson.title,
          courseName: lesson.class.course.name,
          delayMinutes,
          triggeredBy: authResult.user.email
        })
      }
    });

    return NextResponse.json({
      success: true,
      message: `NPS feedback agendado para a aula "${lesson.title}" com atraso de ${delayMinutes} minutos`
    });

  } catch (error) {
    console.error('❌ Error scheduling NPS feedback:', error);
    
    if (error instanceof Error && error.message.includes('No students attended')) {
      return NextResponse.json(
        { error: 'Nenhum aluno compareceu à aula' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
