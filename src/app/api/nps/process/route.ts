import { NextRequest, NextResponse } from 'next/server';
import { verifyAuth } from '@/lib/auth';
import { NpsMessagingService } from '@/lib/services/nps-messaging.service';
import { prisma } from '@/lib/prisma';

// POST /api/nps/process - Manually trigger NPS message processing (admin only)
export async function POST(request: NextRequest) {
  try {
    const authResult = await verifyAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    // Process pending NPS messages
    const results = await NpsMessagingService.processPendingNpsMessages();

    // Log audit event
    await prisma.auditLog.create({
      data: {
        userId: authResult.user.id,
        action: 'PROCESS_NPS_MESSAGES',
        tableName: 'message_queue',
        recordId: 'bulk',
        newValues: JSON.stringify({
          processed: results.processed,
          sent: results.sent,
          failed: results.failed,
          triggeredBy: authResult.user.email
        })
      }
    });

    return NextResponse.json({
      success: true,
      data: results,
      message: `Processamento concluído. ${results.processed} mensagens processadas, ${results.sent} enviadas, ${results.failed} falharam.`
    });

  } catch (error) {
    console.error('❌ Error processing NPS messages:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// GET /api/nps/process - Get NPS processing status and statistics (admin only)
export async function GET(request: NextRequest) {
  try {
    const authResult = await verifyAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    // Get pending NPS messages count
    const pendingCount = await prisma.messageQueue.count({
      where: {
        messageType: 'nps',
        status: 'pending'
      }
    });

    // Get processing statistics
    const stats = await prisma.messageQueue.groupBy({
      by: ['status'],
      where: {
        messageType: 'nps'
      },
      _count: {
        id: true
      }
    });

    // Get recent NPS messages (last 24 hours)
    const twentyFourHoursAgo = new Date();
    twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);

    const recentMessages = await prisma.messageQueue.findMany({
      where: {
        messageType: 'nps',
        createdAt: { gte: twentyFourHoursAgo }
      },
      orderBy: { createdAt: 'desc' },
      take: 10,
      select: {
        id: true,
        recipientPhone: true,
        status: true,
        attempts: true,
        scheduledFor: true,
        sentAt: true,
        errorMessage: true,
        createdAt: true
      }
    });

    const statusCounts = stats.reduce((acc, stat) => {
      acc[stat.status] = stat._count.id;
      return acc;
    }, {} as Record<string, number>);

    return NextResponse.json({
      success: true,
      data: {
        pending: pendingCount,
        statistics: {
          pending: statusCounts.pending || 0,
          processing: statusCounts.processing || 0,
          sent: statusCounts.sent || 0,
          failed: statusCounts.failed || 0,
          cancelled: statusCounts.cancelled || 0
        },
        recentMessages
      }
    });

  } catch (error) {
    console.error('❌ Error getting NPS processing status:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
