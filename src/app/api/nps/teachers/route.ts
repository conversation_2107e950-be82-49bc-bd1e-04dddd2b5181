import { NextRequest, NextResponse } from 'next/server';
import { verifyAuth } from '@/lib/auth';
import { isSuperAdmin } from '@/lib/roles';
import { NpsService } from '@/lib/services/nps.service';

// GET /api/nps/teachers - Get NPS statistics for teachers (super admin only)
export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const authResult = await verifyAuth(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    // Check if user is super admin
    if (!isSuperAdmin(authResult.user)) {
      return NextResponse.json({ 
        error: 'Acesso negado. Apenas super administradores podem acessar relatórios de NPS por professor.' 
      }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const teacherId = searchParams.get('teacherId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const courseId = searchParams.get('courseId');
    const classId = searchParams.get('classId');
    const studentId = searchParams.get('studentId');
    const breakdown = searchParams.get('breakdown'); // 'lesson', 'class', or null for overall

    // Build filters
    const filters: any = {};
    if (startDate) {
      filters.startDate = new Date(startDate);
    }
    if (endDate) {
      filters.endDate = new Date(endDate);
    }
    if (courseId) {
      filters.courseId = courseId;
    }
    if (classId) {
      filters.classId = classId;
    }
    if (studentId) {
      filters.studentId = studentId;
    }

    let data;

    if (teacherId) {
      // Get stats for specific teacher
      if (breakdown === 'lesson') {
        data = await NpsService.getTeacherNpsByLesson(teacherId, filters);
      } else if (breakdown === 'class') {
        data = await NpsService.getTeacherNpsByClass(teacherId, filters);
      } else {
        data = await NpsService.getTeacherNpsStats(teacherId, filters);
      }
    } else {
      // Get stats for all teachers
      data = await NpsService.getAllTeachersNpsStats(filters);
    }

    return NextResponse.json({
      success: true,
      data
    });

  } catch (error) {
    console.error('❌ Error getting teacher NPS stats:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
