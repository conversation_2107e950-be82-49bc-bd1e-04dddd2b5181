import { NextRequest, NextResponse } from 'next/server';
import { NpsService } from '@/lib/services/nps.service';
import { verifyAuth } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// POST /api/nps/token - Generate NPS token for a lesson and student (admin only)
export async function POST(request: NextRequest) {
  try {
    const authResult = await verifyAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const { lessonId, studentId } = body;

    // Validate required fields
    if (!lessonId || !studentId) {
      return NextResponse.json(
        { error: 'ID da aula e ID do aluno são obrigatórios' },
        { status: 400 }
      );
    }

    // Verify lesson exists
    const lesson = await prisma.lesson.findUnique({
      where: { id: lessonId },
      include: {
        class: {
          include: {
            course: true
          }
        }
      }
    });

    if (!lesson) {
      return NextResponse.json(
        { error: 'Aula não encontrada' },
        { status: 404 }
      );
    }

    // Verify student exists
    const student = await prisma.student.findUnique({
      where: { id: studentId }
    });

    if (!student) {
      return NextResponse.json(
        { error: 'Aluno não encontrado' },
        { status: 404 }
      );
    }

    // Generate NPS token
    const tokenData = await NpsService.generateNpsToken(lessonId, studentId);

    // Log audit event
    await prisma.auditLog.create({
      data: {
        userId: authResult.user.id,
        action: 'CREATE_NPS_TOKEN',
        tableName: 'nps_tokens',
        recordId: tokenData.id,
        newValues: JSON.stringify({
          lessonId,
          studentId,
          expiresAt: tokenData.expiresAt
        })
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        tokenId: tokenData.id,
        token: tokenData.token,
        expiresAt: tokenData.expiresAt,
        lesson: {
          id: lesson.id,
          title: lesson.title,
          scheduledDate: lesson.scheduledDate,
          class: {
            name: lesson.class.name,
            course: {
              name: lesson.class.course.name
            }
          }
        },
        student: {
          id: student.id,
          name: student.name
        }
      }
    });

  } catch (error) {
    console.error('❌ Error generating NPS token:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// GET /api/nps/token?token=xxx - Validate and get token details
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');

    if (!token) {
      return NextResponse.json(
        { error: 'Token é obrigatório' },
        { status: 400 }
      );
    }

    // Get token details
    const tokenDetails = await NpsService.getTokenDetails(token);

    if (!tokenDetails) {
      return NextResponse.json(
        { error: 'Token inválido ou expirado' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        tokenId: tokenDetails.id,
        lesson: {
          id: tokenDetails.lesson.id,
          title: tokenDetails.lesson.title,
          scheduledDate: tokenDetails.lesson.scheduledDate,
          class: {
            name: tokenDetails.lesson.class.name,
            course: {
              name: tokenDetails.lesson.class.course.name
            }
          }
        },
        student: {
          id: tokenDetails.student.id,
          name: tokenDetails.student.name
        },
        existingFeedback: tokenDetails.feedback ? {
          rating: tokenDetails.feedback.rating,
          anonymousText: tokenDetails.feedback.anonymousText,
          submittedAt: tokenDetails.feedback.submittedAt
        } : null,
        expiresAt: tokenDetails.expiresAt
      }
    });

  } catch (error) {
    console.error('❌ Error validating NPS token:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
