import { NextRequest, NextResponse } from 'next/server';
import { NpsService } from '@/lib/services/nps.service';
import { verifyAuth } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// POST /api/nps/feedback - Submit NPS feedback
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { token, rating, anonymousText } = body;

    // Validate required fields
    if (!token || !rating) {
      return NextResponse.json(
        { error: 'Token e avaliação são obrigatórios' },
        { status: 400 }
      );
    }

    // Validate rating values
    if (!['good', 'neutral', 'bad'].includes(rating)) {
      return NextResponse.json(
        { error: 'Avaliação deve ser: good, neutral ou bad' },
        { status: 400 }
      );
    }

    // Validate token and get details
    const tokenData = await NpsService.validateNpsToken(token);
    if (!tokenData) {
      return NextResponse.json(
        { error: 'Token inválido ou expirado' },
        { status: 400 }
      );
    }

    // Submit feedback
    await NpsService.submitFeedback({
      tokenId: tokenData.id,
      lessonId: tokenData.lessonId,
      studentId: tokenData.studentId,
      rating,
      anonymousText: anonymousText?.trim() || undefined
    });

    return NextResponse.json({
      success: true,
      message: 'Feedback enviado com sucesso!'
    });

  } catch (error) {
    console.error('❌ Error submitting NPS feedback:', error);
    
    if (error instanceof Error && error.message.includes('Invalid or expired')) {
      return NextResponse.json(
        { error: 'Token inválido ou expirado' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// GET /api/nps/feedback?lessonId=xxx - Get feedback for a lesson (admin only)
export async function GET(request: NextRequest) {
  try {
    const authResult = await verifyAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const lessonId = searchParams.get('lessonId');
    const courseId = searchParams.get('courseId');

    if (lessonId) {
      // Get feedback for specific lesson
      const feedback = await NpsService.getLessonFeedback(lessonId);
      
      return NextResponse.json({
        success: true,
        data: feedback
      });
    } else if (courseId) {
      // Get feedback stats for course
      const stats = await NpsService.getCourseFeedbackStats(courseId);
      
      return NextResponse.json({
        success: true,
        data: stats
      });
    } else {
      return NextResponse.json(
        { error: 'lessonId ou courseId é obrigatório' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('❌ Error getting NPS feedback:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
