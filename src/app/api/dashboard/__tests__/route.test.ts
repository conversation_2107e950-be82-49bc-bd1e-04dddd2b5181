import { NextRequest } from 'next/server';
import { GET } from '../route';
import { prisma } from '@/lib/prisma';
import { auth } from '@/lib/auth';
import { isAdminOrSuperAdmin } from '@/lib/roles';

// Mock dependencies
jest.mock('@/lib/prisma', () => ({
  prisma: {
    student: {
      count: jest.fn(),
      findMany: jest.fn()
    },
    class: {
      findMany: jest.fn()
    },
    attendance: {
      findMany: jest.fn()
    },
    mentoringBooking: {
      count: jest.fn(),
      findMany: jest.fn()
    },
    lesson: {
      count: jest.fn(),
      findMany: jest.fn()
    }
  }
}));

jest.mock('@/lib/auth', () => ({
  auth: jest.fn()
}));

jest.mock('@/lib/roles', () => ({
  isAdminOrSuperAdmin: jest.fn()
}));

const mockPrisma = prisma as jest.Mocked<typeof prisma>;
const mockAuth = auth as jest.MockedFunction<typeof auth>;
const mockIsAdminOrSuperAdmin = isAdminOrSuperAdmin as jest.MockedFunction<typeof isAdminOrSuperAdmin>;

describe('/api/dashboard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock current date to ensure consistent test results
    jest.useFakeTimers();
    jest.setSystemTime(new Date('2024-01-15T10:00:00Z'));
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('should return dashboard data for authorized admin user', async () => {
    const mockUser = { id: '1', role: 'admin' };
    mockAuth.mockResolvedValue(mockUser);
    mockIsAdminOrSuperAdmin.mockReturnValue(true);

    // Mock database responses
    mockPrisma.student.count
      .mockResolvedValueOnce(150) // totalStudents
      .mockResolvedValueOnce(12); // newStudentsThisMonth

    mockPrisma.class.findMany.mockResolvedValue([
      {
        id: '1',
        name: 'Turma A',
        isActive: true,
        enrollments: [{ id: '1' }, { id: '2' }],
        lessons: []
      },
      {
        id: '2',
        name: 'Turma B',
        isActive: true,
        enrollments: [{ id: '3' }],
        lessons: []
      }
    ]);

    mockPrisma.attendance.findMany.mockResolvedValue([
      { id: '1', status: 'present', lesson: { class: { id: '1' } } },
      { id: '2', status: 'present', lesson: { class: { id: '1' } } },
      { id: '3', status: 'absent', lesson: { class: { id: '1' } } },
      { id: '4', status: 'present', lesson: { class: { id: '2' } } }
    ]);

    mockPrisma.mentoringBooking.count.mockResolvedValue(25);

    mockPrisma.student.findMany.mockResolvedValue([
      {
        id: '1',
        enrollments: [
          { course: { id: '1' } }
        ]
      },
      {
        id: '2',
        enrollments: [
          { course: { id: '1' } },
          { course: { id: '2' } }
        ]
      }
    ]);

    mockPrisma.mentoringBooking.findMany.mockResolvedValue([
      { id: '1', student: { enrollments: [{ course: { id: '1' } }] } },
      { id: '2', student: { enrollments: [{ course: { id: '1' } }] } }
    ]);

    mockPrisma.lesson.count.mockResolvedValue(3);
    mockPrisma.lesson.findMany.mockResolvedValue([
      {
        id: '1',
        scheduledDate: new Date('2024-01-15T09:00:00Z'),
        class: {
          name: 'Turma A',
          enrollments: [{ id: '1' }, { id: '2' }]
        }
      }
    ]);

    const request = new NextRequest('http://localhost:3000/api/dashboard');
    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.success).toBe(true);
    expect(data.data.stats).toMatchObject({
      totalStudents: 150,
      newStudentsThisMonth: 12,
      activeClasses: 2,
      upcomingLessons: 3,
      scheduledMentoringSessions: 25
    });

    // Verify mentoring calculations
    expect(typeof data.data.stats.mentoringCompletionPercentage).toBe('number');
    expect(data.data.stats.mentoringCompletionPercentage).toBeGreaterThanOrEqual(0);
    expect(data.data.stats.mentoringCompletionPercentage).toBeLessThanOrEqual(100);
    expect(typeof data.data.stats.pendingMentoringSessions).toBe('number');
    expect(data.data.stats.pendingMentoringSessions).toBeGreaterThanOrEqual(0);

    // Verify average attendance rate calculation
    expect(typeof data.data.stats.averageAttendanceRate).toBe('number');
    expect(data.data.stats.averageAttendanceRate).toBeGreaterThanOrEqual(0);
    expect(data.data.stats.averageAttendanceRate).toBeLessThanOrEqual(100);
  });

  it('should return 401 for unauthorized user', async () => {
    mockAuth.mockResolvedValue(null);

    const request = new NextRequest('http://localhost:3000/api/dashboard');
    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(401);
    expect(data.error).toBe('Unauthorized');
  });

  it('should return 401 for non-admin user', async () => {
    const mockUser = { id: '1', role: 'user' };
    mockAuth.mockResolvedValue(mockUser);
    mockIsAdminOrSuperAdmin.mockReturnValue(false);

    const request = new NextRequest('http://localhost:3000/api/dashboard');
    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(401);
    expect(data.error).toBe('Unauthorized');
  });

  it('should handle database errors gracefully', async () => {
    const mockUser = { id: '1', role: 'admin' };
    mockAuth.mockResolvedValue(mockUser);
    mockIsAdminOrSuperAdmin.mockReturnValue(true);

    mockPrisma.student.count.mockRejectedValue(new Error('Database error'));

    const request = new NextRequest('http://localhost:3000/api/dashboard');
    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.error).toBe('Internal server error');
  });

  it('should correctly calculate mentoring metrics', async () => {
    const mockUser = { id: '1', role: 'admin' };
    mockAuth.mockResolvedValue(mockUser);
    mockIsAdminOrSuperAdmin.mockReturnValue(true);

    // Setup mock data for mentoring calculation test
    mockPrisma.student.count
      .mockResolvedValueOnce(50) // totalStudents
      .mockResolvedValueOnce(5); // newStudentsThisMonth

    mockPrisma.class.findMany.mockResolvedValue([]);
    mockPrisma.attendance.findMany.mockResolvedValue([]);
    mockPrisma.mentoringBooking.count.mockResolvedValue(10);
    mockPrisma.lesson.count.mockResolvedValue(0);
    mockPrisma.lesson.findMany.mockResolvedValue([]);

    // Mock 3 active students with different course enrollments
    mockPrisma.student.findMany.mockResolvedValue([
      {
        id: '1',
        enrollments: [{ course: { id: '1' } }] // 1 course = 4 mentoring sessions
      },
      {
        id: '2',
        enrollments: [
          { course: { id: '1' } },
          { course: { id: '2' } }
        ] // 2 courses = 8 mentoring sessions
      },
      {
        id: '3',
        enrollments: [{ course: { id: '3' } }] // 1 course = 4 mentoring sessions
      }
    ]);

    // Mock 6 completed mentoring sessions
    mockPrisma.mentoringBooking.findMany.mockResolvedValue([
      { id: '1' },
      { id: '2' },
      { id: '3' },
      { id: '4' },
      { id: '5' },
      { id: '6' }
    ]);

    const request = new NextRequest('http://localhost:3000/api/dashboard');
    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    
    // Total expected: 3 students * avg 1.33 courses * 4 sessions = 16 sessions
    // Completed: 6 sessions
    // Pending: 16 - 6 = 10 sessions
    // Completion rate: 6/16 * 100 = 37.5% (rounded to 38%)
    
    const stats = data.data.stats;
    expect(stats.mentoringCompletionPercentage).toBeGreaterThanOrEqual(0);
    expect(stats.mentoringCompletionPercentage).toBeLessThanOrEqual(100);
    expect(stats.pendingMentoringSessions).toBeGreaterThanOrEqual(0);
  });

  it('should correctly calculate average attendance rate', async () => {
    const mockUser = { id: '1', role: 'admin' };
    mockAuth.mockResolvedValue(mockUser);
    mockIsAdminOrSuperAdmin.mockReturnValue(true);

    mockPrisma.student.count
      .mockResolvedValueOnce(20)
      .mockResolvedValueOnce(2);

    // Mock classes with different attendance rates
    mockPrisma.class.findMany.mockResolvedValue([
      {
        id: '1',
        name: 'Turma A',
        isActive: true,
        enrollments: [{ id: '1' }],
        lessons: []
      },
      {
        id: '2',
        name: 'Turma B',
        isActive: true,
        enrollments: [{ id: '2' }],
        lessons: []
      }
    ]);

    // Class 1: 3/4 = 75% attendance
    // Class 2: 2/2 = 100% attendance
    // Average: (75 + 100) / 2 = 87.5% (rounded to 88%)
    mockPrisma.attendance.findMany.mockResolvedValue([
      { id: '1', status: 'present', lesson: { class: { id: '1' } } },
      { id: '2', status: 'present', lesson: { class: { id: '1' } } },
      { id: '3', status: 'present', lesson: { class: { id: '1' } } },
      { id: '4', status: 'absent', lesson: { class: { id: '1' } } },
      { id: '5', status: 'present', lesson: { class: { id: '2' } } },
      { id: '6', status: 'present', lesson: { class: { id: '2' } } }
    ]);

    mockPrisma.mentoringBooking.count.mockResolvedValue(0);
    mockPrisma.student.findMany.mockResolvedValue([]);
    mockPrisma.mentoringBooking.findMany.mockResolvedValue([]);
    mockPrisma.lesson.count.mockResolvedValue(0);
    mockPrisma.lesson.findMany.mockResolvedValue([]);

    const request = new NextRequest('http://localhost:3000/api/dashboard');
    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.data.stats.averageAttendanceRate).toBe(88);
  });
});