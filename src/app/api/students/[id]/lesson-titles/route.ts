import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id: studentId } = params;

    if (!studentId) {
      return NextResponse.json(
        { error: 'ID do estudante é obrigatório' },
        { status: 400 }
      );
    }

    // Get current date and calculate 6 days in the future
    const now = new Date();
    const sixDaysFromNow = new Date(now);
    sixDaysFromNow.setDate(now.getDate() + 6);

    // Find all enrollments for this student
    const enrollments = await prisma.enrollment.findMany({
      where: {
        studentId,
        status: 'active'
      },
      include: {
        class: {
          include: {
            lessons: {
              orderBy: [
                { lessonNumber: 'asc' },
                { scheduledDate: 'asc' }
              ]
            }
          }
        },
        course: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    const visibleLessons = enrollments.map(enrollment => {
      const filteredLessons = enrollment.class?.lessons.filter(lesson => {
        const lessonDate = new Date(lesson.scheduledDate);
        
        // Show past lessons (completed or date has passed)
        if (lessonDate < now || lesson.isCompleted) {
          return true;
        }
        
        // Show upcoming lessons within 6 days
        if (lessonDate >= now && lessonDate <= sixDaysFromNow) {
          return true;
        }
        
        return false;
      }) || [];

      return {
        classId: enrollment.class?.id,
        className: enrollment.class?.name,
        courseId: enrollment.course.id,
        courseName: enrollment.course.name,
        lessons: filteredLessons.map(lesson => ({
          id: lesson.id,
          title: lesson.title,
          lessonNumber: lesson.lessonNumber,
          scheduledDate: lesson.scheduledDate,
          isCompleted: lesson.isCompleted,
          duration: lesson.duration,
          location: lesson.location
        }))
      };
    });

    return NextResponse.json({
      studentId,
      classes: visibleLessons,
      totalVisibleLessons: visibleLessons.reduce(
        (total, classData) => total + classData.lessons.length, 
        0
      )
    });

  } catch (error) {
    console.error('Erro ao buscar títulos das aulas:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}