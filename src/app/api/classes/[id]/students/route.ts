import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { verifyAuth } from '@/lib/auth';

const prisma = new PrismaClient();

// GET /api/classes/[id]/students - Get students enrolled in a specific class
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  try {
    const authResult = await verifyAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const enrollments = await prisma.enrollment.findMany({
      where: { 
        classId: id,
        status: 'active'
      },
      include: {
        student: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true
          }
        }
      },
      orderBy: {
        student: {
          name: 'asc'
        }
      }
    });

    const students = enrollments.map(enrollment => ({
      ...enrollment.student,
      enrollmentId: enrollment.id,
      enrollmentDate: enrollment.enrollmentDate
    }));

    return NextResponse.json(students);
  } catch (error) {
    console.error('Get class students error:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}