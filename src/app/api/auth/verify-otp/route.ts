import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';
import jwt from 'jsonwebtoken';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json({ error: 'Token é obrigatório' }, { status: 400 });
    }

    // Find recovery OTP token
    const recoveryOtp = await prisma.recoveryOtp.findFirst({
      where: { 
        token,
        usedAt: null,
        expiresAt: {
          gt: new Date()
        }
      },
      include: {
        student: true,
        attendance: {
          include: {
            lesson: {
              include: {
                class: {
                  include: {
                    course: true
                  }
                }
              }
            }
          }
        }
      }
    });

    if (!recoveryOtp) {
      return NextResponse.json({ 
        error: 'Token OTP inválido ou expirado' 
      }, { status: 400 });
    }

    // Check if student exists and is active
    if (!recoveryOtp.student || recoveryOtp.student.status !== 'active') {
      return NextResponse.json({ 
        error: 'Aluno não encontrado ou inativo' 
      }, { status: 400 });
    }

    // Find or create user account for the student
    let user = await prisma.user.findFirst({
      where: { 
        email: recoveryOtp.student.email || recoveryOtp.student.phone 
      },
      include: {
        profile: true
      }
    });

    if (!user) {
      // Create user account for the student
      const email = recoveryOtp.student.email || `${recoveryOtp.student.phone}@student.voxstudent.com`;
      
      user = await prisma.user.create({
        data: {
          email: email,
          emailVerified: true,
          profile: {
            create: {
              fullName: recoveryOtp.student.name,
              role: 'student'
            }
          }
        },
        include: {
          profile: true
        }
      });

      console.log(`🎓 Created user account for student OTP login: ${email}`);
    }

    // Mark OTP as used
    await prisma.recoveryOtp.update({
      where: { id: recoveryOtp.id },
      data: { usedAt: new Date() }
    });

    // Update user last login
    await prisma.user.update({
      where: { id: user.id },
      data: { 
        lastLoginAt: new Date(),
        emailVerified: true
      }
    });

    // Create session with unique token
    const sessionToken = jwt.sign(
      {
        userId: user.id,
        email: user.email,
        studentId: recoveryOtp.student.id,
        sessionId: crypto.randomBytes(16).toString('hex'),
        loginType: 'otp' // Mark as OTP login
      },
      process.env.JWT_SECRET!,
      { expiresIn: `${parseInt(process.env.SESSION_EXPIRY_DAYS || '30')}d` }
    );

    const sessionExpiresAt = new Date();
    sessionExpiresAt.setDate(sessionExpiresAt.getDate() + parseInt(process.env.SESSION_EXPIRY_DAYS || '30'));

    // Delete any existing sessions for this user to prevent token conflicts
    await prisma.session.deleteMany({
      where: { userId: user.id }
    });

    // Create new session
    await prisma.session.create({
      data: {
        userId: user.id,
        token: sessionToken,
        expiresAt: sessionExpiresAt
      }
    });

    console.log(`✅ Student OTP login successful: ${recoveryOtp.student.name} (${user.email})`);

    return NextResponse.json({
      token: sessionToken,
      user: {
        id: user.id,
        email: user.email,
        profile: user.profile
      },
      student: {
        id: recoveryOtp.student.id,
        name: recoveryOtp.student.name,
        email: recoveryOtp.student.email,
        phone: recoveryOtp.student.phone
      },
      recoveryData: {
        attendance: recoveryOtp.attendance,
        course: recoveryOtp.attendance.lesson.class.course
      }
    });

  } catch (error) {
    console.error('Error verifying OTP:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}