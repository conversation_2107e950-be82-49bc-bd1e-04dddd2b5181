import { NextRequest, NextResponse } from 'next/server';
import { recoveryService } from '@/lib/services/recovery.service';

// GET /api/recovery/validate - Validate recovery OTP token
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');

    if (!token) {
      return NextResponse.json(
        { error: 'Token é obrigatório' },
        { status: 400 }
      );
    }

    const otpData = await recoveryService.validateRecoveryOtp(token);

    return NextResponse.json({
      data: otpData,
      message: 'Token validado com sucesso'
    });
  } catch (error) {
    console.error('Recovery OTP validation error:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Erro interno do servidor';
    const status = errorMessage.includes('Invalid or expired') ? 401 : 500;
    
    return NextResponse.json(
      { error: errorMessage },
      { status }
    );
  }
}