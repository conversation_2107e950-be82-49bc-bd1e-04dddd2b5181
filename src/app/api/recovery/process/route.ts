import { NextRequest, NextResponse } from 'next/server';
import { verifyAuth } from '@/lib/auth';
import { recoverySchedulerService } from '@/lib/services/recovery-scheduler.service';

// POST /api/recovery/process - Manually trigger recovery reminder processing (admin only)
export async function POST(request: NextRequest) {
  try {
    const authResult = await verifyAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    // Check if user is teacher or super admin
    if (!['teacher', 'super_admin'].includes(authResult.user.role)) {
      return NextResponse.json({ error: '<PERSON><PERSON> negado - apenas professores' }, { status: 403 });
    }

    const results = await recoverySchedulerService.triggerManualProcessing();

    return NextResponse.json({
      data: results,
      message: `Processamento concluído. ${results.length} lembretes processados.`
    });
  } catch (error) {
    console.error('Manual recovery processing error:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// GET /api/recovery/process - Get recovery reminder statistics (admin only)
export async function GET(request: NextRequest) {
  try {
    const authResult = await verifyAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    // Check if user is teacher or super admin
    if (!['teacher', 'super_admin'].includes(authResult.user.role)) {
      return NextResponse.json({ error: 'Acesso negado - apenas professores' }, { status: 403 });
    }

    const stats = await recoverySchedulerService.getRecoveryReminderStats();

    return NextResponse.json({
      data: stats,
      message: 'Estatísticas de lembretes de reposição'
    });
  } catch (error) {
    console.error('Get recovery stats error:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}