import { NextRequest, NextResponse } from 'next/server';
import { verifyAuth } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// POST /api/recovery/book-retake - Book a lesson retake
export async function POST(request: NextRequest) {
  try {
    const authResult = await verifyAuth(request);
    
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      );
    }

    const user = authResult.user;

    // Only allow students to access this endpoint
    if (user.profile?.role !== 'student') {
      return NextResponse.json(
        { error: 'Acesso negado. Esta página é para estudantes.' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { lessonId } = body;

    if (!lessonId) {
      return NextResponse.json(
        { error: 'ID da aula é obrigatório' },
        { status: 400 }
      );
    }

    // Find student record by email or phone
    const student = await prisma.student.findFirst({
      where: {
        OR: [
          { email: user.email },
          { phone: user.email }
        ],
        status: 'active'
      }
    });

    if (!student) {
      return NextResponse.json(
        { error: 'Registro de estudante não encontrado' },
        { status: 404 }
      );
    }

    // Get the lesson details
    const lesson = await prisma.lesson.findUnique({
      where: { id: lessonId },
      include: {
        class: {
          include: {
            course: true
          }
        },
        attendance: {
          where: {
            studentId: student.id
          }
        }
      }
    });

    if (!lesson) {
      return NextResponse.json(
        { error: 'Aula não encontrada' },
        { status: 404 }
      );
    }

    // Check if lesson is in the future
    if (lesson.scheduledDate <= new Date()) {
      return NextResponse.json(
        { error: 'Não é possível agendar uma aula que já aconteceu' },
        { status: 400 }
      );
    }

    // Check if student already has attendance for this lesson
    if (lesson.attendance.length > 0) {
      return NextResponse.json(
        { error: 'Você já possui presença registrada para esta aula' },
        { status: 400 }
      );
    }

    // Check if student is already enrolled in this class
    const existingEnrollment = await prisma.enrollment.findFirst({
      where: {
        studentId: student.id,
        classId: lesson.classId,
        status: 'active'
      }
    });

    if (existingEnrollment) {
      return NextResponse.json(
        { error: 'Você já está matriculado nesta turma' },
        { status: 400 }
      );
    }

    // Verify that student has completed this lesson number in the course before
    const completedLesson = await prisma.attendance.findFirst({
      where: {
        studentId: student.id,
        status: 'present',
        lesson: {
          lessonNumber: lesson.lessonNumber,
          class: {
            courseId: lesson.class.courseId
          }
        }
      },
      include: {
        lesson: true
      }
    });

    if (!completedLesson) {
      return NextResponse.json(
        { error: 'Você ainda não concluiu esta aula. Só é possível refazer aulas que já foram concluídas.' },
        { status: 400 }
      );
    }

    // Check for existing attendance for this retake lesson
    const existingRetakeAttendance = await prisma.attendance.findFirst({
      where: {
        studentId: student.id,
        lessonId: lessonId,
        isRetake: true
      }
    });

    if (existingRetakeAttendance) {
      return NextResponse.json(
        { error: 'Você já agendou para refazer esta aula' },
        { status: 400 }
      );
    }

    // Create an attendance record for the retake
    const retakeAttendance = await prisma.attendance.create({
      data: {
        studentId: student.id,
        lessonId: lessonId,
        status: 'pending',
        isRetake: true,
        originalLessonId: completedLesson.lessonId
      },
      include: {
        lesson: {
          include: {
            class: {
              include: {
                course: true
              }
            }
          }
        }
      }
    });

    return NextResponse.json({
      data: {
        id: retakeAttendance.id,
        lesson: {
          title: retakeAttendance.lesson.title,
          scheduledDate: retakeAttendance.lesson.scheduledDate.toISOString(),
          class: {
            name: retakeAttendance.lesson.class.name,
            course: {
              name: retakeAttendance.lesson.class.course.name
            }
          }
        }
      },
      message: 'Aula para refazer agendada com sucesso!'
    });
  } catch (error) {
    console.error('Book retake error:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Erro interno do servidor';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}