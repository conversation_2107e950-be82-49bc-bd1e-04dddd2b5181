import { NextRequest, NextResponse } from 'next/server';
import { verifyAuth } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/recovery/retake-lessons - Get available classes for retaking a specific lesson
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const lessonNumber = searchParams.get('lessonNumber');
    const courseId = searchParams.get('courseId');

    if (!lessonNumber || !courseId) {
      return NextResponse.json(
        { error: 'Número da aula e ID do curso são obrigatórios' },
        { status: 400 }
      );
    }

    const authResult = await verifyAuth(request);
    
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      );
    }

    const user = authResult.user;

    // Only allow students to access this endpoint
    if (user.profile?.role !== 'student') {
      return NextResponse.json(
        { error: 'Acesso negado. Esta página é para estudantes.' },
        { status: 403 }
      );
    }

    // Find student record by email or phone
    const student = await prisma.student.findFirst({
      where: {
        OR: [
          { email: user.email },
          { phone: user.email }
        ],
        status: 'active'
      }
    });

    if (!student) {
      return NextResponse.json(
        { error: 'Registro de estudante não encontrado' },
        { status: 404 }
      );
    }

    const now = new Date();

    // Get future lessons with the same lesson number in active classes of the same course
    const availableLessons = await prisma.lesson.findMany({
      where: {
        lessonNumber: parseInt(lessonNumber),
        scheduledDate: {
          gte: now
        },
        class: {
          courseId: courseId,
          status: 'active'
        }
      },
      include: {
        class: {
          include: {
            course: true,
            enrollments: {
              where: {
                studentId: student.id,
                status: 'active'
              }
            }
          }
        },
        attendance: {
          where: {
            studentId: student.id
          }
        }
      },
      orderBy: {
        scheduledDate: 'asc'
      }
    });

    // Filter out lessons where student is already enrolled or has attendance
    const filteredLessons = availableLessons.filter(lesson => {
      // Skip if student is already enrolled in this class
      if (lesson.class.enrollments.length > 0) {
        return false;
      }
      
      // Skip if student already has attendance for this lesson
      if (lesson.attendance.length > 0) {
        return false;
      }
      
      return true;
    });

    // Transform the data
    const formattedLessons = filteredLessons.map(lesson => ({
      id: lesson.id,
      title: lesson.title,
      lessonNumber: lesson.lessonNumber,
      scheduledDate: lesson.scheduledDate.toISOString(),
      class: {
        id: lesson.class.id,
        name: lesson.class.name,
        course: {
          id: lesson.class.course.id,
          name: lesson.class.course.name
        }
      }
    }));

    return NextResponse.json({
      data: formattedLessons,
      message: 'Aulas disponíveis para refazer carregadas com sucesso'
    });
  } catch (error) {
    console.error('Get retake lessons error:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Erro interno do servidor';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}