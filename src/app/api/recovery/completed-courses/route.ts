import { NextRequest, NextResponse } from 'next/server';
import { verifyAuth } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/recovery/completed-courses - Get student's completed courses
export async function GET(request: NextRequest) {
  try {
    const authResult = await verifyAuth(request);
    
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      );
    }

    const user = authResult.user;

    // Only allow students to access this endpoint
    if (user.profile?.role !== 'student') {
      return NextResponse.json(
        { error: 'Acesso negado. Esta página é para estudantes.' },
        { status: 403 }
      );
    }

    // Find student record by email or phone
    const student = await prisma.student.findFirst({
      where: {
        OR: [
          { email: user.email },
          { phone: user.email }
        ],
        status: 'active'
      }
    });

    if (!student) {
      return NextResponse.json(
        { error: '<PERSON>tro de estudante não encontrado' },
        { status: 404 }
      );
    }

    // Get courses where student has completed at least one lesson
    const completedCourses = await prisma.course.findMany({
      where: {
        classes: {
          some: {
            lessons: {
              some: {
                attendance: {
                  some: {
                    studentId: student.id,
                    status: 'present'
                  }
                }
              }
            }
          }
        }
      },
      include: {
        _count: {
          select: {
            classes: {
              where: {
                lessons: {
                  some: {
                    attendance: {
                      some: {
                        studentId: student.id,
                        status: 'present'
                      }
                    }
                  }
                }
              }
            }
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    });

    return NextResponse.json({
      data: completedCourses,
      message: 'Cursos concluídos carregados com sucesso'
    });
  } catch (error) {
    console.error('Get completed courses error:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Erro interno do servidor';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}