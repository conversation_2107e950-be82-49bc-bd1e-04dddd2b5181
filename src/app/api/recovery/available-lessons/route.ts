import { NextRequest, NextResponse } from 'next/server';
import { recoveryService } from '@/lib/services/recovery.service';
import { verifyAuth } from '@/lib/auth';

// GET /api/recovery/available-lessons - Get available makeup lessons
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const courseId = searchParams.get('courseId');

    if (!courseId) {
      return NextResponse.json(
        { error: 'ID do curso é obrigatório' },
        { status: 400 }
      );
    }

    // Authenticated access only
    const authResult = await verifyAuth(request);
    
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Autenticação obrigatória' },
        { status: 401 }
      );
    }

    // Only allow students to access this endpoint
    if (authResult.user.profile?.role !== 'student') {
      return NextResponse.json(
        { error: 'Acesso negado. Esta página é para estudantes.' },
        { status: 403 }
      );
    }

    // Get available lessons using current date as reference
    const availableLessons = await recoveryService.getAvailableMakeupLessons(
      courseId,
      new Date(),
      null
    );

    return NextResponse.json({
      data: availableLessons,
      message: 'Aulas disponíveis carregadas com sucesso'
    });
  } catch (error) {
    console.error('Get available lessons error:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Erro interno do servidor';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}