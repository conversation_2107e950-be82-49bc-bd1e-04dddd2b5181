import { NextRequest, NextResponse } from 'next/server';
import { recoveryService } from '@/lib/services/recovery.service';
import { verifyAuth } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// POST /api/recovery/book - Book a makeup lesson
export async function POST(request: NextRequest) {
  try {
    let body;
    try {
      body = await request.json();
    } catch (jsonError) {
      console.error('JSON parsing error:', jsonError);
      return NextResponse.json(
        { error: 'Dados inválidos - JSON mal formado' },
        { status: 400 }
      );
    }

    const { originalLessonId, makeupLessonId } = body;

    if (!originalLessonId) {
      return NextResponse.json(
        { error: 'ID da aula original é obrigatório' },
        { status: 400 }
      );
    }

    if (!makeupLessonId) {
      return NextResponse.json(
        { error: 'ID da aula de reposição é obrigatório' },
        { status: 400 }
      );
    }

    // Authenticated access only
    const authResult = await verifyAuth(request);
    
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Autenticação obrigatória' },
        { status: 401 }
      );
    }

    if (authResult.user.profile?.role !== 'student') {
      return NextResponse.json(
        { error: 'Acesso negado. Esta página é para estudantes.' },
        { status: 403 }
      );
    }

    // Find student record by email or phone
    const student = await prisma.student.findFirst({
      where: {
        OR: [
          { email: authResult.user.email },
          { phone: authResult.user.email }
        ],
        status: 'active'
      }
    });

    if (!student) {
      return NextResponse.json(
        { error: 'Registro de estudante não encontrado' },
        { status: 404 }
      );
    }

    // Book the makeup lesson
    const booking = await recoveryService.bookMakeupLesson({
      studentId: student.id,
      originalLessonId,
      makeupLessonId,
      otpToken: 'authenticated'
    });

    // Log audit event
    await prisma.auditLog.create({
      data: {
        userId: authResult.user.id,
        action: 'CREATE',
        tableName: 'recovery_booking',
        recordId: booking.id,
        newValues: JSON.stringify({
          studentId: booking.studentId,
          originalLessonId: booking.originalLessonId,
          makeupLessonId: booking.makeupLessonId,
          studentName: booking.student.name,
          accessMethod: 'authenticated'
        })
      }
    });

    return NextResponse.json({
      data: booking,
      message: 'Reposição agendada com sucesso'
    });
  } catch (error) {
    console.error('Book makeup lesson error:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Erro interno do servidor';
    
    let status = 500;
    if (errorMessage.includes('already booked') || errorMessage.includes('not found')) {
      status = 400;
    }
    
    return NextResponse.json(
      { error: errorMessage },
      { status }
    );
  }
}