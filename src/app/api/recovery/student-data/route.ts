import { NextRequest, NextResponse } from 'next/server';
import { verifyAuth } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/recovery/student-data - Get recovery data for authenticated student
export async function GET(request: NextRequest) {
  try {
    const authResult = await verifyAuth(request);
    
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      );
    }

    const user = authResult.user;

    // Only allow students to access this endpoint
    if (user.profile?.role !== 'student') {
      return NextResponse.json(
        { error: 'Acesso negado. Esta página é para estudantes.' },
        { status: 403 }
      );
    }

    // Find student record by email or phone
    const student = await prisma.student.findFirst({
      where: {
        OR: [
          { email: user.email },
          { phone: user.email } // Phone might be stored as email
        ],
        status: 'active'
      }
    });

    if (!student) {
      return NextResponse.json(
        { error: 'Registro de estudante não encontrado' },
        { status: 404 }
      );
    }

    // Find pending makeup lessons for this student
    // Get absent attendances that don't have a recovery booking yet
    const pendingAttendances = await prisma.attendance.findMany({
      where: {
        studentId: student.id,
        status: 'absent',
        originalLessonId: null, // This is not a makeup lesson itself
      },
      include: {
        lesson: {
          include: {
            class: {
              include: {
                course: true
              }
            }
          }
        },
        student: true
      },
      orderBy: {
        lesson: {
          scheduledDate: 'desc'
        }
      }
    });

    // Filter out attendances that already have recovery bookings
    const attendancesWithoutBookings = [];
    for (const attendance of pendingAttendances) {
      const existingBooking = await prisma.recoveryBooking.findFirst({
        where: {
          studentId: student.id,
          originalLessonId: attendance.lessonId
        }
      });
      
      if (!existingBooking) {
        attendancesWithoutBookings.push(attendance);
      }
    }

    // Transform data to match the expected format
    const recoveryData = attendancesWithoutBookings.map(attendance => ({
      student: {
        id: attendance.student.id,
        name: attendance.student.name,
        email: attendance.student.email,
        phone: attendance.student.phone
      },
      attendance: {
        lesson: {
          id: attendance.lesson.id,
          title: attendance.lesson.title,
          scheduledDate: attendance.lesson.scheduledDate.toISOString(),
          class: {
            name: attendance.lesson.class.name,
            course: {
              name: attendance.lesson.class.course.name
            }
          }
        }
      }
    }));

    return NextResponse.json({
      data: recoveryData,
      message: 'Dados de reposição carregados com sucesso'
    });
  } catch (error) {
    console.error('Get student recovery data error:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Erro interno do servidor';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}