import { NextRequest, NextResponse } from 'next/server';
import { verifyAuth } from '@/lib/auth';
import { recoverySchedulerService } from '@/lib/services/recovery-scheduler.service';

// POST /api/recovery/cleanup - Clean up old recovery data (admin only)
export async function POST(request: NextRequest) {
  try {
    const authResult = await verifyAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    // Check if user is teacher or super admin
    if (!['teacher', 'super_admin'].includes(authResult.user.role)) {
      return NextResponse.json({ error: '<PERSON><PERSON> negado - apenas professores' }, { status: 403 });
    }

    const result = await recoverySchedulerService.cleanupOldData();

    return NextResponse.json({
      data: result,
      message: `Limpeza concluída. ${result.deletedReminders} lembretes e ${result.deletedOtps} OTPs removidos.`
    });
  } catch (error) {
    console.error('Recovery cleanup error:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}