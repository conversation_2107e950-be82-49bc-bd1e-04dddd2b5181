import { NextRequest, NextResponse } from 'next/server';
import { verifyAuth } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/recovery/completed-lessons - Get student's completed lessons for a course
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const courseId = searchParams.get('courseId');

    if (!courseId) {
      return NextResponse.json(
        { error: 'ID do curso é obrigatório' },
        { status: 400 }
      );
    }

    const authResult = await verifyAuth(request);
    
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      );
    }

    const user = authResult.user;

    // Only allow students to access this endpoint
    if (user.profile?.role !== 'student') {
      return NextResponse.json(
        { error: 'Acesso negado. Esta página é para estudantes.' },
        { status: 403 }
      );
    }

    // Find student record by email or phone
    const student = await prisma.student.findFirst({
      where: {
        OR: [
          { email: user.email },
          { phone: user.email }
        ],
        status: 'active'
      }
    });

    if (!student) {
      return NextResponse.json(
        { error: 'Registro de estudante não encontrado' },
        { status: 404 }
      );
    }

    // Get all lessons this student has completed in the specified course
    const completedLessons = await prisma.lesson.findMany({
      where: {
        class: {
          courseId: courseId
        },
        attendance: {
          some: {
            studentId: student.id,
            status: 'present'
          }
        }
      },
      include: {
        class: {
          include: {
            course: true
          }
        },
        attendance: {
          where: {
            studentId: student.id,
            status: 'present'
          }
        }
      },
      orderBy: [
        { lessonNumber: 'asc' },
        { scheduledDate: 'asc' }
      ]
    });

    // Transform the data to include the attended date
    const formattedLessons = completedLessons.map(lesson => ({
      id: lesson.id,
      title: lesson.title,
      lessonNumber: lesson.lessonNumber,
      originalDate: lesson.scheduledDate.toISOString(),
      attendedDate: lesson.attendance[0]?.createdAt.toISOString(),
      class: {
        id: lesson.class.id,
        name: lesson.class.name,
        course: {
          id: lesson.class.course.id,
          name: lesson.class.course.name
        }
      }
    }));

    return NextResponse.json({
      data: formattedLessons,
      message: 'Aulas concluídas carregadas com sucesso'
    });
  } catch (error) {
    console.error('Get completed lessons error:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Erro interno do servidor';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}