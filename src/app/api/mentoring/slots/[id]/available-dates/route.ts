import { NextRequest, NextResponse } from 'next/server';
import { mentoringService } from '@/lib/services/mentoring.service';
import { verifyAuth } from '@/lib/auth';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authResult = await verifyAuth(request);
    
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = authResult.user;

    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const weeksAhead = parseInt(searchParams.get('weeksAhead') || '4');

    const result = await mentoringService.getAvailableDates(id, weeksAhead);

    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 400 });
    }

    return NextResponse.json({ 
      success: true, 
      data: result.data 
    });

  } catch (error) {
    console.error('Error getting available dates:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}