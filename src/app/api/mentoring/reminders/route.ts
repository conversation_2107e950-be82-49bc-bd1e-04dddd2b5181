import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { mentoringReminderService } from '@/lib/services/mentoring-reminder.service';

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const action = url.searchParams.get('action');

    if (action === 'preview') {
      // Preview a reminder template with sample data
      const templateId = url.searchParams.get('templateId');
      const slotId = url.searchParams.get('slotId');

      if (!templateId || !slotId) {
        return NextResponse.json(
          { error: 'Template ID and Slot ID are required for preview' },
          { status: 400 }
        );
      }

      const result = await mentoringReminderService.previewReminderTemplate(templateId, slotId);
      return NextResponse.json(result);
    }

    if (action === 'pending') {
      // Get pending reminders
      const result = await mentoringReminderService.getPendingReminders();
      return NextResponse.json(result);
    }

    if (action === 'statistics') {
      // Get reminder statistics
      const days = parseInt(url.searchParams.get('days') || '7');
      const result = await mentoringReminderService.getReminderStatistics(days);
      return NextResponse.json(result);
    }

    // Default: Get all scheduled reminders
    const result = await mentoringReminderService.getScheduledReminders();
    return NextResponse.json(result);

  } catch (error) {
    console.error('Error in mentoring reminders GET:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    if (action === 'schedule') {
      // Schedule reminders for a specific mentoring booking
      const { bookingId } = body;

      if (!bookingId) {
        return NextResponse.json(
          { error: 'Booking ID is required' },
          { status: 400 }
        );
      }

      const result = await mentoringReminderService.scheduleReminderForBooking(bookingId);
      return NextResponse.json(result);
    }

    if (action === 'bulk-schedule') {
      // Schedule reminders for all upcoming mentoring sessions
      const { hoursAhead } = body;
      const result = await mentoringReminderService.scheduleBulkReminders(hoursAhead || 24);
      return NextResponse.json(result);
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error in mentoring reminders POST:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const reminderId = url.searchParams.get('id');

    if (!reminderId) {
      return NextResponse.json(
        { error: 'Reminder ID is required' },
        { status: 400 }
      );
    }

    const result = await mentoringReminderService.cancelReminder(reminderId);
    return NextResponse.json(result);

  } catch (error) {
    console.error('Error in mentoring reminders DELETE:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}