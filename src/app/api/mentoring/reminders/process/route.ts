import { NextRequest, NextResponse } from 'next/server';
import { mentoringReminderService } from '@/lib/services/mentoring-reminder.service';

export async function POST(request: NextRequest) {
  try {
    // Process and send pending mentoring reminders
    console.log('🔔 Processing mentoring reminders...');
    
    const result = await mentoringReminderService.processPendingReminders();
    
    return NextResponse.json({
      success: true,
      data: {
        processed: result.processed,
        sent: result.sent,
        failed: result.failed,
        message: `Processed ${result.processed} reminders: ${result.sent} sent, ${result.failed} failed`
      }
    });

  } catch (error) {
    console.error('Error processing mentoring reminders:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to process mentoring reminders',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}