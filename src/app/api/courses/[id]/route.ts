import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { verifyAuth } from '@/lib/auth';

const prisma = new PrismaClient();

// GET /api/courses/[id] - Get a specific course
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  try {
    const authResult = await verifyAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const course = await prisma.course.findUnique({
      where: { id },
      include: {
        classes: {
          include: {
            _count: {
              select: {
                enrollments: true,
                lessons: true
              }
            }
          }
        },
        _count: {
          select: {
            enrollments: true
          }
        }
      }
    });

    if (!course) {
      return NextResponse.json({ error: 'Curso não encontrado' }, { status: 404 });
    }

    return NextResponse.json({ data: course });
  } catch (error) {
    console.error('Get course error:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}

// PUT /api/courses/[id] - Update a course
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  try {
    const authResult = await verifyAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const { name, description, duration, numberOfLessons, lessonDuration, price, allowsMakeup, mentoringSessionsPerEnrollment, isActive } = body;

    if (!name || name.trim().length < 2) {
      return NextResponse.json(
        { error: 'Nome do curso é obrigatório (mínimo 2 caracteres)' },
        { status: 400 }
      );
    }

    // Get old values for audit
    const oldCourse = await prisma.course.findUnique({
      where: { id }
    });

    if (!oldCourse) {
      return NextResponse.json({ error: 'Curso não encontrado' }, { status: 404 });
    }

    const course = await prisma.course.update({
      where: { id },
      data: {
        name: name.trim(),
        description: description?.trim() || null,
        duration: duration ? parseInt(duration) : null,
        numberOfLessons: numberOfLessons ? parseInt(numberOfLessons) : null,
        lessonDuration: lessonDuration !== undefined ? parseInt(lessonDuration) : oldCourse.lessonDuration,
        price: price ? parseFloat(price) : null,
        allowsMakeup: allowsMakeup || false,
        mentoringSessionsPerEnrollment: mentoringSessionsPerEnrollment !== undefined ? parseInt(mentoringSessionsPerEnrollment) : oldCourse.mentoringSessionsPerEnrollment,
        isActive: isActive !== undefined ? isActive : oldCourse.isActive
      }
    });

    // Log audit event
    await prisma.auditLog.create({
      data: {
        userId: authResult.user.id,
        action: 'UPDATE',
        tableName: 'courses',
        recordId: course.id,
        oldValues: JSON.stringify(oldCourse),
        newValues: JSON.stringify(course)
      }
    });

    // Log security event
    await prisma.securityEvent.create({
      data: {
        userId: authResult.user.id,
        eventType: 'data_modification',
        severity: 'low',
        description: `User updated course: ${course.name}`,
        metadata: JSON.stringify({ courseId: course.id }),
        ipAddress: request.ip || 'unknown',
        userAgent: request.headers.get('User-Agent') || 'unknown'
      }
    });

    return NextResponse.json({ data: course });
  } catch (error) {
    console.error('Update course error:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}

// DELETE /api/courses/[id] - Delete a course
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  try {
    const authResult = await verifyAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    // Get course for audit
    const course = await prisma.course.findUnique({
      where: { id }
    });

    if (!course) {
      return NextResponse.json({ error: 'Curso não encontrado' }, { status: 404 });
    }

    // Check if course has active classes or enrollments
    const courseWithRelations = await prisma.course.findUnique({
      where: { id },
      include: {
        classes: true,
        enrollments: true
      }
    });

    if (courseWithRelations?.classes.length || courseWithRelations?.enrollments.length) {
      return NextResponse.json(
        { error: 'Não é possível excluir curso com turmas ou matrículas ativas' },
        { status: 400 }
      );
    }

    await prisma.course.delete({
      where: { id }
    });

    // Log audit event
    await prisma.auditLog.create({
      data: {
        userId: authResult.user.id,
        action: 'DELETE',
        tableName: 'courses',
        recordId: id,
        oldValues: JSON.stringify(course)
      }
    });

    // Log security event
    await prisma.securityEvent.create({
      data: {
        userId: authResult.user.id,
        eventType: 'data_modification',
        severity: 'medium',
        description: `User deleted course: ${course.name}`,
        metadata: JSON.stringify({ courseId: id }),
        ipAddress: request.ip || 'unknown',
        userAgent: request.headers.get('User-Agent') || 'unknown'
      }
    });

    return NextResponse.json({ data: { success: true } });
  } catch (error) {
    console.error('Delete course error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PATCH /api/courses/[id] - Update lesson durations for all lessons in course
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  try {
    const authResult = await verifyAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const { action, lessonDuration } = body;

    if (action !== 'update_lesson_durations') {
      return NextResponse.json({ error: 'Ação inválida' }, { status: 400 });
    }

    if (!lessonDuration || lessonDuration < 30) {
      return NextResponse.json({ 
        error: 'Duração da aula deve ser pelo menos 30 minutos' 
      }, { status: 400 });
    }

    // Verify course exists
    const course = await prisma.course.findUnique({
      where: { id },
      include: {
        classes: {
          include: {
            lessons: {
              select: {
                id: true,
                scheduledDate: true,
                duration: true
              }
            }
          }
        }
      }
    });

    if (!course) {
      return NextResponse.json({ error: 'Curso não encontrado' }, { status: 404 });
    }

    // Update course lesson duration
    await prisma.course.update({
      where: { id },
      data: { lessonDuration: parseInt(lessonDuration) }
    });

    // Update all lessons in all classes of this course
    let updatedLessonsCount = 0;
    
    for (const classItem of course.classes) {
      for (const lesson of classItem.lessons) {
        // Update lesson duration
        await prisma.lesson.update({
          where: { id: lesson.id },
          data: { duration: parseInt(lessonDuration) }
        });
        
        updatedLessonsCount++;
      }
    }

    // Log audit event
    await prisma.auditLog.create({
      data: {
        userId: authResult.user.id,
        action: 'UPDATE',
        tableName: 'courses',
        recordId: id,
        newValues: JSON.stringify({ 
          action: 'update_lesson_durations',
          newDuration: lessonDuration,
          updatedLessonsCount
        })
      }
    });

    // Log security event
    await prisma.securityEvent.create({
      data: {
        userId: authResult.user.id,
        eventType: 'data_modification',
        severity: 'medium',
        description: `User updated lesson durations for course: ${course.name}`,
        metadata: JSON.stringify({ 
          courseId: id,
          newDuration: lessonDuration,
          updatedLessonsCount
        }),
        ipAddress: request.ip || 'unknown',
        userAgent: request.headers.get('User-Agent') || 'unknown'
      }
    });

    return NextResponse.json({ 
      data: { 
        success: true,
        updatedLessonsCount,
        newDuration: lessonDuration
      },
      message: `${updatedLessonsCount} aulas atualizadas para ${lessonDuration} minutos`
    });
  } catch (error) {
    console.error('Update lesson durations error:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}
