import { NextResponse } from 'next/server';
import { getWhatsAppConnectionStatus } from '@/lib/whatsapp';

/**
 * Public WhatsApp Availability Endpoint
 * Returns only if WhatsApp service is available for login
 * No authentication required - used by login page
 */
export async function GET() {
  try {
    // Get minimal connection status for public consumption
    const connectionStatus = await getWhatsAppConnectionStatus();
    
    // Only return if service is ready and authenticated
    const isAvailable = connectionStatus.isReady && !connectionStatus.isInitializing;
    
    return NextResponse.json({
      available: isAvailable
    });
    
  } catch (error) {
    // Don't expose internal errors to public endpoint
    return NextResponse.json({
      available: false
    });
  }
}