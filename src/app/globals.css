@import "tailwindcss";

@source "./src/**/*.{js,ts,jsx,tsx,mdx}";

:root {
  /* VoxStudent Modern Theme - Beautiful gradients from email system */
  --background: #ffffff;
  --foreground: #1f2937;
  --card: #ffffff;
  --card-foreground: #1f2937;
  --popover: #ffffff;
  --popover-foreground: #1f2937;
  
  /* Beautiful gradient colors from email system */
  --primary: #667eea;
  --primary-dark: #764ba2;
  --primary-foreground: #ffffff;
  
  /* Complementary colors */
  --secondary: #f8fafc;
  --secondary-foreground: #0f172a;
  --muted: #f1f5f9;
  --muted-foreground: #64748b;
  --accent: #e0e7ff;
  --accent-foreground: #3730a3;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e2e8f0;
  --input: #f1f5f9;
  --ring: #667eea;
  --radius: 0.75rem;
  
  /* Custom gradient variables */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-light: linear-gradient(135deg, #a8b5ff 0%, #b19cd9 100%);
  --gradient-dark: linear-gradient(135deg, #4c63d2 0%, #5a4270 100%);
  --gradient-accent: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
}


/* Remove dark mode - VoxStudent uses consistent light theme */
/* @media (prefers-color-scheme: dark) - REMOVED */

body {
  background: var(--background);
  color: var(--foreground);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Prevent contentEditable from being set globally */
*:not(input):not(textarea):not([contenteditable="true"]) {
  -webkit-user-modify: read-only;
  -moz-user-modify: read-only;
  user-modify: read-only;
}

/* Common span classes that might show I-beam cursor */
span.text-sm, span.text-xs, span.text-lg, span.text-xl, span.text-2xl,
span.font-medium, span.font-semibold, span.font-bold,
span.text-gray-500, span.text-gray-600, span.text-gray-700, span.text-gray-800, span.text-gray-900,
span.text-muted-foreground, span.text-foreground,
span.text-white, span.text-blue-600, span.text-green-600, span.text-red-600,
.sr-only span {
  cursor: default !important;
}

/* Override any potential Tailwind cursor utilities */
*[class*="cursor-text"]:not(input):not(textarea):not([contenteditable="true"]),
*[class*="cursor-auto"]:not(input):not(textarea):not([contenteditable="true"]),
span[class*="cursor-"], span.cursor-auto, span.cursor-text {
  cursor: default !important;
}

/* Prevent text cursor on common problematic elements */
div:not([contenteditable="true"]),
span:not([contenteditable="true"]),
p:not([contenteditable="true"]) {
  cursor: default !important;
}

/* Buttons and clickable elements should have pointer cursor */
button, .btn, [role="button"], input[type="button"], 
input[type="submit"], input[type="reset"], a,
[data-clickable], .clickable, 
[onClick], [onSelect], [data-radix-collection-item],
.cursor-pointer, .hover\:bg-\*, .hover\:border-\*,
tbody tr:hover, .table-row-hover,
.card-hover:hover, [role="tab"], [role="menuitem"],
[role="option"], [data-state="open"], [data-state="closed"] {
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Beautiful gradient utilities */
.gradient-primary {
  background: var(--gradient-primary);
}

.gradient-light {
  background: var(--gradient-light);
}

.gradient-dark {
  background: var(--gradient-dark);
}

.gradient-accent {
  background: var(--gradient-accent);
}

/* Glass effect utilities */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Smooth animations */
.smooth-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Card hover effects */
.card-hover {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #4c63d2, #5a4270);
}

/* Loading animation */
@keyframes pulse-gradient {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.pulse-gradient {
  animation: pulse-gradient 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Button gradient hover effects */
.btn-gradient {
  background: var(--gradient-primary);
  transition: all 0.3s ease;
}

.btn-gradient:hover {
  background: var(--gradient-dark);
  transform: translateY(-1px);
  box-shadow: 0 10px 15px -3px rgba(102, 126, 234, 0.4);
}

/* Notification styles */
.notification-dot {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

/* Danger/Delete button styles */
.btn-danger, .variant-destructive, .text-destructive,
button[title="Excluir"], button[title="Delete"], 
button.border-red-300, .bg-red-600, .bg-destructive {
  background-color: #dc2626;
  color: white;
  border-color: #dc2626;
}

.btn-danger:hover, .variant-destructive:hover,
button[title="Excluir"]:hover, button[title="Delete"]:hover,
button.border-red-300:hover {
  background-color: #b91c1c;
  color: white;
  border-color: #b91c1c;
}

/* Modern focus styles */
.focus-gradient:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  border-color: var(--primary);
}

/* Dialog overlay fix */
[data-radix-dialog-overlay] {
  background-color: rgba(0, 0, 0, 0.8);
}

/* Dialog content background fix */
[data-radix-dialog-content] {
  background-color: white;
  max-width: 95vw;
}

/* Prevent contenteditable attribute from being added to page elements */
h1, h2, h3, h4, h5, h6, p, span, div {
  -webkit-user-modify: read-only;
}

.sidebar *, 
.sidebar span, 
.sidebar div, 
.sidebar a {
  cursor: default !important;
}