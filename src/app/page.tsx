'use client';

import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, User, TrendingUp, Users, Calendar, MessageSquare, BarChart3, Settings, CheckSquare, BookOpen, Shield, Sparkles, Award, Clock, UserPlus, GraduationCap } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { isSuperAdmin, isAdminOrSuperAdmin } from '@/lib/roles';
import { useEffect, useState, useCallback } from 'react';
import dashboardService, { type DashboardData } from '@/lib/services/dashboard.service';

export default function Dashboard() {
  const { user, loading, signOut } = useAuth();
  const router = useRouter();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [dataLoading, setDataLoading] = useState(false);

  // All hooks must be at the top, before any early returns
  const loadDashboardData = useCallback(async () => {
    setDataLoading(true);
    try {
      const data = await dashboardService.getDashboardData();
      setDashboardData(data);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setDataLoading(false);
    }
  }, []);

  const isAdmin = user ? isAdminOrSuperAdmin(user) : false;
  const isSuperAdminUser = user ? isSuperAdmin(user) : false;

  useEffect(() => {
    if (user && isAdmin) {
      loadDashboardData();
    }
  }, [user, isAdmin, loadDashboardData]);

  // Early returns after all hooks
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#667eea] to-[#764ba2]">
        <div className="text-center text-white">
          <Loader2 className="mx-auto animate-spin" size={48} />
          <p className="mt-4">Carregando...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    router.push('/login');
    return null;
  }

  return (
    <div className="space-y-8">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-2xl p-8 text-white shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">
                Bem-vindo de volta! 👋
              </h1>
              <p className="text-white/90 text-lg">
                {user.profile?.fullName || user.email}
              </p>
              <p className="text-white/70 text-sm mt-1">
                {isAdmin ? 'Administrador' : 'Usuário'} • {new Date().toLocaleDateString('pt-BR', { 
                  weekday: 'long', 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}
              </p>
            </div>
            <div className="hidden md:block">
              <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                <User className="w-10 h-10" />
              </div>
            </div>
          </div>
        </div>

        {/* Admin Analytics & Insights */}
        {isAdmin && (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {dataLoading ? (
                // Loading skeleton
                Array.from({ length: 4 }).map((_, i) => (
                  <Card key={i} className="hover:shadow-lg transition-all duration-300">
                    <CardContent className="p-6">
                      <div className="animate-pulse">
                        <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
                        <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                        <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <>
                  <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-blue-500">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Total de Alunos</p>
                          <p className="text-3xl font-bold text-gray-900">{dashboardData?.stats.totalStudents || 0}</p>
                          <p className="text-sm text-green-600 flex items-center mt-1">
                            <UserPlus className="w-4 h-4 mr-1" />
                            +{dashboardData?.stats.newStudentsThisMonth || 0} novos este mês
                          </p>
                        </div>
                        <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                          <Users className="w-6 h-6 text-white" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-purple-500">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Turmas Ativas</p>
                          <p className="text-3xl font-bold text-gray-900">{dashboardData?.stats.activeClasses || 0}</p>
                          <p className="text-sm text-blue-600 flex items-center mt-1">
                            <BarChart3 className="w-4 h-4 mr-1" />
                            {dashboardData?.stats.averageAttendanceRate || 0}% presença média
                          </p>
                        </div>
                        <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                          <Calendar className="w-6 h-6 text-white" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-green-500">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Mentorias</p>
                          <p className="text-3xl font-bold text-gray-900">{dashboardData?.stats.scheduledMentoringSessions || 0}</p>
                          <p className="text-sm text-orange-600 flex items-center mt-1">
                            <Clock className="w-4 h-4 mr-1" />
                            {dashboardData?.stats.mentoringCompletionPercentage || 0}% agendadas de {dashboardData?.stats.totalAvailableMentoringSessions || 0} mentorias
                          </p>
                        </div>
                        <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
                          <GraduationCap className="w-6 h-6 text-white" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-orange-500">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Próximas Aulas</p>
                          <p className="text-3xl font-bold text-gray-900">{dashboardData?.stats.upcomingLessons || 0}</p>
                          <p className="text-sm text-blue-600 flex items-center mt-1">
                            <Clock className="w-4 h-4 mr-1" />
                            Hoje
                          </p>
                        </div>
                        <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
                          <Clock className="w-6 h-6 text-white" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </>
              )}
            </div>

            {/* Advanced Analytics Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Performance Chart */}
              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChart3 className="w-5 h-5 mr-2 text-purple-600" />
                    Performance das Turmas
                  </CardTitle>
                  <CardDescription>
                    Taxa de presença por turma nos últimos 30 dias
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {(dashboardData?.classPerformance || []).map((class_, index) => (
                      <div key={index} className="flex items-center space-x-4">
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm font-medium text-gray-900">{class_.name}</span>
                            <span className="text-sm text-gray-600">{class_.attendance}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className={`h-2 rounded-full ${class_.color}`}
                              style={{ width: `${class_.attendance}%` }}
                            ></div>
                          </div>
                          <span className="text-xs text-gray-500">{class_.students} alunos</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Quick Insights */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center text-lg">
                      <Shield className="w-5 h-5 mr-2 text-indigo-600" />
                      Alertas do Sistema
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-start space-x-3 p-3 bg-yellow-50 rounded-lg border-l-4 border-yellow-400">
                      <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm font-medium text-yellow-800">3 Alunos com Baixa Frequência</p>
                        <p className="text-xs text-yellow-700">Frequência abaixo de 75%</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg border-l-4 border-blue-400">
                      <div className="w-2 h-2 bg-blue-400 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm font-medium text-blue-800">5 Novos Alunos Esta Semana</p>
                        <p className="text-xs text-blue-700">Aguardando primeira aula</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3 p-3 bg-green-50 rounded-lg border-l-4 border-green-400">
                      <div className="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm font-medium text-green-800">WhatsApp Conectado</p>
                        <p className="text-xs text-green-700">Sistema funcionando normalmente</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center text-lg">
                      <Calendar className="w-5 h-5 mr-2 text-blue-600" />
                      Próximas Aulas
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {(dashboardData?.upcomingLessons || []).map((lesson, index) => (
                        <div key={index} className="flex items-center justify-between p-2 rounded-lg bg-gray-50">
                          <div>
                            <p className="font-medium text-sm">{lesson.class}</p>
                            <p className="text-xs text-gray-600">{lesson.students} alunos</p>
                          </div>
                          <span className="text-sm font-medium text-gray-900">{lesson.time}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </>
        )}

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl">Ações Rápidas</CardTitle>
            <CardDescription>
              Acesse rapidamente as funcionalidades mais utilizadas
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isAdmin ? (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <Button
                  onClick={() => router.push('/admin/students/new')}
                  className="h-20 flex-col bg-gradient-to-br from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white border-0"
                >
                  <Users className="w-6 h-6 mb-2" />
                  Novo Aluno
                </Button>
                <Button
                  onClick={() => router.push('/admin/classes/new')}
                  className="h-20 flex-col bg-gradient-to-br from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white border-0"
                >
                  <Calendar className="w-6 h-6 mb-2" />
                  Nova Turma
                </Button>
                <Button
                  onClick={() => router.push('/admin/attendance')}
                  className="h-20 flex-col bg-gradient-to-br from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white border-0"
                >
                  <CheckSquare className="w-6 h-6 mb-2" />
                  Presença
                </Button>
                <Button
                  onClick={() => router.push('/admin/reports')}
                  className="h-20 flex-col bg-gradient-to-br from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white border-0"
                >
                  <BarChart3 className="w-6 h-6 mb-2" />
                  Relatórios
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-2 gap-4">
                <Button variant="outline" disabled className="h-20 flex-col">
                  <CheckSquare className="w-6 h-6 mb-2" />
                  Marcar Presença
                  <span className="text-xs text-muted-foreground mt-1">Em breve</span>
                </Button>
                <Button variant="outline" disabled className="h-20 flex-col">
                  <Calendar className="w-6 h-6 mb-2" />
                  Minhas Aulas
                  <span className="text-xs text-muted-foreground mt-1">Em breve</span>
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Recent Activity - Admin Only */}
        {isAdmin && (
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Atividade Recente</CardTitle>
              <CardDescription>
                Últimas ações no sistema
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {(dashboardData?.recentActivity || []).map((activity, index) => {
                  const IconComponent = activity.icon === 'Users' ? Users : 
                                       activity.icon === 'CheckSquare' ? CheckSquare :
                                       activity.icon === 'Calendar' ? Calendar : BarChart3;
                  return (
                  <div key={index} className="flex items-center space-x-4 p-3 rounded-lg bg-gray-50">
                    <div className={`w-10 h-10 rounded-full bg-white flex items-center justify-center ${activity.color}`}>
                      <IconComponent className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">{activity.action}</p>
                      <p className="text-sm text-gray-600">{activity.user}</p>
                    </div>
                    <p className="text-sm text-gray-400">{activity.time}</p>
                  </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
  );
}
