import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/contexts/AuthContext";
import { validateAdminConfiguration } from "@/lib/auth";
import { adminConfigService } from "@/lib/services/admin-config.service";
import { Toaster } from "@/components/ui/sonner";
import { AppLayout } from "@/components/layouts/app-layout";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "VoxStudent - Sistema de Gestão Educacional",
  description: "Sistema de gestão educacional com autenticação por magic links",
  icons: {
    icon: [
      { url: '/favicon.svg', type: 'image/svg+xml' },
      { url: '/favicon.ico', type: 'image/x-icon', sizes: '32x32' }
    ],
    apple: '/apple-touch-icon.svg'
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Initialize admin configuration on server startup
  if (typeof window === 'undefined') {
    adminConfigService.initialize();
    validateAdminConfiguration();
  }

  return (
    <html lang="pt-BR">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <AuthProvider>
          <AppLayout>
            {children}
          </AppLayout>
          <Toaster />
        </AuthProvider>
        
        {/* Fix contentEditable issue */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Remove contentEditable from elements that shouldn't have it
              document.addEventListener('DOMContentLoaded', function() {
                function removeUnwantedContentEditable() {
                  const elements = document.querySelectorAll('*[contenteditable]');
                  elements.forEach(el => {
                    if (el.tagName !== 'INPUT' && 
                        el.tagName !== 'TEXTAREA' && 
                        !el.hasAttribute('data-allow-editable')) {
                      el.removeAttribute('contenteditable');
                    }
                  });
                }
                
                // Run immediately
                removeUnwantedContentEditable();
                
                // Run after any DOM changes
                const observer = new MutationObserver(removeUnwantedContentEditable);
                observer.observe(document.body, { 
                  attributes: true, 
                  childList: true, 
                  subtree: true,
                  attributeFilter: ['contenteditable']
                });
              });
            `,
          }}
        />
      </body>
    </html>
  );
}
