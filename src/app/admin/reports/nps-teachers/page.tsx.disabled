'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { isSuperAdmin } from '@/lib/roles';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line
} from 'recharts';
import { 
  TrendingUp, 
  Users, 
  BookOpen, 
  Calendar, 
  Filter,
  Download,
  RefreshCw,
  ArrowLeft,
  Star,
  ThumbsUp,
  ThumbsDown,
  Minus
} from 'lucide-react';
import { teacherNpsService, type TeacherNpsData, type TeacherNpsFilters } from '@/lib/services/teacher-nps.service';
import { coursesService } from '@/lib/services/courses.service';
import { classesService } from '@/lib/services/classes.service';
import { teachersService } from '@/lib/services/teachers.service';
import { AdminPageLayout } from '@/components/layouts/admin-page-layout';

interface Course {
  id: string;
  name: string;
}

interface Class {
  id: string;
  name: string;
  courseId: string;
}

interface Teacher {
  id: string;
  name: string;
}

export default function TeacherNpsReportPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  
  // Data states
  const [teachersNpsData, setTeachersNpsData] = useState<TeacherNpsData[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [classes, setClasses] = useState<Class[]>([]);
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isFiltering, setIsFiltering] = useState(false);

  // Filter states
  const [filters, setFilters] = useState<TeacherNpsFilters>({
    startDate: '',
    endDate: '',
    courseId: '',
    classId: '',
    studentId: ''
  });

  // Check authentication and authorization
  useEffect(() => {
    if (!loading && (!user || !isSuperAdmin(user))) {
      toast.error('Acesso negado. Apenas super administradores podem acessar este relatório.');
      router.push('/');
      return;
    }

    if (user && isSuperAdmin(user)) {
      loadInitialData();
    }
  }, [user, loading, router]);

  const loadInitialData = async () => {
    try {
      setIsLoading(true);
      
      // Load all required data in parallel
      const [npsResponse, coursesResponse, classesResponse, teachersResponse] = await Promise.all([
        teacherNpsService.getAllTeachersNps(),
        coursesService.getCourses(),
        classesService.getClasses(),
        teachersService.getTeachers()
      ]);

      if (npsResponse.success && npsResponse.data) {
        setTeachersNpsData(npsResponse.data);
      }

      if (coursesResponse.success && coursesResponse.data) {
        setCourses(coursesResponse.data);
      }

      if (classesResponse.success && classesResponse.data) {
        setClasses(classesResponse.data);
      }

      if (teachersResponse.success && teachersResponse.data) {
        setTeachers(teachersResponse.data);
      }

    } catch (error) {
      console.error('Error loading initial data:', error);
      toast.error('Erro ao carregar dados iniciais');
    } finally {
      setIsLoading(false);
    }
  };

  const applyFilters = async () => {
    try {
      setIsFiltering(true);
      
      const response = await teacherNpsService.getAllTeachersNps(filters);
      
      if (response.success && response.data) {
        setTeachersNpsData(response.data);
        toast.success('Filtros aplicados com sucesso');
      } else {
        toast.error(response.error || 'Erro ao aplicar filtros');
      }
    } catch (error) {
      console.error('Error applying filters:', error);
      toast.error('Erro ao aplicar filtros');
    } finally {
      setIsFiltering(false);
    }
  };

  const clearFilters = async () => {
    setFilters({
      startDate: '',
      endDate: '',
      courseId: '',
      classId: '',
      studentId: ''
    });
    
    // Reload data without filters
    const response = await teacherNpsService.getAllTeachersNps();
    if (response.success && response.data) {
      setTeachersNpsData(response.data);
    }
  };

  const getNpsScoreColor = (score: number) => {
    if (score >= 70) return 'text-green-600';
    if (score >= 50) return 'text-yellow-600';
    if (score >= 30) return 'text-orange-600';
    return 'text-red-600';
  };

  const getNpsScoreBadgeVariant = (score: number): "default" | "secondary" | "destructive" | "outline" => {
    if (score >= 70) return 'default';
    if (score >= 50) return 'secondary';
    if (score >= 30) return 'outline';
    return 'destructive';
  };

  // Calculate overall statistics
  const overallStats = teachersNpsData.reduce((acc, teacher) => {
    acc.totalFeedback += teacher.stats.total;
    acc.totalGood += teacher.stats.good;
    acc.totalNeutral += teacher.stats.neutral;
    acc.totalBad += teacher.stats.bad;
    return acc;
  }, { totalFeedback: 0, totalGood: 0, totalNeutral: 0, totalBad: 0 });

  const overallNpsScore = overallStats.totalFeedback > 0 
    ? Math.round(((overallStats.totalGood - overallStats.totalBad) / overallStats.totalFeedback) * 100)
    : 0;

  // Prepare chart data
  const chartData = teachersNpsData.map(teacher => ({
    name: teacher.teacher.name.split(' ')[0], // First name only for chart
    npsScore: teacher.stats.npsScore,
    total: teacher.stats.total,
    good: teacher.stats.good,
    neutral: teacher.stats.neutral,
    bad: teacher.stats.bad
  }));

  const pieChartData = [
    { name: 'Positivo', value: overallStats.totalGood, color: '#22c55e' },
    { name: 'Neutro', value: overallStats.totalNeutral, color: '#f59e0b' },
    { name: 'Negativo', value: overallStats.totalBad, color: '#ef4444' }
  ];

  if (loading || isLoading) {
    return (
      <AdminPageLayout
        title="Relatório NPS por Professor"
        description="Carregando dados..."
        icon={TrendingUp}
        backUrl="/admin/reports"
      >
        <div className="flex justify-center items-center h-64">
          <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
        </div>
      </AdminPageLayout>
    );
  }

  return (
    <AdminPageLayout
      title="Relatório NPS por Professor"
      description="Análise detalhada do Net Promoter Score por professor com filtros por período"
      icon={TrendingUp}
      backUrl="/admin/reports"
    >
      {/* Filters Section */}
      <Card className="shadow-xl border-0">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-100 rounded-t-lg">
          <CardTitle className="flex items-center text-xl font-bold text-gray-900">
            <Filter className="w-5 h-5 mr-2" />
            Filtros
          </CardTitle>
          <CardDescription>
            Filtre os dados por período, curso, turma ou professor
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
            <div>
              <Label htmlFor="startDate">Data Inicial</Label>
              <Input
                id="startDate"
                type="date"
                value={filters.startDate}
                onChange={(e) => setFilters(prev => ({ ...prev, startDate: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="endDate">Data Final</Label>
              <Input
                id="endDate"
                type="date"
                value={filters.endDate}
                onChange={(e) => setFilters(prev => ({ ...prev, endDate: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="courseId">Curso</Label>
              <Select value={filters.courseId} onValueChange={(value) => setFilters(prev => ({ ...prev, courseId: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Todos os cursos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Todos os cursos</SelectItem>
                  {courses.map(course => (
                    <SelectItem key={course.id} value={course.id}>
                      {course.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="classId">Turma</Label>
              <Select value={filters.classId} onValueChange={(value) => setFilters(prev => ({ ...prev, classId: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Todas as turmas" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Todas as turmas</SelectItem>
                  {classes
                    .filter(cls => !filters.courseId || cls.courseId === filters.courseId)
                    .map(cls => (
                      <SelectItem key={cls.id} value={cls.id}>
                        {cls.name}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="flex gap-2">
            <Button onClick={applyFilters} disabled={isFiltering} className="bg-blue-600 hover:bg-blue-700">
              {isFiltering ? (
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Filter className="w-4 h-4 mr-2" />
              )}
              Aplicar Filtros
            </Button>
            <Button onClick={clearFilters} variant="outline">
              Limpar Filtros
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Overall Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="shadow-xl border-0 hover:shadow-2xl transition-all duration-300">
          <CardHeader className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-t-lg flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-lg font-bold text-gray-900">NPS Geral</CardTitle>
            <div className="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
              <Star className="h-5 w-5 text-white" />
            </div>
          </CardHeader>
          <CardContent className="pt-6">
            <div className={`text-3xl font-bold ${getNpsScoreColor(overallNpsScore)}`}>
              {overallNpsScore}
            </div>
            <p className="text-sm text-gray-600 mt-2">
              Score médio de todos os professores
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-xl border-0 hover:shadow-2xl transition-all duration-300">
          <CardHeader className="bg-gradient-to-r from-green-50 to-green-100 rounded-t-lg flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-lg font-bold text-gray-900">Total de Avaliações</CardTitle>
            <div className="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
              <Users className="h-5 w-5 text-white" />
            </div>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="text-3xl font-bold text-gray-900">{overallStats.totalFeedback}</div>
            <p className="text-sm text-gray-600 mt-2">
              Feedbacks coletados
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-xl border-0 hover:shadow-2xl transition-all duration-300">
          <CardHeader className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-t-lg flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-lg font-bold text-gray-900">Professores Ativos</CardTitle>
            <div className="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
              <BookOpen className="h-5 w-5 text-white" />
            </div>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="text-3xl font-bold text-gray-900">{teachersNpsData.length}</div>
            <p className="text-sm text-gray-600 mt-2">
              Com avaliações NPS
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-xl border-0 hover:shadow-2xl transition-all duration-300">
          <CardHeader className="bg-gradient-to-r from-orange-50 to-orange-100 rounded-t-lg flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-lg font-bold text-gray-900">Taxa de Satisfação</CardTitle>
            <div className="p-2 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg">
              <ThumbsUp className="h-5 w-5 text-white" />
            </div>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="text-3xl font-bold text-gray-900">
              {overallStats.totalFeedback > 0
                ? Math.round((overallStats.totalGood / overallStats.totalFeedback) * 100)
                : 0}%
            </div>
            <p className="text-sm text-gray-600 mt-2">
              Avaliações positivas
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="shadow-xl border-0">
          <CardHeader>
            <CardTitle>NPS por Professor</CardTitle>
            <CardDescription>Score NPS de cada professor</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip
                  formatter={(value, name) => [value, name === 'npsScore' ? 'NPS Score' : name]}
                  labelFormatter={(label) => `Professor: ${label}`}
                />
                <Bar dataKey="npsScore" fill="#3b82f6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card className="shadow-xl border-0">
          <CardHeader>
            <CardTitle>Distribuição Geral</CardTitle>
            <CardDescription>Distribuição de todas as avaliações</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={pieChartData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value, percent }) => `${name}: ${value} (${(percent * 100).toFixed(0)}%)`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {pieChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Teachers Table */}
      <Card className="shadow-xl border-0">
        <CardHeader>
          <CardTitle>Detalhamento por Professor</CardTitle>
          <CardDescription>
            Estatísticas detalhadas de NPS para cada professor
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-semibold">Professor</th>
                  <th className="text-left py-3 px-4 font-semibold">Especialização</th>
                  <th className="text-center py-3 px-4 font-semibold">NPS Score</th>
                  <th className="text-center py-3 px-4 font-semibold">Total</th>
                  <th className="text-center py-3 px-4 font-semibold">Positivo</th>
                  <th className="text-center py-3 px-4 font-semibold">Neutro</th>
                  <th className="text-center py-3 px-4 font-semibold">Negativo</th>
                </tr>
              </thead>
              <tbody>
                {teachersNpsData.map((teacher) => (
                  <tr key={teacher.teacher.id} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <div>
                        <div className="font-medium">{teacher.teacher.name}</div>
                        {teacher.teacher.email && (
                          <div className="text-sm text-gray-500">{teacher.teacher.email}</div>
                        )}
                      </div>
                    </td>
                    <td className="py-3 px-4 text-sm text-gray-600">
                      {teacher.teacher.specialization || '-'}
                    </td>
                    <td className="py-3 px-4 text-center">
                      <Badge variant={getNpsScoreBadgeVariant(teacher.stats.npsScore)}>
                        {teacher.stats.npsScore}
                      </Badge>
                    </td>
                    <td className="py-3 px-4 text-center font-medium">
                      {teacher.stats.total}
                    </td>
                    <td className="py-3 px-4 text-center">
                      <div className="flex items-center justify-center">
                        <ThumbsUp className="w-4 h-4 text-green-600 mr-1" />
                        <span className="text-green-600 font-medium">
                          {teacher.stats.good} ({teacher.stats.goodPercentage}%)
                        </span>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-center">
                      <div className="flex items-center justify-center">
                        <Minus className="w-4 h-4 text-yellow-600 mr-1" />
                        <span className="text-yellow-600 font-medium">
                          {teacher.stats.neutral} ({teacher.stats.neutralPercentage}%)
                        </span>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-center">
                      <div className="flex items-center justify-center">
                        <ThumbsDown className="w-4 h-4 text-red-600 mr-1" />
                        <span className="text-red-600 font-medium">
                          {teacher.stats.bad} ({teacher.stats.badPercentage}%)
                        </span>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {teachersNpsData.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <Users className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>Nenhum dado de NPS encontrado para os filtros selecionados.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </AdminPageLayout>
  );
}
