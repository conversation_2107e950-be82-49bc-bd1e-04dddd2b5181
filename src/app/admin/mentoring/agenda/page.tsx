'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState, useCallback, Suspense } from 'react';
import { toast } from 'sonner';
import { isAdminOrSuperAdmin } from '@/lib/roles';
import { MentoringSlot } from '@/lib/services/mentoring.service';
import { teachersService, Teacher } from '@/lib/services/teachers.service';
import { useAutoSave } from '@/hooks/useAutoSave';

// Components
import {
  AdminPageLayout,
  FormCard,
  ModernInput,
  ModernSelect,
  Button,
  Badge
} from '@/components/ui/modern';
import { AutoSaveIndicator } from '@/components/ui/auto-save-indicator';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Switch } from '@/components/ui/switch';
import { useConfirmationDialog } from '@/components/ui/confirmation-dialog';
import { ReminderTemplatePreview } from '@/components/ReminderTemplatePreview';

// Icons
import { 
  Loader2, Calendar, ArrowLeft, Plus, Search, Edit, Trash2, Clock, 
 User, CalendarDays, UserCheck, Bell
} from 'lucide-react';

interface SlotFormData {
  teacherId: string;
  dayOfWeek: string;
  startTime: string;
  endTime: string;
  duration: string;
  maxBookingsPerWeek: string;
  googleCalendarId: string;
  // Reminder configuration
  reminderEnabled: boolean;
  reminderHoursBefore: string;
  reminderTemplateId: string;
}

const dayOfWeekOptions = [
  { value: 'monday', label: 'Segunda-feira' },
  { value: 'tuesday', label: 'Terça-feira' },
  { value: 'wednesday', label: 'Quarta-feira' },
  { value: 'thursday', label: 'Quinta-feira' },
  { value: 'friday', label: 'Sexta-feira' },
  { value: 'saturday', label: 'Sábado' },
  { value: 'sunday', label: 'Domingo' },
];

const durationOptions = [
  { value: '30', label: '30 minutos' },
  { value: '45', label: '45 minutos' },
  { value: '60', label: '1 hora' },
  { value: '90', label: '1h 30min' },
  { value: '120', label: '2 horas' },
];

function MentoringAgendaPageContent() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Get mode and ID from URL parameters
  const mode = searchParams.get('mode') || 'list'; // list, new, edit
  const slotId = searchParams.get('id');
  
  // State
  const [slots, setSlots] = useState<MentoringSlot[]>([]);
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [reminderTemplates, setReminderTemplates] = useState<any[]>([]);
  const [selectedSlot, setSelectedSlot] = useState<MentoringSlot | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState<string | null>(null);
  const { showConfirmation, ConfirmationDialog } = useConfirmationDialog();
  
  const [formData, setFormData] = useState<SlotFormData>({
    teacherId: '',
    dayOfWeek: '',
    startTime: '',
    endTime: '',
    duration: '60',
    maxBookingsPerWeek: '5',
    googleCalendarId: '',
    // Reminder configuration
    reminderEnabled: false,
    reminderHoursBefore: '2',
    reminderTemplateId: ''
  });

  // Navigation helpers
  const navigateToMode = useCallback((newMode: string, id?: string) => {
    const params = new URLSearchParams();
    params.set('mode', newMode);
    if (id) params.set('id', id);
    router.push(`/admin/mentoring/agenda?${params.toString()}`);
  }, [router]);

  const navigateToList = useCallback(() => {
    router.push('/admin/mentoring/agenda');
  }, [router]);

  // AutoSave function for edit mode
  const autoSaveSlot = useCallback(async (data: SlotFormData) => {
    if (!slotId || mode !== 'edit') return;

    const payload = {
      dayOfWeek: data.dayOfWeek,
      startTime: data.startTime,
      endTime: data.endTime,
      duration: data.duration ? parseInt(data.duration) : undefined,
      maxBookingsPerWeek: data.maxBookingsPerWeek ? parseInt(data.maxBookingsPerWeek) : undefined,
      googleCalendarId: data.googleCalendarId.trim() || undefined,
      // Reminder configuration
      reminderEnabled: data.reminderEnabled,
      reminderHoursBefore: data.reminderEnabled ? parseInt(data.reminderHoursBefore) : undefined,
      reminderTemplateId: data.reminderEnabled ? (data.reminderTemplateId || undefined) : undefined,
    };

    const token = localStorage.getItem('auth_token');
    if (!token) {
      throw new Error('Sessão expirada');
    }

    const response = await fetch(`/api/mentoring/slots/${slotId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Falha ao salvar agenda');
    }

    return response.json();
  }, [slotId, mode]);

  // AutoSave hook for edit mode
  const autoSaveState = useAutoSave({
    data: formData,
    onSave: autoSaveSlot,
    config: { debounceMs: 2000, enableToast: false },
    isValid: (data) => !!data.teacherId && !!data.dayOfWeek && !!data.startTime && !!data.endTime,
    enabled: mode === 'edit'
  });

  // Data fetching
  const fetchSlots = useCallback(async (search?: string) => {
    try {
      if (mode === 'list') {
        setIsLoading(true);
      }
      setError(null);

      const token = localStorage.getItem('auth_token');
      const headers: HeadersInit = {};
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const url = search
        ? `/api/mentoring/slots?search=${encodeURIComponent(search)}`
        : '/api/mentoring/slots';

      const response = await fetch(url, { headers });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch slots');
      }

      const result = await response.json();
      setSlots(result.data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      if (mode === 'list') {
        setIsLoading(false);
      }
    }
  }, [mode]);

  const fetchTeachers = useCallback(async () => {
    try {
      const response = await teachersService.getTeachers();
      if (response.success && response.data) {
        setTeachers(response.data.filter(teacher => teacher.status === 'active'));
      }
    } catch (err) {
      console.error('Error fetching teachers:', err);
    }
  }, []);

  const fetchReminderTemplates = useCallback(async () => {
    try {
      const response = await fetch('/api/reminder-templates?category=mentoria');
      if (response.ok) {
        const result = await response.json();
        setReminderTemplates(result.data || []);
      }
    } catch (error) {
      console.error('Error fetching reminder templates:', error);
      toast.error('Erro ao carregar templates de lembrete');
    }
  }, []);

  const fetchSlot = useCallback(async (id: string) => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('auth_token');
      const headers: HeadersInit = {};
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`/api/mentoring/slots/${id}`, { headers });

      if (!response.ok) {
        throw new Error('Falha ao carregar agenda');
      }

      const result = await response.json();
      const slotData = result.data;
      setSelectedSlot(slotData);

      // Populate form for editing
      setFormData({
        teacherId: slotData.teacherId || '',
        dayOfWeek: slotData.dayOfWeek || '',
        startTime: slotData.startTime || '',
        endTime: slotData.endTime || '',
        duration: slotData.duration ? slotData.duration.toString() : '60',
        maxBookingsPerWeek: slotData.maxBookingsPerWeek ? slotData.maxBookingsPerWeek.toString() : '5',
        googleCalendarId: slotData.googleCalendarId || '',
        reminderEnabled: slotData.reminderEnabled || false,
        reminderHoursBefore: slotData.reminderHoursBefore ? slotData.reminderHoursBefore.toString() : '2',
        reminderTemplateId: slotData.reminderTemplateId || ''
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Effects
  useEffect(() => {
    if (!loading && !isAdminOrSuperAdmin(user)) {
      router.push('/');
      return;
    }

    if (isAdminOrSuperAdmin(user)) {
      if (mode === 'edit' && slotId) {
        fetchSlot(slotId);
        fetchTeachers();
        fetchReminderTemplates();
      } else if (mode === 'list') {
        fetchSlots('');
      } else if (mode === 'new') {
        setIsLoading(false);
        fetchTeachers();
        fetchReminderTemplates();
        // Reset form for new slot
        setFormData({
          teacherId: '',
          dayOfWeek: '',
          startTime: '',
          endTime: '',
          duration: '60',
          maxBookingsPerWeek: '5',
          googleCalendarId: '',
          reminderEnabled: false,
          reminderHoursBefore: '2',
          reminderTemplateId: ''
        });
      }
    }
  }, [user, loading, router, mode, slotId, fetchSlot, fetchSlots, fetchTeachers, fetchReminderTemplates]);

  // Calculate end time when start time or duration changes
  useEffect(() => {
    if (formData.startTime && formData.duration) {
      const [hours, minutes] = formData.startTime.split(':').map(Number);
      const durationMinutes = parseInt(formData.duration);
      
      const totalMinutes = hours * 60 + minutes + durationMinutes;
      const endHours = Math.floor(totalMinutes / 60);
      const endMinutes = totalMinutes % 60;
      
      const endTime = `${endHours.toString().padStart(2, '0')}:${endMinutes.toString().padStart(2, '0')}`;
      
      if (endTime !== formData.endTime) {
        setFormData(prev => ({ ...prev, endTime }));
      }
    }
  }, [formData.startTime, formData.duration, formData.endTime]);

  // Form handlers
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.teacherId || !formData.dayOfWeek || !formData.startTime || !formData.endTime) {
      toast.error('Todos os campos obrigatórios devem ser preenchidos');
      return;
    }

    try {
      setIsSubmitting(true);
      const token = localStorage.getItem('auth_token');

      if (!token) {
        toast.error('Sessão expirada. Faça login novamente.');
        router.push('/login');
        return;
      }

      const payload = {
        teacherId: formData.teacherId,
        dayOfWeek: formData.dayOfWeek,
        startTime: formData.startTime,
        endTime: formData.endTime,
        duration: parseInt(formData.duration),
        maxBookingsPerWeek: parseInt(formData.maxBookingsPerWeek),
        googleCalendarId: formData.googleCalendarId.trim() || undefined,
        // Reminder configuration
        reminderEnabled: formData.reminderEnabled,
        reminderHoursBefore: formData.reminderEnabled ? parseInt(formData.reminderHoursBefore) : undefined,
        reminderTemplateId: formData.reminderEnabled ? (formData.reminderTemplateId || undefined) : undefined,
      };

      const headers: HeadersInit = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      };

      if (mode === 'new') {
        const response = await fetch('/api/mentoring/slots', {
          method: 'POST',
          headers,
          body: JSON.stringify(payload),
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || 'Falha ao criar agenda');
        }

        toast.success('Agenda criada com sucesso!');
        navigateToList();
      } else if (mode === 'edit' && slotId) {
        // In edit mode, just navigate since autosave handles saving
        navigateToList();
      }
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Erro ao salvar agenda');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSearch = useCallback(() => {
    fetchSlots(searchTerm);
  }, [fetchSlots, searchTerm]);

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (mode === 'list') {
        fetchSlots(searchTerm);
      }
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [searchTerm, mode, fetchSlots]);

  const handleDelete = (slotId: string, teacherName: string, dayOfWeek: string) => {
    showConfirmation({
      title: 'Excluir Agenda',
      description: `Tem certeza que deseja excluir a agenda de ${teacherName} para ${getDayOfWeekLabel(dayOfWeek)}? Esta ação não pode ser desfeita.`,
      confirmText: 'Excluir',
      cancelText: 'Cancelar',
      variant: 'destructive',
      icon: 'delete',
      onConfirm: async () => {
        try {
          const token = localStorage.getItem('auth_token');

          if (!token) {
            toast.error('Sessão expirada. Faça login novamente.');
            router.push('/login');
            return;
          }

          const response = await fetch(`/api/mentoring/slots/${slotId}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });

          if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || 'Falha ao excluir agenda');
          }

          toast.success('Agenda excluída com sucesso!');
          fetchSlots(searchTerm);
        } catch (err) {
          toast.error(err instanceof Error ? err.message : 'Erro ao excluir agenda');
        }
      }
    });
  };

  // Utility functions
  const getDayOfWeekLabel = (dayOfWeek: string) => {
    const option = dayOfWeekOptions.find(opt => opt.value === dayOfWeek);
    return option?.label || dayOfWeek;
  };

  const formatTime = (time: string) => {
    return time.slice(0, 5); // Remove seconds if present
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  // Loading state - only show full page loading for non-list modes
  if (loading || (isLoading && mode !== 'list')) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="mx-auto animate-spin" size={48} />
          <p className="mt-4 text-muted-foreground">
            {mode === 'edit' ? 'Carregando agenda...' : 'Carregando agendas...'}
          </p>
        </div>
      </div>
    );
  }

  // Permission check
  if (!isAdminOrSuperAdmin(user)) {
    return null;
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-destructive">Erro</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{error}</p>
            <Button onClick={() => navigateToList()} className="mt-4">
              Voltar para Lista
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Render based on mode
  if (mode === 'new') {
    return (
      <AdminPageLayout
        title="Nova Agenda de Mentoria"
        description="Configurar disponibilidade de mentoria para um professor"
        icon={CalendarDays}
        backUrl="/admin/mentoring/agenda"
      >
        <FormCard title="Configurar Agenda de Mentoria" icon={Calendar}>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 xl:grid-cols-3">
              <ModernSelect
                label="Professor"
                value={formData.teacherId}
                onValueChange={(value) => setFormData({ ...formData, teacherId: value })}
                icon={UserCheck}
                options={teachers.map(teacher => ({ value: teacher.id, label: teacher.name }))}
                placeholder="Selecione um professor"
                required
                className="md:col-span-2 xl:col-span-1"
              />

              <ModernSelect
                label="Dia da Semana"
                value={formData.dayOfWeek}
                onValueChange={(value) => setFormData({ ...formData, dayOfWeek: value })}
                icon={Calendar}
                options={dayOfWeekOptions}
                placeholder="Selecione o dia"
                required
              />

              <ModernSelect
                label="Duração"
                value={formData.duration}
                onValueChange={(value) => setFormData({ ...formData, duration: value })}
                icon={Clock}
                options={durationOptions}
                required
              />

              <ModernInput
                label="Horário de Início"
                type="time"
                value={formData.startTime}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, startTime: e.target.value })}
                icon={Clock}
                required
              />

              <ModernInput
                label="Horário de Término"
                type="time"
                value={formData.endTime}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, endTime: e.target.value })}
                icon={Clock}
                required
                disabled
              />

              <ModernInput
                label="Máx. Agendamentos por Semana"
                type="number"
                min="1"
                max="20"
                value={formData.maxBookingsPerWeek}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, maxBookingsPerWeek: e.target.value })}
                icon={Calendar}
              />

              <ModernInput
                label="ID do Google Calendar"
                value={formData.googleCalendarId}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, googleCalendarId: e.target.value })}
                placeholder="<EMAIL>"
                icon={Calendar}
                className="md:col-span-2 xl:col-span-3"
              />
            </div>

            {/* Reminder Configuration Section */}
            <div className="border-t pt-6">
              <div className="flex items-center space-x-2 mb-4">
                <Bell className="w-5 h-5 text-gray-600" />
                <h3 className="text-lg font-medium text-gray-900">Configuração de Lembretes</h3>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Switch
                    checked={formData.reminderEnabled}
                    onCheckedChange={(checked) => setFormData({ ...formData, reminderEnabled: checked })}
                  />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Ativar lembretes automáticos</p>
                    <p className="text-sm text-gray-600">Enviar lembretes por WhatsApp antes da mentoria</p>
                  </div>
                </div>

                {formData.reminderEnabled && (
                  <div className="grid gap-4 md:grid-cols-2 pl-6 border-l-2 border-blue-200">
                    <ModernSelect
                      label="Horas de Antecedência"
                      value={formData.reminderHoursBefore}
                      onValueChange={(value) => setFormData({ ...formData, reminderHoursBefore: value })}
                      icon={Clock}
                      options={[
                        { value: '1', label: '1 hora antes' },
                        { value: '2', label: '2 horas antes' },
                        { value: '3', label: '3 horas antes' },
                        { value: '6', label: '6 horas antes' },
                        { value: '12', label: '12 horas antes' },
                        { value: '24', label: '24 horas antes' },
                      ]}
                      required
                    />

                    <ModernSelect
                      label="Template de Lembrete"
                      value={formData.reminderTemplateId}
                      onValueChange={(value) => setFormData({ ...formData, reminderTemplateId: value })}
                      icon={Bell}
                      options={reminderTemplates.map(template => ({ 
                        value: template.id, 
                        label: template.name 
                      }))}
                      placeholder="Selecione um template"
                      required
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Reminder Template Preview */}
            {formData.reminderEnabled && formData.reminderTemplateId && (
              <ReminderTemplatePreview
                templateId={formData.reminderTemplateId}
                slotId={selectedSlot?.id}
                className="border-t pt-6"
              />
            )}

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">💡 Dicas para Google Calendar</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• O ID do Google Calendar é encontrado nas configurações do calendário</li>
                <li>• Geralmente tem o formato: <EMAIL></li>
                <li>• Se deixado em branco, não será criado evento no Google Calendar</li>
                <li>• Certifique-se de que o professor tenha acesso ao calendário</li>
              </ul>
            </div>

            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigateToList()}
                disabled={isSubmitting}
                className="flex items-center"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting || !formData.teacherId || !formData.dayOfWeek || !formData.startTime || !formData.endTime}
                className="bg-gradient-to-r from-[#667eea] to-[#764ba2] hover:from-[#5a6fd8] hover:to-[#6b4190] text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Salvando...
                  </>
                ) : (
                  <>
                    <CalendarDays className="w-4 h-4 mr-2" />
                    Salvar Agenda
                  </>
                )}
              </Button>
            </div>

          </form>
        </FormCard>
      </AdminPageLayout>
    );
  }

  if (mode === 'edit' && selectedSlot) {
    return (
      <AdminPageLayout
        title="Editar Agenda"
        description={`Edite a agenda de mentoria`}
        icon={CalendarDays}
        backUrl="/admin/mentoring/agenda"
      >
        <FormCard 
          title="Configurações da Agenda" 
          icon={Calendar}
          rightContent={
            <AutoSaveIndicator
              isAutoSaving={autoSaveState.isAutoSaving}
              lastSaved={autoSaveState.lastSaved}
              hasUnsavedChanges={autoSaveState.hasUnsavedChanges}
              error={autoSaveState.error}
            />
          }
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 xl:grid-cols-3">
              <ModernSelect
                label="Professor"
                value={formData.teacherId}
                onValueChange={(value) => setFormData({ ...formData, teacherId: value })}
                icon={UserCheck}
                options={teachers.map(teacher => ({ value: teacher.id, label: teacher.name }))}
                placeholder="Selecione um professor"
                required
                className="md:col-span-2 xl:col-span-1"
                disabled
              />

              <ModernSelect
                label="Dia da Semana"
                value={formData.dayOfWeek}
                onValueChange={(value) => setFormData({ ...formData, dayOfWeek: value })}
                icon={Calendar}
                options={dayOfWeekOptions}
                placeholder="Selecione o dia"
                required
              />

              <ModernSelect
                label="Duração"
                value={formData.duration}
                onValueChange={(value) => setFormData({ ...formData, duration: value })}
                icon={Clock}
                options={durationOptions}
                required
              />

              <ModernInput
                label="Horário de Início"
                type="time"
                value={formData.startTime}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, startTime: e.target.value })}
                icon={Clock}
                required
              />

              <ModernInput
                label="Horário de Término"
                type="time"
                value={formData.endTime}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, endTime: e.target.value })}
                icon={Clock}
                required
                disabled
              />

              <ModernInput
                label="Máx. Agendamentos por Semana"
                type="number"
                min="1"
                max="20"
                value={formData.maxBookingsPerWeek}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, maxBookingsPerWeek: e.target.value })}
                icon={Calendar}
              />

              <ModernInput
                label="ID do Google Calendar"
                value={formData.googleCalendarId}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, googleCalendarId: e.target.value })}
                placeholder="<EMAIL>"
                icon={Calendar}
                className="md:col-span-2 xl:col-span-3"
              />
            </div>

            {/* Reminder Configuration Section */}
            <div className="border-t pt-6">
              <div className="flex items-center space-x-2 mb-4">
                <Bell className="w-5 h-5 text-gray-600" />
                <h3 className="text-lg font-medium text-gray-900">Configuração de Lembretes</h3>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Switch
                    checked={formData.reminderEnabled}
                    onCheckedChange={(checked) => setFormData({ ...formData, reminderEnabled: checked })}
                  />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Ativar lembretes automáticos</p>
                    <p className="text-sm text-gray-600">Enviar lembretes por WhatsApp antes da mentoria</p>
                  </div>
                </div>

                {formData.reminderEnabled && (
                  <div className="grid gap-4 md:grid-cols-2 pl-6 border-l-2 border-blue-200">
                    <ModernSelect
                      label="Horas de Antecedência"
                      value={formData.reminderHoursBefore}
                      onValueChange={(value) => setFormData({ ...formData, reminderHoursBefore: value })}
                      icon={Clock}
                      options={[
                        { value: '1', label: '1 hora antes' },
                        { value: '2', label: '2 horas antes' },
                        { value: '3', label: '3 horas antes' },
                        { value: '6', label: '6 horas antes' },
                        { value: '12', label: '12 horas antes' },
                        { value: '24', label: '24 horas antes' },
                      ]}
                      required
                    />

                    <ModernSelect
                      label="Template de Lembrete"
                      value={formData.reminderTemplateId}
                      onValueChange={(value) => setFormData({ ...formData, reminderTemplateId: value })}
                      icon={Bell}
                      options={reminderTemplates.map(template => ({ 
                        value: template.id, 
                        label: template.name 
                      }))}
                      placeholder="Selecione um template"
                      required
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Reminder Template Preview */}
            {formData.reminderEnabled && formData.reminderTemplateId && (
              <ReminderTemplatePreview
                templateId={formData.reminderTemplateId}
                slotId={selectedSlot?.id}
                className="border-t pt-6"
              />
            )}

            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <h4 className="font-medium text-amber-900 mb-2">📊 Estatísticas</h4>
              <div className="text-sm text-amber-700">
                <p>Total de agendamentos: {selectedSlot._count?.bookings || 0}</p>
                <p>Agenda criada em: {formatDate(selectedSlot.createdAt)}</p>
              </div>
            </div>

            <div className="flex justify-end">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigateToList()}
                className="flex items-center"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar para Lista
              </Button>
            </div>
          </form>
        </FormCard>
      </AdminPageLayout>
    );
  }

  // Default list view
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="w-full max-w-[1600px] mx-auto">
        {/* Header Section */}
        <div className="bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-2xl p-8 text-white shadow-xl mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button onClick={() => router.push('/')} variant="outline" size="sm" className="bg-white/20 text-white border-white/30 hover:bg-white/30">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar
              </Button>
              <div>
                <h1 className="text-3xl font-bold mb-2 flex items-center">
                  <CalendarDays className="w-8 h-8 mr-3" />
                  Agenda de Mentorias
                </h1>
                <p className="text-white/90">
                  Gerencie a disponibilidade dos professores para mentorias
                </p>
              </div>
            </div>
            <div className="hidden md:block">
              <Button 
                onClick={() => navigateToMode('new')}
                variant="secondary"
                size="lg"
              >
                <Plus className="w-5 h-5 mr-2" />
                Nova Agenda
              </Button>
            </div>
          </div>
          {/* Mobile button */}
          <div className="md:hidden mt-4">
            <Button 
              onClick={() => navigateToMode('new')}
              variant="secondary"
              className="w-full"
            >
              <Plus className="w-5 h-5 mr-2" />
              Nova Agenda
            </Button>
          </div>
        </div>

        {/* Search Section */}
        <Card className="mb-6 shadow-lg border-0">
          <CardContent className="pt-6">
            <div className="flex gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Buscar por professor ou dia da semana..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                  className="border-2 border-gray-200 focus:border-[#667eea] focus:ring-4 focus:ring-[#667eea]/20 transition-all duration-300"
                />
              </div>
              <Button 
                onClick={handleSearch}
                variant="default"
              >
                <Search className="w-4 h-4 mr-2" />
                Buscar
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Slots Table */}
        <Card className="shadow-xl border-0">
          <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg">
            <CardTitle className="text-2xl font-bold text-gray-900 flex items-center">
              <CalendarDays className="w-6 h-6 mr-2 text-[#667eea]" />
              Agendas Configuradas
            </CardTitle>
            <CardDescription className="text-lg">
              {slots.length} agenda{slots.length !== 1 ? 's' : ''} encontrada{slots.length !== 1 ? 's' : ''}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading && mode === 'list' ? (
              <div className="text-center py-12">
                <Loader2 className="mx-auto animate-spin" size={32} />
                <p className="mt-4 text-muted-foreground">Carregando agendas...</p>
              </div>
            ) : slots.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-20 h-20 bg-gradient-to-br from-[#667eea] to-[#764ba2] rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-xl">
                  <CalendarDays className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  Nenhuma agenda encontrada
                </h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  {searchTerm ? 'Tente uma busca diferente ou ajuste os filtros.' : 'Comece configurando uma agenda de mentoria para um professor.'}
                </p>
                {!searchTerm && (
                  <Button 
                    onClick={() => navigateToMode('new')} 
                    className="bg-gradient-to-r from-[#667eea] to-[#764ba2] hover:from-[#5a6fd8] hover:to-[#6b4190] text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300"
                    size="lg"
                  >
                    <Plus className="w-5 h-5 mr-2" />
                    Criar Primeira Agenda
                  </Button>
                )}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Professor</TableHead>
                      <TableHead>Dia da Semana</TableHead>
                      <TableHead>Horário</TableHead>
                      <TableHead>Duração</TableHead>
                      <TableHead>Max/Semana</TableHead>
                      <TableHead>Agendamentos</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {slots.map((slot) => (
                      <TableRow key={slot.id}>
                        <TableCell>
                          <div className="flex items-center">
                            <User className="w-4 h-4 mr-2 text-[#667eea]" />
                            {slot.teacher?.name}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {getDayOfWeekLabel(slot.dayOfWeek)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Clock className="w-4 h-4 mr-1" />
                            {formatTime(slot.startTime)} - {formatTime(slot.endTime)}
                          </div>
                        </TableCell>
                        <TableCell>{slot.duration} min</TableCell>
                        <TableCell>{slot.maxBookingsPerWeek}</TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div>{slot._count?.bookings || 0}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={slot.isActive ? 'success' : 'secondary'}>
                            {slot.isActive ? 'Ativa' : 'Inativa'}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              onClick={() => navigateToMode('edit', slot.id)}
                              variant="outline"
                              size="sm"
                              className="border-[#667eea] text-[#667eea] hover:bg-[#667eea] hover:text-white transition-all duration-300"
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              onClick={() => handleDelete(slot.id, slot.teacher?.name || '', slot.dayOfWeek)}
                              variant="outline"
                              size="sm"
                              className="border-red-500 text-red-500 hover:bg-red-500 hover:text-white transition-all duration-300"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      <ConfirmationDialog />
    </div>
  );
}

export default function MentoringAgendaPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="mx-auto animate-spin" size={48} />
          <p className="mt-4 text-muted-foreground">Carregando...</p>
        </div>
      </div>
    }>
      <MentoringAgendaPageContent />
    </Suspense>
  );
}