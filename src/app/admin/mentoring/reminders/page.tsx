'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState, useCallback } from 'react';
import { toast } from 'sonner';
import { isAdminOrSuperAdmin } from '@/lib/roles';

// Components
import {
  AdminPageLayout,
  Button,
  Badge
} from '@/components/ui/modern';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useConfirmationDialog } from '@/components/ui/confirmation-dialog';

// Icons
import { 
  Loader2, Bell, ArrowLeft, RefreshCw, Trash2, Clock, 
  User, MessageSquare, Calendar, AlertCircle, CheckCircle, 
  XCircle, PlayCircle, TrendingUp
} from 'lucide-react';

interface MentoringReminderJob {
  id: string;
  bookingId: string;
  studentId: string;
  teacherId: string;
  slotId: string;
  templateId: string;
  scheduledFor: Date;
  scheduledDateTime: Date;
  status: 'pending' | 'sent' | 'failed' | 'cancelled';
  attempts: number;
  lastAttempt?: Date;
  errorMessage?: string;
  messageText?: string;
}

interface ReminderStatistics {
  totalReminders: number;
  sentReminders: number;
  failedReminders: number;
  pendingReminders: number;
  successRate: string;
}

export default function MentoringRemindersPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  
  // State
  const [reminders, setReminders] = useState<MentoringReminderJob[]>([]);
  const [statistics, setStatistics] = useState<ReminderStatistics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { showConfirmation, ConfirmationDialog } = useConfirmationDialog();

  // Load data
  const loadReminders = useCallback(async () => {
    try {
      setIsLoading(true);
      const [remindersRes, statsRes] = await Promise.all([
        fetch('/api/mentoring/reminders'),
        fetch('/api/mentoring/reminders?action=statistics')
      ]);

      if (remindersRes.ok) {
        const remindersData = await remindersRes.json();
        setReminders(remindersData.data || []);
      } else {
        throw new Error('Failed to load reminders');
      }

      if (statsRes.ok) {
        const statsData = await statsRes.json();
        setStatistics(statsData.data || null);
      }

    } catch (err) {
      console.error('Error loading reminder data:', err);
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
      toast.error('Erro ao carregar dados dos lembretes');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Effects
  useEffect(() => {
    if (!loading && !isAdminOrSuperAdmin(user)) {
      router.push('/');
      return;
    }

    if (isAdminOrSuperAdmin(user)) {
      loadReminders();
    }
  }, [user, loading, router, loadReminders]);

  // Actions
  const handleProcessPendingReminders = async () => {
    try {
      setIsProcessing(true);
      const response = await fetch('/api/mentoring/reminders/process', {
        method: 'POST'
      });

      if (!response.ok) {
        throw new Error('Failed to process reminders');
      }

      const result = await response.json();
      toast.success(result.data.message || 'Lembretes processados com sucesso');
      
      // Reload data
      await loadReminders();

    } catch (err) {
      console.error('Error processing reminders:', err);
      toast.error('Erro ao processar lembretes');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleScheduleBulkReminders = async () => {
    const confirmed = await showConfirmation({
      title: 'Agendar Lembretes em Massa',
      description: 'Deseja agendar lembretes para todas as mentorias das próximas 24 horas?',
      confirmText: 'Agendar',
      cancelText: 'Cancelar'
    });

    if (!confirmed) return;

    try {
      const response = await fetch('/api/mentoring/reminders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'bulk-schedule',
          hoursAhead: 24
        })
      });

      if (!response.ok) {
        throw new Error('Failed to schedule bulk reminders');
      }

      const result = await response.json();
      toast.success(`${result.data.scheduled} lembretes agendados com sucesso`);
      
      // Reload data
      await loadReminders();

    } catch (err) {
      console.error('Error scheduling bulk reminders:', err);
      toast.error('Erro ao agendar lembretes em massa');
    }
  };

  const handleCancelReminder = async (reminderId: string) => {
    const confirmed = await showConfirmation({
      title: 'Cancelar Lembrete',
      description: 'Tem certeza que deseja cancelar este lembrete?',
      confirmText: 'Cancelar Lembrete',
      cancelText: 'Manter'
    });

    if (!confirmed) return;

    try {
      const response = await fetch(`/api/mentoring/reminders?id=${reminderId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error('Failed to cancel reminder');
      }

      toast.success('Lembrete cancelado com sucesso');
      
      // Reload data
      await loadReminders();

    } catch (err) {
      console.error('Error cancelling reminder:', err);
      toast.error('Erro ao cancelar lembrete');
    }
  };

  // Utility functions
  const formatDate = (dateString: string | Date) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-orange-500" />;
      case 'sent':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'cancelled':
        return <AlertCircle className="w-4 h-4 text-gray-500" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const getStatusVariant = (status: string): "secondary" | "destructive" | "default" => {
    switch (status) {
      case 'sent':
        return 'default';
      case 'failed':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  // Loading state
  if (loading || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="mx-auto animate-spin" size={48} />
          <p className="mt-4 text-muted-foreground">Carregando lembretes...</p>
        </div>
      </div>
    );
  }

  // Permission check
  if (!isAdminOrSuperAdmin(user)) {
    return null;
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-destructive">Erro</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{error}</p>
            <Button onClick={() => router.push('/admin/mentoring')} className="mt-4">
              Voltar
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <AdminPageLayout
      title="Gerenciar Lembretes de Mentoria"
      description="Monitore e gerencie lembretes automáticos de mentoria"
      icon={Bell}
      backUrl="/admin/mentoring/agenda"
    >
      <div className="space-y-6">
        {/* Statistics Cards */}
        {statistics && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <MessageSquare className="w-5 h-5 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total</p>
                    <p className="text-2xl font-bold text-gray-900">{statistics.totalReminders}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <div>
                    <p className="text-sm font-medium text-gray-600">Enviados</p>
                    <p className="text-2xl font-bold text-green-600">{statistics.sentReminders}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Clock className="w-5 h-5 text-orange-600" />
                  <div>
                    <p className="text-sm font-medium text-gray-600">Pendentes</p>
                    <p className="text-2xl font-bold text-orange-600">{statistics.pendingReminders}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="w-5 h-5 text-purple-600" />
                  <div>
                    <p className="text-sm font-medium text-gray-600">Taxa de Sucesso</p>
                    <p className="text-2xl font-bold text-purple-600">{statistics.successRate}%</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-between items-center">
          <div className="flex space-x-3">
            <Button
              onClick={handleProcessPendingReminders}
              disabled={isProcessing}
              className="flex items-center"
            >
              {isProcessing ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <PlayCircle className="w-4 h-4 mr-2" />
              )}
              Processar Pendentes
            </Button>

            <Button
              variant="outline"
              onClick={handleScheduleBulkReminders}
              className="flex items-center"
            >
              <Calendar className="w-4 h-4 mr-2" />
              Agendar em Massa
            </Button>
          </div>

          <Button
            variant="outline"
            onClick={loadReminders}
            className="flex items-center"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Atualizar
          </Button>
        </div>

        {/* Reminders Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Bell className="w-5 h-5 mr-2" />
              Lembretes Agendados
            </CardTitle>
            <CardDescription>
              Lista de lembretes de mentoria agendados e seu status
            </CardDescription>
          </CardHeader>
          <CardContent>
            {reminders.length === 0 ? (
              <div className="text-center py-8">
                <Bell className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-600">Nenhum lembrete encontrado</p>
                <p className="text-sm text-gray-500 mt-2">
                  Use o botão "Agendar em Massa" para criar novos lembretes
                </p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Status</TableHead>
                    <TableHead>Agendado Para</TableHead>
                    <TableHead>Mentoria</TableHead>
                    <TableHead>Tentativas</TableHead>
                    <TableHead>Última Tentativa</TableHead>
                    <TableHead>Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {reminders.map((reminder) => (
                    <TableRow key={reminder.id}>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(reminder.status)}
                          <Badge variant={getStatusVariant(reminder.status)}>
                            {reminder.status === 'pending' && 'Pendente'}
                            {reminder.status === 'sent' && 'Enviado'}
                            {reminder.status === 'failed' && 'Falhou'}
                            {reminder.status === 'cancelled' && 'Cancelado'}
                          </Badge>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="text-sm">
                          {formatDate(reminder.scheduledFor)}
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="text-sm">
                          <div className="font-medium">
                            {formatDate(reminder.scheduledDateTime)}
                          </div>
                          <div className="text-gray-500 text-xs">
                            Booking: {reminder.bookingId.slice(0, 8)}...
                          </div>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="text-sm">
                          {reminder.attempts}/3
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="text-sm text-gray-500">
                          {reminder.lastAttempt
                            ? formatDate(reminder.lastAttempt)
                            : '-'
                          }
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex space-x-1">
                          {reminder.status === 'pending' && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleCancelReminder(reminder.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          )}
                          
                          {reminder.errorMessage && (
                            <div 
                              className="text-xs text-red-600 cursor-help"
                              title={reminder.errorMessage}
                            >
                              <AlertCircle className="w-3 h-3" />
                            </div>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>

      <ConfirmationDialog />
    </AdminPageLayout>
  );
}