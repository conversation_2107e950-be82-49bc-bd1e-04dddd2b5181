'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState, useCallback, Suspense } from 'react';
import { toast } from 'sonner';
import { coursesService } from '@/lib/services/courses.service';
import { classesService } from '@/lib/services/classes.service';
import { useAutoSave } from '@/hooks/useAutoSave';
import { AutoSaveIndicator } from '@/components/ui/auto-save-indicator';

// Components
import {
  AdminPageLayout,
  FormCard,
  ModernInput,
  ModernTextarea,
  Button,
  Badge
} from '@/components/ui/modern';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useConfirmationDialog } from '@/components/ui/confirmation-dialog';
import { Switch } from '@/components/ui/switch';

// Icons
import { 
  Loader2, BookOpen, ArrowLeft, Plus, Search, Edit, Trash2, Users, 
  FileText, DollarSign, Clock, BookPlus, Calendar, BookMarked, MessageCircle
} from 'lucide-react';

interface Course {
  id: string;
  name: string;
  description?: string;
  duration?: number;
  numberOfLessons?: number;
  price?: number;
  allowsMakeup: boolean;
  isActive: boolean;
  createdAt: string;
  _count?: {
    classes: number;
    enrollments: number;
  };
}

interface CourseFormData {
  name: string;
  description: string;
  duration: string;
  numberOfLessons: string;
  lessonDuration: string;
  price: string;
  allowsMakeup: boolean;
  mentoringSessionsPerEnrollment: string;
  isActive: boolean;
}

function CoursesPageContent() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Get mode and ID from URL parameters
  const mode = searchParams.get('mode') || 'list'; // list, new, edit
  const courseId = searchParams.get('id');
  
  // State
  const [courses, setCourses] = useState<Course[]>([]);
  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState<string | null>(null);
  const { showConfirmation, ConfirmationDialog } = useConfirmationDialog();
  
  // Classes state
  const [classes, setClasses] = useState<any[]>([]);
  const [isLoadingClasses, setIsLoadingClasses] = useState(true);
  
  // Loading state for bulk update
  const [isUpdatingLessons, setIsUpdatingLessons] = useState(false);
  
  const [formData, setFormData] = useState<CourseFormData>({
    name: '',
    description: '',
    duration: '',
    numberOfLessons: '',
    lessonDuration: '180',
    price: '',
    allowsMakeup: false,
    mentoringSessionsPerEnrollment: '0',
    isActive: true
  });

  // Navigation helpers
  const navigateToMode = useCallback((newMode: string, id?: string) => {
    const params = new URLSearchParams();
    params.set('mode', newMode);
    if (id) params.set('id', id);
    router.push(`/admin/courses?${params.toString()}`);
  }, [router]);

  const navigateToList = useCallback(() => {
    router.push('/admin/courses');
  }, [router]);

  // Data fetching
  const fetchCourses = useCallback(async (search?: string) => {
    try {
      // Only show loading for table content, not full page
      if (mode === 'list') {
        setIsLoading(true);
      }
      setError(null);
      const url = search
        ? `/api/courses?search=${encodeURIComponent(search)}`
        : '/api/courses';

      const token = localStorage.getItem('auth_token');
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Falha ao carregar cursos');
      }

      const result = await response.json();
      setCourses(result.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      // Only hide loading for table content
      if (mode === 'list') {
        setIsLoading(false);
      }
    }
  }, [mode]);

  const fetchClasses = useCallback(async () => {
    try {
      setIsLoadingClasses(true);
      const result = await classesService.getClasses();
      
      if (result.success && result.data) {
        setClasses(result.data);
      }
    } catch (err) {
      console.error('Error fetching classes:', err);
    } finally {
      setIsLoadingClasses(false);
    }
  }, []);

  const fetchCourse = useCallback(async (id: string) => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/courses/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Falha ao carregar curso');
      }

      const result = await response.json();
      const courseData = result.data;
      setSelectedCourse(courseData);

      // Populate form for editing
      setFormData({
        name: courseData.name ?? '',
        description: courseData.description ?? '',
        duration: courseData.duration ? courseData.duration.toString() : '',
        numberOfLessons: courseData.numberOfLessons ? courseData.numberOfLessons.toString() : '',
        lessonDuration: courseData.lessonDuration ? courseData.lessonDuration.toString() : '180',
        price: courseData.price ? courseData.price.toString() : '',
        allowsMakeup: courseData.allowsMakeup ?? false,
        mentoringSessionsPerEnrollment: courseData.mentoringSessionsPerEnrollment ? courseData.mentoringSessionsPerEnrollment.toString() : '0',
        isActive: courseData.isActive !== false
      });
      
      // Mark form as saved after loading data to avoid showing "dirty" state
      setTimeout(() => {
        autoSaveState.markAsSaved();
      }, 100);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Auto-save functionality for edit mode
  const autoSaveCourse = useCallback(async (data: CourseFormData) => {
    if (mode !== 'edit' || !courseId || !data.name.trim()) {
      return false;
    }

    try {
      const token = localStorage.getItem('auth_token');
      if (!token) return false;

      const payload = {
        name: data.name.trim(),
        description: data.description.trim() || null,
        duration: data.duration ? parseInt(data.duration) : null,
        numberOfLessons: data.numberOfLessons ? parseInt(data.numberOfLessons) : null,
        lessonDuration: data.lessonDuration ? parseInt(data.lessonDuration) : 180,
        price: data.price ? parseFloat(data.price) : null,
        allowsMakeup: data.allowsMakeup,
        mentoringSessionsPerEnrollment: data.mentoringSessionsPerEnrollment ? parseInt(data.mentoringSessionsPerEnrollment) : 0,
        isActive: data.isActive
      };

      const response = await fetch(`/api/courses/${courseId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(payload),
      });

      return response.ok;
    } catch (err) {
      console.error('Auto-save error:', err);
      return false;
    }
  }, [mode, courseId]);

  const autoSaveState = useAutoSave({
    data: formData,
    onSave: autoSaveCourse,
    config: {
      debounceMs: 2000,
      enableToast: false
    },
    isValid: (data) => !!data.name.trim(),
    enabled: mode === 'edit'
  });

  // Effects
  useEffect(() => {
    if (!loading && (!user || !['teacher', 'super_admin'].includes(user.profile?.role || ''))) {
      router.push('/');
      return;
    }

    if (user && ['teacher', 'super_admin'].includes(user.profile?.role || '')) {
      if (mode === 'edit' && courseId) {
        fetchCourse(courseId);
        fetchClasses(); // Add classes fetching to edit mode
      } else if (mode === 'list') {
        fetchCourses('');
        fetchClasses();
      } else if (mode === 'new') {
        setIsLoading(false);
        // Reset form for new course
        setFormData({
          name: '',
          description: '',
          duration: '',
          numberOfLessons: '',
          lessonDuration: '180',
          price: '',
          allowsMakeup: false,
          isActive: true
        });
      }
    }
  }, [user, loading, router, mode, courseId, fetchCourse, fetchCourses, fetchClasses]);

  // Form handlers
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('Nome é obrigatório');
      return;
    }

    try {
      setIsSubmitting(true);
      const token = localStorage.getItem('auth_token');

      if (!token) {
        toast.error('Sessão expirada. Faça login novamente.');
        router.push('/login');
        return;
      }

      const payload = {
        name: formData.name.trim(),
        description: formData.description.trim() || null,
        duration: formData.duration ? parseInt(formData.duration) : null,
        numberOfLessons: formData.numberOfLessons ? parseInt(formData.numberOfLessons) : null,
        lessonDuration: formData.lessonDuration ? parseInt(formData.lessonDuration) : 180,
        price: formData.price ? parseFloat(formData.price) : null,
        allowsMakeup: formData.allowsMakeup,
        mentoringSessionsPerEnrollment: formData.mentoringSessionsPerEnrollment ? parseInt(formData.mentoringSessionsPerEnrollment) : 0,
        isActive: formData.isActive
      };

      if (mode === 'new') {
        const response = await coursesService.createCourse({
          name: payload.name,
          description: payload.description || undefined,
          duration: payload.duration || undefined,
          numberOfLessons: payload.numberOfLessons || undefined,
          lessonDuration: payload.lessonDuration || undefined,
          price: payload.price || undefined,
          allowsMakeup: payload.allowsMakeup,
          mentoringSessionsPerEnrollment: payload.mentoringSessionsPerEnrollment
        });

        if (!response.success) {
          throw new Error(response.error || 'Falha ao criar curso');
        }

        toast.success('Curso criado com sucesso!');
      } else if (mode === 'edit' && courseId) {
        const response = await fetch(`/api/courses/${courseId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify(payload),
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || 'Falha ao atualizar curso');
        }

        toast.success('Curso atualizado com sucesso!');
      }

      navigateToList();
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Erro ao salvar curso');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSearch = useCallback(() => {
    fetchCourses(searchTerm);
  }, [fetchCourses, searchTerm]);

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (mode === 'list') {
        fetchCourses(searchTerm);
      }
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [searchTerm, mode, fetchCourses]);

  const handleDelete = (courseId: string, courseName: string) => {
    showConfirmation({
      title: 'Excluir Curso',
      description: `Tem certeza que deseja excluir o curso "${courseName}"? Esta ação não pode ser desfeita.`,
      confirmText: 'Excluir',
      cancelText: 'Cancelar',
      variant: 'destructive',
      icon: 'delete',
      onConfirm: async () => {
        try {
          const token = localStorage.getItem('auth_token');

          if (!token) {
            toast.error('Sessão expirada. Faça login novamente.');
            router.push('/login');
            return;
          }

          const response = await fetch(`/api/courses/${courseId}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });

          if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || 'Falha ao excluir curso');
          }

          toast.success('Curso excluído com sucesso!');
          fetchCourses(searchTerm);
        } catch (err) {
          toast.error(err instanceof Error ? err.message : 'Erro ao excluir curso');
        }
      }
    });
  };

  // Handle updating all lesson durations with confirm dialog
  const handleUpdateAllLessonDurations = () => {
    if (!selectedCourse?.id || !formData.lessonDuration) {
      toast.error('Duração da aula é obrigatória');
      return;
    }

    const confirmed = confirm(
      `Tem certeza que deseja atualizar a duração de todas as aulas deste curso para ${formData.lessonDuration} minutos?\n\n⚠️ Esta ação não pode ser desfeita.`
    );

    if (confirmed) {
      executeUpdateLessonDurations();
    }
  };

  // Execute the lesson duration update
  const executeUpdateLessonDurations = async () => {
    if (!selectedCourse?.id || !formData.lessonDuration) return;

    try {
      setIsUpdatingLessons(true);
      const token = localStorage.getItem('auth_token');

      if (!token) {
        toast.error('Sessão expirada. Faça login novamente.');
        router.push('/login');
        return;
      }

      const response = await fetch(`/api/courses/${selectedCourse.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          action: 'update_lesson_durations',
          lessonDuration: parseInt(formData.lessonDuration)
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Falha ao atualizar durações das aulas');
      }

      const result = await response.json();
      toast.success(result.message || 'Durações das aulas atualizadas com sucesso!');
      
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Erro ao atualizar durações das aulas');
    } finally {
      setIsUpdatingLessons(false);
    }
  };

  // Utility functions
  const formatCurrency = (value?: number) => {
    if (!value) return '-';
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  // Loading state - only show full page loading for non-list modes
  if (loading || (isLoading && mode !== 'list')) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="mx-auto animate-spin" size={48} />
          <p className="mt-4 text-muted-foreground">
            {mode === 'edit' ? 'Carregando curso...' : 'Carregando cursos...'}
          </p>
        </div>
      </div>
    );
  }

  // Permission check
  if (!user || !['teacher', 'super_admin'].includes(user.profile?.role || '')) {
    return null;
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-destructive">Erro</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{error}</p>
            <Button onClick={() => navigateToList()} className="mt-4">
              Voltar para Lista
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Render based on mode
  if (mode === 'new') {
    return (
      <AdminPageLayout
        title="Novo Curso"
        description="Criar um novo curso para oferecer aos alunos"
        icon={BookPlus}
        backUrl="/admin/courses"
      >
        <FormCard title="Cadastrar Novo Curso" icon={BookOpen}>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 xl:grid-cols-5">
              <ModernInput
                label="Nome do Curso"
                value={formData.name}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Ex: Academy, Master, Intensivox..."
                icon={BookOpen}
                required
                className="md:col-span-2 xl:col-span-1"
              />

              <ModernInput
                label="Duração Total (horas)"
                type="number"
                min="1"
                value={formData.duration}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, duration: e.target.value })}
                placeholder="Ex: 40"
                icon={Clock}
              />

              <ModernInput
                label="Número de Aulas"
                type="number"
                min="1"
                value={formData.numberOfLessons}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, numberOfLessons: e.target.value })}
                placeholder="Ex: 12"
                icon={Calendar}
              />

              <ModernInput
                label="Duração da Aula (min)"
                type="number"
                min="30"
                step="15"
                value={formData.lessonDuration}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, lessonDuration: e.target.value })}
                placeholder="180 (3h)"
                icon={Clock}
              />

              <ModernInput
                label="Preço (R$)"
                type="number"
                min="0"
                step="0.01"
                value={formData.price}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, price: e.target.value })}
                placeholder="Ex: 299.90"
                icon={DollarSign}
              />
            </div>

            <ModernTextarea
              label="Descrição"
              value={formData.description}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Descrição detalhada do curso..."
              rows={3}
              icon={FileText}
            />

            <div className="grid gap-6 md:grid-cols-2">
              <ModernInput
                label="Sessões de Mentoria por Aluno"
                type="number"
                min="0"
                value={formData.mentoringSessionsPerEnrollment}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, mentoringSessionsPerEnrollment: e.target.value })}
                placeholder="Ex: 4"
                icon={MessageCircle}
                className="w-[70px]"
              />
              
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="allowsMakeup"
                    checked={formData.allowsMakeup}
                    onCheckedChange={(checked) => setFormData({ ...formData, allowsMakeup: checked })}
                  />
                  <label htmlFor="allowsMakeup" className="text-sm font-medium">
                    Permite reposição de aulas
                  </label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
                  />
                  <label htmlFor="isActive" className="text-sm font-medium">
                    Curso ativo
                  </label>
                </div>
              </div>
            </div>

            {/* Form Actions for New Mode */}
            <div className="flex gap-4 pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigateToList()}
                disabled={isSubmitting}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Salvando...' : 'Salvar Curso'}
              </Button>
            </div>

          </form>
        </FormCard>
      </AdminPageLayout>
    );
  }

  if (mode === 'edit' && selectedCourse) {
    return (
      <AdminPageLayout
        title="Editar Curso"
        description={`Edite as informações do curso ${selectedCourse.name}`}
        icon={BookOpen}
        backUrl="/admin/courses"
      >
        <FormCard 
          title="Informações do Curso" 
          icon={BookOpen}
          headerAction={
            <AutoSaveIndicator
              isAutoSaving={autoSaveState.isAutoSaving}
              lastSaved={autoSaveState.lastSaved}
              hasUnsavedChanges={autoSaveState.hasUnsavedChanges}
              error={autoSaveState.error}
            />
          }
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 xl:grid-cols-5">
              <ModernInput
                label="Nome do Curso"
                value={formData.name}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Ex: Academy, Master, Intensivox..."
                icon={BookOpen}
                required
                className="md:col-span-2 xl:col-span-1"
              />

              <ModernInput
                label="Duração Total (horas)"
                type="number"
                min="1"
                value={formData.duration}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, duration: e.target.value })}
                placeholder="Ex: 40"
                icon={Clock}
              />

              <ModernInput
                label="Número de Aulas"
                type="number"
                min="1"
                value={formData.numberOfLessons}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, numberOfLessons: e.target.value })}
                placeholder="Ex: 12"
                icon={Calendar}
              />

              <ModernInput
                label="Duração da Aula (min)"
                type="number"
                min="30"
                step="15"
                value={formData.lessonDuration}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, lessonDuration: e.target.value })}
                placeholder="180 (3h)"
                icon={Clock}
              />

              <ModernInput
                label="Preço (R$)"
                type="number"
                min="0"
                step="0.01"
                value={formData.price}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, price: e.target.value })}
                placeholder="Ex: 299.90"
                icon={DollarSign}
              />
            </div>

            <ModernTextarea
              label="Descrição"
              value={formData.description}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Descrição detalhada do curso..."
              rows={3}
              icon={FileText}
            />

            <div className="grid gap-6 grid-cols-8">
              <div className="col-span-2">
                <ModernInput
                  label="Sessões de Mentoria por Aluno"
                  type="number"
                  min="0"
                  value={formData.mentoringSessionsPerEnrollment}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, mentoringSessionsPerEnrollment: e.target.value })}
                  placeholder="Ex: 4"
                  icon={MessageCircle}
                  className="w-[70px]"
                />
              </div>
              
              <div className="col-span-2 space-y-4">
                <div className="flex items-center space-x-3">
                  <Switch
                    id="allowsMakeup"
                    checked={formData.allowsMakeup}
                    onCheckedChange={(checked) => setFormData({ ...formData, allowsMakeup: checked })}
                  />
                  <label htmlFor="allowsMakeup" className="text-sm font-medium">
                    Permite reposição de aulas
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
                  />
                  <label htmlFor="isActive" className="text-sm font-medium">
                    Curso ativo
                  </label>
                </div>
              </div>

              {/* Bulk Update Section */}
              <div className="col-span-4 bg-amber-50 border border-amber-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <Clock className="w-5 h-5 text-amber-600 mt-0.5" />
                  <div className="flex-1">
                    <h4 className="font-medium text-amber-900 mb-2">
                      Atualizar Duração de Todas as Aulas
                    </h4>
                    <p className="text-sm text-amber-700 mb-3">
                      Esta ação irá atualizar a duração de todas as aulas existentes deste curso para corresponder à duração configurada acima ({formData.lessonDuration} min).
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleUpdateAllLessonDurations}
                      disabled={isSubmitting || isUpdatingLessons || !formData.lessonDuration}
                      className="border-amber-300 text-amber-700 hover:bg-amber-100"
                    >
                      {isUpdatingLessons ? (
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      ) : (
                        <Clock className="w-4 h-4 mr-2" />
                      )}
                      {isUpdatingLessons ? 'Atualizando...' : 'Atualizar Todas as Aulas'}
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            

          </form>
        </FormCard>

        {/* Turmas por Curso Panel */}
        <Card className="shadow-xl border-0 mt-8">
          <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <CardTitle className="text-2xl font-bold text-gray-900 flex items-center">
                  <Users className="w-6 h-6 mr-2 text-[#667eea]" />
                  Turmas do Curso
                </CardTitle>
                <CardDescription className="text-lg">
                  Turmas associadas ao curso {selectedCourse.name}
                </CardDescription>
              </div>
              <div className="flex-shrink-0">
                <Button
                  onClick={() => router.push(`/admin/classes?mode=new&courseId=${selectedCourse.id}`)}
                  variant="default"
                  size="sm"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Adicionar Turma
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-8">
            {isLoadingClasses ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <Loader2 className="mx-auto animate-spin mb-4" size={40} />
                  <p className="text-muted-foreground">Carregando turmas...</p>
                </div>
              </div>
            ) : (
              (() => {
                const courseClasses = classes.filter(cls => cls.course.id === selectedCourse.id);
                
                if (courseClasses.length === 0) {
                  return (
                    <div className="text-center py-12">
                      <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <Users className="w-10 h-10 text-gray-400" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        Nenhuma turma encontrada
                      </h3>
                      <p className="text-gray-600 mb-6">
                        Este curso ainda não possui turmas criadas.
                      </p>
                    </div>
                  );
                }

                return (
                  <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {courseClasses.map((classItem) => {
                      const occupancyRate = classItem.capacity > 0 
                        ? (classItem.enrollmentsCount / classItem.capacity) * 100 
                        : 0;
                      
                      return (
                        <Card key={classItem.id} className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-[#667eea]">
                          <CardHeader className="pb-3">
                            <div className="flex items-center justify-between">
                              <CardTitle className="text-lg font-semibold text-gray-900">
                                {classItem.name}
                              </CardTitle>
                              <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                                classItem.isActive 
                                  ? 'bg-green-100 text-green-800' 
                                  : 'bg-red-100 text-red-800'
                              }`}>
                                {classItem.isActive ? 'Ativa' : 'Inativa'}
                              </div>
                            </div>
                          </CardHeader>
                          <CardContent className="space-y-3">
                            <div className="flex items-center text-sm text-gray-600">
                              <Calendar className="w-4 h-4 mr-2" />
                              <span>
                                {new Date(classItem.startDate).toLocaleDateString('pt-BR')} - {' '}
                                {new Date(classItem.endDate).toLocaleDateString('pt-BR')}
                              </span>
                            </div>
                            <div className="flex items-center text-sm text-gray-600">
                              <Users className="w-4 h-4 mr-2" />
                              <span>{classItem.enrollmentsCount} / {classItem.capacity} alunos</span>
                              <span className="ml-2 text-xs text-gray-500">
                                ({occupancyRate.toFixed(0)}% ocupação)
                              </span>
                            </div>
                            <div className="flex items-center text-sm text-gray-600">
                              <BookOpen className="w-4 h-4 mr-2" />
                              <span>{classItem.lessonsCount} aulas</span>
                            </div>
                            <div className="pt-2">
                              <Button 
                                onClick={() => router.push(`/admin/classes?mode=edit&id=${classItem.id}`)}
                                className="w-full bg-gradient-to-r from-[#667eea] to-[#764ba2] hover:from-[#5a6fd8] hover:to-[#6b4190] text-white"
                                size="sm"
                              >
                                Ver Detalhes
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                );
              })()
            )}
          </CardContent>
        </Card>
      </AdminPageLayout>
    );
  }

  // Default list view
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="w-full max-w-[1600px] mx-auto">
        {/* Header Section */}
        <div className="bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-2xl p-8 text-white shadow-xl mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button onClick={() => router.push('/')} variant="outline" size="sm" className="bg-white/20 text-white border-white/30 hover:bg-white/30">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar
              </Button>
              <div>
                <h1 className="text-3xl font-bold mb-2 flex items-center">
                  <BookOpen className="w-8 h-8 mr-3" />
                  Gerenciar Cursos
                </h1>
                <p className="text-white/90">
                  Administre cursos, preços e configurações
                </p>
              </div>
            </div>
            <div className="hidden md:block">
              <Button 
                onClick={() => navigateToMode('new')}
                variant="secondary"
                size="lg"
              >
                <Plus className="w-5 h-5 mr-2" />
                Novo Curso
              </Button>
            </div>
          </div>
          {/* Mobile button */}
          <div className="md:hidden mt-4">
            <Button 
              onClick={() => navigateToMode('new')}
              variant="secondary"
              className="w-full"
            >
              <Plus className="w-5 h-5 mr-2" />
              Novo Curso
            </Button>
          </div>
        </div>

        {/* Search Section */}
        <Card className="mb-6 shadow-lg border-0">
          <CardContent className="pt-6">
            <div className="flex gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Buscar cursos por nome ou descrição..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                  className="border-2 border-gray-200 focus:border-[#667eea] focus:ring-4 focus:ring-[#667eea]/20 transition-all duration-300"
                />
              </div>
              <Button 
                onClick={handleSearch}
                variant="default"
              >
                <Search className="w-4 h-4 mr-2" />
                Buscar
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Courses Table */}
        <Card className="shadow-xl border-0">
          <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg">
            <CardTitle className="text-2xl font-bold text-gray-900 flex items-center">
              <BookOpen className="w-6 h-6 mr-2 text-[#667eea]" />
              Cursos Cadastrados
            </CardTitle>
            <CardDescription className="text-lg">
              {courses.length} curso{courses.length !== 1 ? 's' : ''} encontrado{courses.length !== 1 ? 's' : ''}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading && mode === 'list' ? (
              <div className="text-center py-12">
                <Loader2 className="mx-auto animate-spin" size={32} />
                <p className="mt-4 text-muted-foreground">Carregando cursos...</p>
              </div>
            ) : courses.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-20 h-20 bg-gradient-to-br from-[#667eea] to-[#764ba2] rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-xl">
                  <BookOpen className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  Nenhum curso encontrado
                </h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  {searchTerm ? 'Tente uma busca diferente ou ajuste os filtros.' : 'Comece criando um novo curso para oferecer aos seus alunos.'}
                </p>
                {!searchTerm && (
                  <Button 
                    onClick={() => navigateToMode('new')} 
                    className="bg-gradient-to-r from-[#667eea] to-[#764ba2] hover:from-[#5a6fd8] hover:to-[#6b4190] text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300"
                    size="lg"
                  >
                    <Plus className="w-5 h-5 mr-2" />
                    Criar Primeiro Curso
                  </Button>
                )}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nome</TableHead>
                      <TableHead>Duração</TableHead>
                      <TableHead>Aulas</TableHead>
                      <TableHead>Preço</TableHead>
                      <TableHead>Reposição</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Turmas</TableHead>
                      <TableHead>Matrículas</TableHead>
                      <TableHead>Criado em</TableHead>
                      <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {courses.map((course) => (
                      <TableRow key={course.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{course.name}</div>
                            {course.description && (
                              <div className="text-sm text-muted-foreground">
                                {course.description}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {course.duration ? `${course.duration}h` : '-'}
                        </TableCell>
                        <TableCell>
                          {course.numberOfLessons || '-'}
                        </TableCell>
                        <TableCell>{formatCurrency(course.price)}</TableCell>
                        <TableCell>
                          <Badge variant={course.allowsMakeup ? 'success' : 'secondary'}>
                            {course.allowsMakeup ? 'Sim' : 'Não'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={course.isActive ? 'success' : 'secondary'}>
                            {course.isActive ? 'Ativo' : 'Inativo'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Users className="w-4 h-4 mr-1" />
                            {course._count?.classes || 0}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Users className="w-4 h-4 mr-1" />
                            {course._count?.enrollments || 0}
                          </div>
                        </TableCell>
                        <TableCell>{formatDate(course.createdAt)}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            {course.numberOfLessons && course.numberOfLessons > 0 && (
                              <Button
                                onClick={() => router.push(`/admin/courses/${course.id}/lessons`)}
                                variant="outline"
                                size="sm"
                                className="border-green-500 text-green-600 hover:bg-green-500 hover:text-white transition-all duration-300"
                                title="Editar Aulas"
                              >
                                <BookMarked className="w-4 h-4" />
                              </Button>
                            )}
                            <Button
                              onClick={() => navigateToMode('edit', course.id)}
                              variant="outline"
                              size="sm"
                              className="border-[#667eea] text-[#667eea] hover:bg-[#667eea] hover:text-white transition-all duration-300"
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              onClick={() => handleDelete(course.id, course.name)}
                              variant="outline"
                              size="sm"
                              className="border-red-500 text-red-500 hover:bg-red-500 hover:text-white transition-all duration-300"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Classes List Section */}
        <Card className="shadow-xl border-0 mt-8">
          <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg">
            <CardTitle className="text-2xl font-bold text-gray-900 flex items-center">
              <Users className="w-6 h-6 mr-2 text-[#667eea]" />
              Turmas por Curso
            </CardTitle>
            <CardDescription className="text-lg">
              Estatísticas das turmas organizadas por curso
            </CardDescription>
          </CardHeader>
          <CardContent className="p-6">
            {isLoadingClasses ? (
              <div className="text-center py-12">
                <Loader2 className="mx-auto animate-spin" size={32} />
                <p className="mt-4 text-muted-foreground">Carregando turmas...</p>
              </div>
            ) : (
              <div className="space-y-6">
                {courses.map((course) => {
                  const courseClasses = classes.filter(cls => cls.course.id === course.id);
                  
                  if (courseClasses.length === 0) return null;

                  return (
                    <div key={course.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-4">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">{course.name}</h3>
                          <p className="text-sm text-gray-600">{courseClasses.length} turma{courseClasses.length !== 1 ? 's' : ''}</p>
                        </div>
                        <Button
                          onClick={() => router.push('/admin/classes')}
                          variant="outline"
                          size="sm"
                          className="border-[#667eea] text-[#667eea] hover:bg-[#667eea] hover:text-white"
                        >
                          Ver Todas
                        </Button>
                      </div>
                      
                      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        {courseClasses.map((classItem) => (
                          <Card key={classItem.id} className="hover:shadow-md transition-shadow">
                            <CardContent className="p-4">
                              <div className="flex items-start justify-between mb-2">
                                <h4 className="font-medium text-gray-900 truncate">{classItem.name}</h4>
                                <Badge variant={classItem.isActive ? 'success' : 'secondary'}>
                                  {classItem.isActive ? 'Ativa' : 'Inativa'}
                                </Badge>
                              </div>
                              
                              <div className="space-y-2 text-sm text-gray-600">
                                <div className="flex items-center">
                                  <Calendar className="w-4 h-4 mr-2" />
                                  <span>Início: {new Date(classItem.startDate).toLocaleDateString('pt-BR')}</span>
                                </div>
                                {classItem.endDate && (
                                  <div className="flex items-center">
                                    <Calendar className="w-4 h-4 mr-2" />
                                    <span>Fim: {new Date(classItem.endDate).toLocaleDateString('pt-BR')}</span>
                                  </div>
                                )}
                                <div className="flex items-center">
                                  <Users className="w-4 h-4 mr-2" />
                                  <span>{classItem._count.enrollments} aluno{classItem._count.enrollments !== 1 ? 's' : ''}</span>
                                  {classItem.maxStudents && (
                                    <span className="text-gray-400"> / {classItem.maxStudents}</span>
                                  )}
                                </div>
                                <div className="flex items-center">
                                  <BookOpen className="w-4 h-4 mr-2" />
                                  <span>{classItem._count.lessons} aula{classItem._count.lessons !== 1 ? 's' : ''}</span>
                                </div>
                              </div>

                              <div className="mt-3 pt-3 border-t">
                                <div className="flex items-center justify-between text-xs">
                                  <span className="text-gray-500">
                                    Taxa de Ocupação:
                                  </span>
                                  <span className="font-medium">
                                    {classItem.maxStudents 
                                      ? `${Math.round((classItem._count.enrollments / classItem.maxStudents) * 100)}%`
                                      : `${classItem._count.enrollments} alunos`
                                    }
                                  </span>
                                </div>
                              </div>

                              <Button
                                onClick={() => router.push(`/admin/classes/${classItem.id}`)}
                                variant="outline"
                                size="sm"
                                className="w-full mt-3 text-xs"
                              >
                                Ver Detalhes
                              </Button>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>
                  );
                })}

                {classes.length === 0 && (
                  <div className="text-center py-12">
                    <div className="w-20 h-20 bg-gradient-to-br from-[#667eea] to-[#764ba2] rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-xl">
                      <Users className="w-10 h-10 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">
                      Nenhuma turma encontrada
                    </h3>
                    <p className="text-gray-600 mb-6 max-w-md mx-auto">
                      Comece criando turmas para os cursos disponíveis.
                    </p>
                    <Button 
                      onClick={() => router.push('/admin/classes?mode=new')} 
                      className="bg-gradient-to-r from-[#667eea] to-[#764ba2] hover:from-[#5a6fd8] hover:to-[#6b4190] text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300"
                      size="lg"
                    >
                      <Plus className="w-5 h-5 mr-2" />
                      Criar Primeira Turma
                    </Button>
                  </div>
                )}

                {classes.length > 0 && courses.filter(course => classes.some(cls => cls.course.id === course.id)).length === 0 && (
                  <div className="text-center py-8">
                    <p className="text-gray-600">
                      Nenhuma turma encontrada para os cursos existentes.
                    </p>
                    <Button 
                      onClick={() => router.push('/admin/classes?mode=new')} 
                      className="mt-4 bg-gradient-to-r from-[#667eea] to-[#764ba2] hover:from-[#5a6fd8] hover:to-[#6b4190] text-white"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Criar Turma
                    </Button>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      <ConfirmationDialog />
      
    </div>
  );
}

export default function CoursesPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="mx-auto animate-spin" size={48} />
          <p className="mt-4 text-muted-foreground">Carregando...</p>
        </div>
      </div>
    }>
      <CoursesPageContent />
    </Suspense>
  );
}