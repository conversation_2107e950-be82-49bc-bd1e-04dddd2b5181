'use client';

import React, { useEffect, useState, Suspense, useRef } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Loader2, CheckCircle, AlertCircle, Key, UserCheck } from 'lucide-react';

function VerifyOTPContent() {
  const [isVerifying, setIsVerifying] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [studentData, setStudentData] = useState<any>(null);
  const hasAttemptedRef = useRef(false);
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const token = searchParams.get('token');
    const redirectPath = searchParams.get('redirect') || '/';

    if (!token) {
      setError('Token OTP não encontrado na URL');
      return;
    }

    // Prevent multiple verification attempts
    if (hasAttemptedRef.current) {
      return;
    }

    const handleVerification = async () => {
      hasAttemptedRef.current = true;
      setIsVerifying(true);
      setError('');

      try {
        const response = await fetch('/api/auth/verify-otp', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ token })
        });

        const data = await response.json();

        if (!response.ok) {
          setError(data.error || 'Erro ao verificar token OTP');
          return;
        }

        // Store auth token
        localStorage.setItem('auth_token', data.token);
        
        // Store student info for display
        setStudentData(data.student);
        setSuccess(true);

        // Redirect after successful verification
        setTimeout(() => {
          router.push(redirectPath);
        }, 3000);

      } catch (err) {
        console.error('OTP verification error:', err);
        setError('Erro inesperado durante a verificação');
      } finally {
        setIsVerifying(false);
      }
    };

    handleVerification();
  }, [searchParams, router]);

  if (isVerifying) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-blue-100 p-4">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <Loader2 className="w-8 h-8 text-green-600 animate-spin" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Verificando acesso...
                </h3>
                <p className="text-gray-600 text-sm">
                  Validando seu token de acesso para reposição de aula.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (success && studentData) {
    const redirectPath = searchParams.get('redirect') || '/';
    
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-blue-100 p-4">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Bem-vindo, {studentData.name}! 🎓
                </h3>
                <p className="text-gray-600 text-sm mb-4">
                  Acesso autorizado com sucesso!
                </p>
                <div className="bg-blue-50 p-3 rounded-lg mb-4">
                  <div className="flex items-center justify-center mb-2">
                    <UserCheck className="w-5 h-5 text-blue-600 mr-2" />
                    <span className="text-sm font-medium text-blue-800">Dados do Aluno</span>
                  </div>
                  <div className="text-xs text-blue-700 space-y-1">
                    <div><strong>Nome:</strong> {studentData.name}</div>
                    {studentData.email && <div><strong>Email:</strong> {studentData.email}</div>}
                    {studentData.phone && <div><strong>Telefone:</strong> {studentData.phone}</div>}
                  </div>
                </div>
                <p className="text-gray-500 text-xs">
                  Redirecionando em 3 segundos...
                  <br />
                  <span className="font-medium">Destino: {redirectPath}</span>
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-blue-100 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-12 h-12 bg-gradient-to-r from-green-600 to-blue-600 rounded-full flex items-center justify-center">
            <Key className="w-6 h-6 text-white" />
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900">
            VoxStudent
          </CardTitle>
          <CardDescription>
            Acesso para Reposição de Aulas
          </CardDescription>
        </CardHeader>

        <CardContent>
          <div className="text-center space-y-4">
            <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
              <AlertCircle className="w-8 h-8 text-red-600" />
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Erro na verificação
              </h3>
              
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>{error}</AlertDescription>
              </Alert>

              <div className="text-xs text-gray-500 mb-4">
                <p><strong>Dica:</strong> Certifique-se de que você está usando o link correto enviado via WhatsApp.</p>
                <p>Os tokens para reposição expiram em 24 horas.</p>
              </div>
            </div>

            <Button
              onClick={() => router.push('/')}
              className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
            >
              Ir para página inicial
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function VerifyOTP() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-blue-100 p-4">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <Loader2 className="w-8 h-8 text-green-600 animate-spin" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Carregando...
                </h3>
                <p className="text-gray-600 text-sm">
                  Preparando verificação do token de reposição.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    }>
      <VerifyOTPContent />
    </Suspense>
  );
}