'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { CalendarIcon, ClockIcon, BookOpenIcon, ArrowLeftIcon, CheckCircleIcon, Loader2, CalendarDays } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';

interface Course {
  id: string;
  name: string;
  _count: {
    classes: number;
  };
}

interface CompletedLesson {
  id: string;
  title: string;
  lessonNumber: number | null;
  originalDate: string;
  attendedDate: string;
  class: {
    id: string;
    name: string;
    course: {
      id: string;
      name: string;
    };
  };
}

interface AvailableLesson {
  id: string;
  title: string;
  lessonNumber: number | null;
  scheduledDate: string;
  class: {
    id: string;
    name: string;
    course: {
      id: string;
      name: string;
    };
  };
}

function RecoveryPageContent() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const authenticatedFetch = useAuthenticatedFetch();

  const [currentView, setCurrentView] = useState<'courses' | 'lessons' | 'retake'>('courses');
  const [isLoading, setIsLoading] = useState(true);
  const [isBooking, setIsBooking] = useState(false);
  
  const [courses, setCourses] = useState<Course[]>([]);
  const [completedLessons, setCompletedLessons] = useState<CompletedLesson[]>([]);
  const [selectedLesson, setSelectedLesson] = useState<CompletedLesson | null>(null);
  const [availableLessons, setAvailableLessons] = useState<AvailableLesson[]>([]);
  const [selectedRetakeLesson, setSelectedRetakeLesson] = useState<string>('');
  
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<string>('');

  useEffect(() => {
    if (authLoading) return;
    
    if (!user) {
      router.push('/login');
      return;
    }

    loadCompletedCourses();
  }, [user, authLoading, router]);

  const loadCompletedCourses = async () => {
    try {
      setIsLoading(true);
      setError('');

      const response = await authenticatedFetch('/api/recovery/completed-courses');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao carregar cursos concluídos');
      }

      setCourses(data.data);
    } catch (error) {
      console.error('Error loading completed courses:', error);
      setError(error instanceof Error ? error.message : 'Erro desconhecido');
    } finally {
      setIsLoading(false);
    }
  };

  const loadCompletedLessons = async (courseId: string) => {
    try {
      setIsLoading(true);
      setError('');

      const response = await authenticatedFetch(`/api/recovery/completed-lessons?courseId=${courseId}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao carregar aulas concluídas');
      }

      setCompletedLessons(data.data);
      setCurrentView('lessons');
    } catch (error) {
      console.error('Error loading completed lessons:', error);
      setError(error instanceof Error ? error.message : 'Erro desconhecido');
    } finally {
      setIsLoading(false);
    }
  };

  const loadRetakeLessons = async (lesson: CompletedLesson) => {
    try {
      setIsLoading(true);
      setError('');
      setSelectedLesson(lesson);

      const response = await authenticatedFetch(
        `/api/recovery/retake-lessons?lessonNumber=${lesson.lessonNumber}&courseId=${lesson.class.course.id}`
      );
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao carregar aulas disponíveis');
      }

      setAvailableLessons(data.data);
      setCurrentView('retake');
    } catch (error) {
      console.error('Error loading retake lessons:', error);
      setError(error instanceof Error ? error.message : 'Erro desconhecido');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBookRetake = async () => {
    if (!selectedRetakeLesson) {
      setError('Por favor, selecione uma aula para refazer.');
      return;
    }

    try {
      setIsBooking(true);
      setError('');

      const response = await authenticatedFetch('/api/recovery/book-retake', {
        method: 'POST',
        body: JSON.stringify({
          lessonId: selectedRetakeLesson,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao agendar aula para refazer');
      }

      setSuccess('Aula agendada para refazer com sucesso! Você receberá uma confirmação em breve.');
      
      // Clear the form
      setSelectedRetakeLesson('');
      
      // Redirect after a delay
      setTimeout(() => {
        router.push('/');
      }, 3000);
    } catch (error) {
      console.error('Error booking retake:', error);
      setError(error instanceof Error ? error.message : 'Erro desconhecido');
    } finally {
      setIsBooking(false);
    }
  };

  const handleSelectCourse = (course: Course) => {
    loadCompletedLessons(course.id);
  };

  const handleBackToCourses = () => {
    setCurrentView('courses');
    setCompletedLessons([]);
    setError('');
  };

  const handleBackToLessons = () => {
    setCurrentView('lessons');
    setSelectedLesson(null);
    setAvailableLessons([]);
    setSelectedRetakeLesson('');
    setError('');
  };

  if (authLoading || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="mx-auto animate-spin" size={48} />
          <p className="text-gray-600 mt-4">
            {authLoading ? 'Verificando autenticação...' : 'Carregando dados...'}
          </p>
        </div>
      </div>
    );
  }

  if (success) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-green-600 flex items-center gap-2">
              <CheckCircleIcon className="h-6 w-6" />
              Sucesso!
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Alert>
              <AlertDescription className="text-green-700">
                {success}
              </AlertDescription>
            </Alert>
            <p className="text-sm text-gray-600 mt-2">
              Redirecionando automaticamente...
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="w-full max-w-[1600px] mx-auto">
        {/* Header */}
        <div className="bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-2xl p-8 text-white shadow-xl mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {currentView !== 'courses' && (
                <Button
                  onClick={currentView === 'lessons' ? handleBackToCourses : handleBackToLessons}
                  variant="outline"
                  size="sm"
                  className="bg-white/20 text-white border-white/30 hover:bg-white/30"
                >
                  <ArrowLeftIcon className="w-4 h-4 mr-2" />
                  Voltar
                </Button>
              )}
              <div>
                <h1 className="text-3xl font-bold mb-2 flex items-center">
                  <BookOpenIcon className="w-8 h-8 mr-3" />
                  Refazer Aulas
                </h1>
                <p className="text-white/90">
                  {currentView === 'courses' && 'Selecione um curso para ver as aulas que você já concluiu'}
                  {currentView === 'lessons' && 'Escolha uma aula para refazer'}
                  {currentView === 'retake' && 'Selecione uma turma disponível para refazer a aula'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Courses View */}
        {currentView === 'courses' && (
          <Card className="shadow-xl border-0">
            <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg">
              <CardTitle className="text-2xl font-bold text-gray-900 flex items-center">
                <BookOpenIcon className="w-6 h-6 mr-2 text-[#667eea]" />
                Cursos Disponíveis
              </CardTitle>
              <CardDescription className="text-lg">
                {courses.length} curso{courses.length !== 1 ? 's' : ''} com aulas concluídas
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {courses.length === 0 ? (
              <div className="col-span-full text-center py-12">
                <div className="w-20 h-20 bg-gradient-to-br from-[#667eea] to-[#764ba2] rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-xl">
                  <CalendarDays className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  Nenhuma aula disponível para refazer
                </h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  Aguarde enquanto você conclui mais aulas em seus cursos.
                </p>
              </div>
            ) : (
              courses.map((course) => (
                <Card key={course.id} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardContent className="p-6" onClick={() => handleSelectCourse(course)}>
                    <h3 className="font-semibold text-lg mb-2">{course.name}</h3>
                    <p className="text-sm text-gray-600">
                      {course._count.classes} turma{course._count.classes !== 1 ? 's' : ''} com aulas concluídas
                    </p>
                  </CardContent>
                </Card>
              ))
            )}
            </div>
            </CardContent>
          </Card>
        )}

        {/* Lessons View */}
        {currentView === 'lessons' && (
          <Card className="shadow-xl border-0">
            <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg">
              <CardTitle className="text-2xl font-bold text-gray-900 flex items-center">
                <BookOpenIcon className="w-6 h-6 mr-2 text-[#667eea]" />
                Aulas Concluídas
              </CardTitle>
              <CardDescription className="text-lg">
                {completedLessons.length} aula{completedLessons.length !== 1 ? 's' : ''} disponível{completedLessons.length !== 1 ? 'is' : ''} para refazer
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-4">
            {completedLessons.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-20 h-20 bg-gradient-to-br from-[#667eea] to-[#764ba2] rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-xl">
                  <BookOpenIcon className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  Nenhuma aula concluída encontrada
                </h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  Você ainda não concluiu nenhuma aula neste curso.
                </p>
              </div>
            ) : (
              completedLessons.map((lesson) => (
                <Card key={lesson.id} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardContent className="p-6" onClick={() => loadRetakeLessons(lesson)}>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold text-lg">{lesson.title}</h3>
                          {lesson.lessonNumber && (
                            <Badge variant="secondary">
                              Aula {lesson.lessonNumber}
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{lesson.class.name}</p>
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <div className="flex items-center gap-1">
                            <CalendarIcon className="h-4 w-4" />
                            Concluída em {format(new Date(lesson.attendedDate), "dd/MM/yyyy", { locale: ptBR })}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Retake View */}
        {currentView === 'retake' && selectedLesson && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Selected Lesson Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpenIcon className="h-5 w-5" />
                  Aula Selecionada para Refazer
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm font-medium text-gray-700">Curso</p>
                  <p className="font-medium">{selectedLesson.class.course.name}</p>
                </div>

                <Separator />

                <div>
                  <p className="text-sm font-medium text-gray-700">Aula</p>
                  <div className="flex items-center gap-2">
                    <p className="font-medium">{selectedLesson.title}</p>
                    {selectedLesson.lessonNumber && (
                      <Badge variant="secondary">
                        Aula {selectedLesson.lessonNumber}
                      </Badge>
                    )}
                  </div>
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-700">Concluída em</p>
                  <p className="flex items-center gap-2">
                    <CalendarIcon className="h-4 w-4 text-gray-400" />
                    {format(
                      new Date(selectedLesson.attendedDate),
                      "dd 'de' MMMM 'de' yyyy",
                      { locale: ptBR }
                    )}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Available Classes */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CalendarIcon className="h-5 w-5" />
                  Turmas Disponíveis
                </CardTitle>
                <CardDescription>
                  Selecione uma turma para refazer esta aula
                </CardDescription>
              </CardHeader>
              <CardContent>
                {availableLessons.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="w-20 h-20 bg-gradient-to-br from-[#667eea] to-[#764ba2] rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-xl">
                      <CalendarIcon className="w-10 h-10 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">
                      Nenhuma turma disponível
                    </h3>
                    <p className="text-gray-600 mb-6 max-w-md mx-auto">
                      Não há turmas disponíveis para refazer esta aula no momento. Entre em contato conosco para mais informações.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {availableLessons.map((lesson) => (
                      <button
                        key={lesson.id}
                        type="button"
                        role="radio"
                        aria-checked={selectedRetakeLesson === lesson.id}
                        className={`w-full text-left p-4 rounded-lg border transition-all focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          selectedRetakeLesson === lesson.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setSelectedRetakeLesson(lesson.id)}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="font-medium text-gray-900 mb-1">
                              {lesson.class.name}
                            </h4>
                            <div className="flex items-center gap-4 text-sm text-gray-500">
                              <div className="flex items-center gap-1">
                                <CalendarIcon className="h-4 w-4" />
                                {format(
                                  new Date(lesson.scheduledDate),
                                  "dd/MM/yyyy",
                                  { locale: ptBR }
                                )}
                              </div>
                              <div className="flex items-center gap-1">
                                <ClockIcon className="h-4 w-4" />
                                {format(
                                  new Date(lesson.scheduledDate),
                                  "HH:mm",
                                  { locale: ptBR }
                                )}
                              </div>
                            </div>
                          </div>
                          {selectedRetakeLesson === lesson.id && (
                            <Badge variant="default">Selecionada</Badge>
                          )}
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {/* Error Alert */}
        {error && (
          <Alert className="mt-6">
            <AlertDescription className="text-red-700">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Action Buttons for Retake View */}
        {currentView === 'retake' && (
          <div className="mt-8 flex gap-4 justify-end">
            <Button
              variant="outline"
              onClick={handleBackToLessons}
            >
              Cancelar
            </Button>
            <Button
              onClick={handleBookRetake}
              disabled={!selectedRetakeLesson || isBooking || availableLessons.length === 0}
            >
              {isBooking ? 'Agendando...' : 'Agendar para Refazer'}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}

export default function RecoveryPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando página de reposição...</p>
        </div>
      </div>
    }>
      <RecoveryPageContent />
    </Suspense>
  );
}