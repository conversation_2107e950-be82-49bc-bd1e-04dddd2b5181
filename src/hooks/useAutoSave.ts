import { useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';

export interface AutoSaveConfig {
  debounceMs?: number;
  enableToast?: boolean;
  toastMessage?: string;
}

export interface AutoSaveState {
  isAutoSaving: boolean;
  lastSaved: Date | null;
  hasUnsavedChanges: boolean;
  error: string | null;
}

interface UseAutoSaveProps<T> {
  data: T;
  onSave: (data: T) => Promise<boolean | void>;
  config?: AutoSaveConfig;
  isValid?: (data: T) => boolean;
  enabled?: boolean;
}

export function useAutoSave<T>({
  data,
  onSave,
  config = {},
  isValid = () => true,
  enabled = true
}: UseAutoSaveProps<T>) {
  const {
    debounceMs = 2000,
    enableToast = false, // Desabilitar por padrão para evitar spam
    toastMessage = 'Alterações salvas automaticamente'
  } = config;

  const [state, setState] = useState<AutoSaveState>({
    isAutoSaving: false,
    lastSaved: null,
    hasUnsavedChanges: false,
    error: null
  });

  const timeoutRef = useRef<NodeJS.Timeout>();
  const previousDataRef = useRef<string>();
  const isInitialMount = useRef(true);
  const isSavingRef = useRef(false);

  useEffect(() => {
    if (!enabled) return;

    const currentDataStr = JSON.stringify(data);
    
    // Se é o primeiro mount, apenas salva a referência
    if (isInitialMount.current) {
      isInitialMount.current = false;
      previousDataRef.current = currentDataStr;
      return;
    }

    // Se já está salvando, não faz nada
    if (isSavingRef.current) {
      return;
    }

    // Verifica se houve mudança
    const hasChanged = currentDataStr !== previousDataRef.current;

    if (hasChanged && isValid(data)) {
      setState(prev => ({ ...prev, hasUnsavedChanges: true }));

      // Limpa timeout anterior
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Configura novo timeout
      timeoutRef.current = setTimeout(async () => {
        if (isSavingRef.current) return;

        try {
          isSavingRef.current = true;
          setState(prev => ({ ...prev, isAutoSaving: true, error: null }));

          const result = await onSave(data);
          const success = result !== false;

          if (success) {
            previousDataRef.current = JSON.stringify(data);
            setState(prev => ({
              ...prev,
              isAutoSaving: false,
              lastSaved: new Date(),
              hasUnsavedChanges: false,
              error: null
            }));

            if (enableToast) {
              toast.success(toastMessage);
            }
          } else {
            throw new Error('Save failed');
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro ao salvar';
          setState(prev => ({
            ...prev,
            isAutoSaving: false,
            error: errorMessage
          }));

          if (enableToast) {
            toast.error(`Erro: ${errorMessage}`);
          }
        } finally {
          isSavingRef.current = false;
        }
      }, debounceMs);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [data, enabled, debounceMs, enableToast, toastMessage]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      isSavingRef.current = false;
    };
  }, []);

  const forceSave = async () => {
    if (isSavingRef.current || !enabled || !isValid(data)) return;

    try {
      isSavingRef.current = true;
      setState(prev => ({ ...prev, isAutoSaving: true, error: null }));

      const result = await onSave(data);
      const success = result !== false;

      if (success) {
        previousDataRef.current = JSON.stringify(data);
        setState(prev => ({
          ...prev,
          isAutoSaving: false,
          lastSaved: new Date(),
          hasUnsavedChanges: false,
          error: null
        }));

        if (enableToast) {
          toast.success(toastMessage);
        }
      } else {
        throw new Error('Save failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro ao salvar';
      setState(prev => ({
        ...prev,
        isAutoSaving: false,
        error: errorMessage
      }));

      if (enableToast) {
        toast.error(`Erro: ${errorMessage}`);
      }
    } finally {
      isSavingRef.current = false;
    }
  };

  const resetState = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    isSavingRef.current = false;
    setState({
      isAutoSaving: false,
      lastSaved: null,
      hasUnsavedChanges: false,
      error: null
    });
    previousDataRef.current = JSON.stringify(data);
  };

  const markAsSaved = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    isSavingRef.current = false;
    setState({
      isAutoSaving: false,
      lastSaved: new Date(),
      hasUnsavedChanges: false,
      error: null
    });
    previousDataRef.current = JSON.stringify(data);
    isInitialMount.current = false;
  };

  return {
    ...state,
    forceSave,
    resetState,
    markAsSaved
  };
}