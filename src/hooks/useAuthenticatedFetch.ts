import { useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';

interface FetchOptions extends RequestInit {
  headers?: HeadersInit;
}

export const useAuthenticatedFetch = () => {
  const { user } = useAuth();
  const router = useRouter();

  const authenticatedFetch = useCallback(async (url: string, options: FetchOptions = {}) => {
    // Check if user is authenticated
    if (!user) {
      router.push('/login?message=Já%20faz%20um%20tempo%20que%20você%20fez%20login.%20Por%20segurança,%20faça%20login%20novamente.');
      throw new Error('User not authenticated');
    }

    // Get token from localStorage (centralized here instead of in components)
    const token = localStorage.getItem('auth_token');
    
    if (!token) {
      router.push('/login?message=Já%20faz%20um%20tempo%20que%20você%20fez%20login.%20Por%20segurança,%20faça%20login%20novamente.');
      throw new Error('No auth token found');
    }

    // Prepare headers with authentication
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      ...options.headers,
    };

    // Make the request
    const response = await fetch(url, {
      ...options,
      headers,
    });

    // Handle authentication errors
    if (response.status === 401) {
      localStorage.removeItem('auth_token');
      router.push('/login?message=Já%20faz%20um%20tempo%20que%20você%20fez%20login.%20Por%20segurança,%20faça%20login%20novamente.');
      throw new Error('Authentication failed');
    }

    return response;
  }, [user, router]);

  return authenticatedFetch;
};