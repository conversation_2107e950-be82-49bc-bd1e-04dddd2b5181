Dockerfile
.dockerignore
node_modules
npm-debug.log
README.md
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.git
.gitignore
.next
.vercel
coverage
test-results
playwright-report
e2e-tests
tests
*.md
.vscode
.idea
*.log
.DS_Store
Thumbs.db
whatsapp-session
# Optimization: exclude heavy dev files
.git
.next
coverage
test-results
playwright-report
e2e-tests
tests
.vscode
.idea
database-backups
scripts/backup-database.sh
scripts/deploy.sh
scripts/build.sh
