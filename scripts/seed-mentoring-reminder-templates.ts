import { prisma } from '../src/lib/prisma';

const mentoringReminderTemplates = [
  {
    name: 'Lembrete Mentoria Padrão',
    category: 'mentoria',
    template: 'Ol<PERSON> {{nome_do_aluno}}! 👋\n\nLembramos que você tem uma mentoria agendada com {{nome_professor}} para {{data_mentoria}} às {{hora_inicio_mentoria}}.\n\n⏰ Duração: {{duracao_mentoria}} minutos\n📅 Data: {{data_mentoria}}\n🕐 Horário: {{hora_inicio_mentoria}} às {{hora_fim_mentoria}}\n\nNão esqueça de se preparar e estar pontual!\n\nAté breve! 📚',
    description: 'Template padrão para lembretes de mentoria com informações básicas'
  },
  {
    name: 'Lembrete Mentoria Detalhado',
    category: 'mentoria',
    template: 'Prezado(a) {{nome_do_aluno}},\n\nEste é um lembrete da sua mentoria agendada:\n\n👨‍🏫 Professor: {{nome_professor}}\n📅 Data: {{data_mentoria}}\n🕐 Horário: {{hora_inicio_mentoria}} às {{hora_fim_mentoria}}\n⏱️ Duração: {{duracao_mentoria}} minutos\n📍 Modalidade: Online\n\n📝 Dicas para aproveitar melhor a mentoria:\n• Prepare suas dúvidas com antecedência\n• Tenha em mãos o material de estudo\n• Acesse a plataforma alguns minutos antes\n• Mantenha um ambiente silencioso\n\nCaso precise reagendar, entre em contato conosco.\n\nBons estudos! 🎓\n{{nome_sistema}}',
    description: 'Template detalhado com dicas e orientações para a mentoria'
  },
  {
    name: 'Lembrete Mentoria Urgente',
    category: 'mentoria',
    template: '🚨 LEMBRETE URGENTE 🚨\n\n{{nome_do_aluno}}, sua mentoria começará em breve!\n\n👨‍🏫 Professor: {{nome_professor}}\n🕐 Horário: {{hora_inicio_mentoria}}\n⏰ Em {{duracao_mentoria}} minutos\n\nPor favor, acesse a plataforma agora para não perder sua sessão.\n\nVamos lá! 💪',
    description: 'Template para lembretes de última hora (1 hora antes ou menos)'
  },
  {
    name: 'Lembrete Mentoria Amigável',
    category: 'mentoria',
    template: 'Oi {{nome_do_aluno}}! 😊\n\nSó passando para lembrar que você tem mentoria hoje com {{nome_professor}}!\n\n📅 Quando: {{data_mentoria}} às {{hora_inicio_mentoria}}\n⏰ Duração: {{duracao_mentoria}} minutos\n\nJá separou suas dúvidas? É sempre bom ter uma lista pronta para aproveitar cada minutinho da mentoria! ✨\n\nNos vemos lá! 🤗\n\nEquipe {{nome_sistema}}',
    description: 'Template mais casual e amigável para criar proximidade com o aluno'
  },
  {
    name: 'Lembrete Mentoria Técnico',
    category: 'mentoria',
    template: 'Confirmação de Mentoria Agendada\n\n{{nome_do_aluno}}, segue o resumo da sua sessão:\n\n═══════════════════════════\nDETALHES DA MENTORIA\n═══════════════════════════\n\n👤 Mentor: {{nome_professor}}\n📧 Email: {{email_professor}}\n📅 Data: {{data_mentoria}}\n🕐 Início: {{hora_inicio_mentoria}}\n🕑 Término: {{hora_fim_mentoria}}\n⏱️ Duração: {{duracao_mentoria}} min\n🗓️ Dia da semana: {{dia_semana_mentoria}}\n\n═══════════════════════════\n\nPara suporte técnico: {{email_suporte}}\nPlataforma: {{url_sistema}}\n\nEsteja online 5 minutos antes do início.',
    description: 'Template formal com informações técnicas completas'
  }
];

export async function seedMentoringReminderTemplates() {
  console.log('🌱 Seeding mentoring reminder templates...');

  try {
    for (const template of mentoringReminderTemplates) {
      // Check if template already exists
      const existing = await prisma.reminderTemplate.findFirst({
        where: {
          name: template.name,
          category: template.category
        }
      });

      if (!existing) {
        await prisma.reminderTemplate.create({
          data: template
        });
        console.log(`✅ Created template: ${template.name}`);
      } else {
        console.log(`⚠️  Template already exists: ${template.name}`);
      }
    }

    console.log('✅ Mentoring reminder templates seeded successfully!');
  } catch (error) {
    console.error('❌ Error seeding mentoring reminder templates:', error);
    throw error;
  }
}

// Run if this file is executed directly
if (require.main === module) {
  seedMentoringReminderTemplates()
    .catch((error) => {
      console.error('Error running seed:', error);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}