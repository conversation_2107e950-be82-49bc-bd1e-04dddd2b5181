#!/usr/bin/env node

/**
 * Setup script for recovery reminder cron jobs
 * 
 * This script can be used to set up automated processing of recovery reminders
 * in production environments. It provides examples of how to schedule the jobs.
 * 
 * Usage:
 * node scripts/setup-recovery-cron.js
 */

const cron = require('node-cron');

// Import the recovery jobs
async function setupRecoveryJobs() {
  console.log('Setting up recovery reminder cron jobs...');

  try {
    // Import the recovery job functions
    const { recoveryJobs } = await import('../src/lib/cron/recovery-reminders.ts');

    // Schedule reminder processing every hour
    // This will check for pending reminders and send them
    cron.schedule('0 * * * *', async () => {
      console.log('Running hourly recovery reminder processing...');
      try {
        const result = await recoveryJobs.processReminders();
        console.log('Recovery reminder processing completed:', result);
      } catch (error) {
        console.error('Recovery reminder processing failed:', error);
      }
    }, {
      scheduled: true,
      timezone: "America/Sao_Paulo" // Adjust timezone as needed
    });

    // Schedule cleanup daily at 2 AM
    // This will remove old reminders and expired OTPs
    cron.schedule('0 2 * * *', async () => {
      console.log('Running daily recovery data cleanup...');
      try {
        const result = await recoveryJobs.cleanupData();
        console.log('Recovery data cleanup completed:', result);
      } catch (error) {
        console.error('Recovery data cleanup failed:', error);
      }
    }, {
      scheduled: true,
      timezone: "America/Sao_Paulo" // Adjust timezone as needed
    });

    console.log('Recovery reminder cron jobs configured successfully!');
    console.log('Jobs scheduled:');
    console.log('- Reminder processing: Every hour');
    console.log('- Data cleanup: Daily at 2 AM');
    
    // Keep the process running
    process.on('SIGINT', () => {
      console.log('Shutting down recovery cron jobs...');
      process.exit(0);
    });

  } catch (error) {
    console.error('Failed to set up recovery cron jobs:', error);
    process.exit(1);
  }
}

// Alternative: Manual job execution for testing
async function runManualJobs() {
  const command = process.argv[2];
  
  try {
    const { recoveryJobs } = await import('../src/lib/cron/recovery-reminders.ts');

    switch (command) {
      case 'process':
        console.log('Manually processing recovery reminders...');
        const processResult = await recoveryJobs.processReminders();
        console.log('Process result:', processResult);
        break;
        
      case 'cleanup':
        console.log('Manually cleaning up recovery data...');
        const cleanupResult = await recoveryJobs.cleanupData();
        console.log('Cleanup result:', cleanupResult);
        break;
        
      default:
        console.log('Starting scheduled cron jobs...');
        await setupRecoveryJobs();
    }
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

// Check if running in manual mode
if (process.argv.length > 2) {
  runManualJobs();
} else {
  setupRecoveryJobs();
}

// Example systemd service file content:
console.log(`
=== EXAMPLE SYSTEMD SERVICE ===
Save this as /etc/systemd/system/vox-recovery.service:

[Unit]
Description=VOX Student Recovery Reminder Service
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/vox-student-nextjs
ExecStart=/usr/bin/node scripts/setup-recovery-cron.js
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target

Then run:
sudo systemctl daemon-reload
sudo systemctl enable vox-recovery.service
sudo systemctl start vox-recovery.service
=== END EXAMPLE ===
`);