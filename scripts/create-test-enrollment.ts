import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createTestEnrollment() {
  try {
    console.log('Creating test enrollment...');

    // Get first student and first class
    const student = await prisma.student.findFirst();
    const classData = await prisma.class.findFirst({
      include: {
        course: true
      }
    });

    if (!student || !classData) {
      console.log('No student or class found in database');
      return;
    }

    console.log(`Student: ${student.name} (${student.id})`);
    console.log(`Class: ${classData.name} (${classData.id})`);
    console.log(`Course: ${classData.course.name} (${classData.course.id})`);

    // Create enrollment
    const enrollment = await prisma.enrollment.create({
      data: {
        studentId: student.id,
        courseId: classData.course.id,
        classId: classData.id,
        status: 'active',
        type: 'regular'
      }
    });

    console.log('✅ Test enrollment created successfully!');
    console.log(`Enrollment ID: ${enrollment.id}`);
    console.log(`Test API URL: http://localhost:3001/api/students/${student.id}/classes/${classData.id}/lessons`);

  } catch (error) {
    console.error('❌ Error creating test enrollment:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestEnrollment();