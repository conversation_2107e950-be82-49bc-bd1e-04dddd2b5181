<!-- This would be a PNG file, but since I can't create binary files, I'll create an SVG version that can be converted -->
<svg width="180" height="180" viewBox="0 0 180 180" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="diamondGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#667eea" />
      <stop offset="100%" stop-color="#764ba2" />
    </linearGradient>
    <linearGradient id="highlightGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#ffffff" stop-opacity="0.3" />
      <stop offset="100%" stop-color="#ffffff" stop-opacity="0.1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="4" stdDeviation="4" flood-color="#000000" flood-opacity="0.15"/>
    </filter>
  </defs>
  
  <!-- Background rounded rectangle for iOS -->
  <rect width="180" height="180" rx="40" ry="40" fill="url(#diamondGradient)" />
  
  <!-- Main Diamond Shape (scaled up) -->
  <path
    d="M90 30 L140 65 L90 150 L40 65 Z"
    fill="url(#diamondGradient)"
    filter="url(#shadow)"
    fill-opacity="0.9"
  />
  
  <!-- Top facet highlight -->
  <path
    d="M90 30 L115 55 L90 70 L65 55 Z"
    fill="url(#highlightGradient)"
  />
  
  <!-- Left facet -->
  <path
    d="M40 65 L65 55 L90 70 L90 150 Z"
    fill="url(#diamondGradient)"
    fill-opacity="0.7"
  />
  
  <!-- Right facet -->
  <path
    d="M140 65 L115 55 L90 70 L90 150 Z"
    fill="url(#diamondGradient)"
    fill-opacity="0.8"
  />
  
  <!-- Center shine -->
  <ellipse
    cx="90"
    cy="65"
    rx="14"
    ry="8"
    fill="url(#highlightGradient)"
    transform="rotate(-15 90 65)"
  />
  
  <!-- Sparkles -->
  <circle cx="75" cy="45" r="3" fill="white" fill-opacity="0.8" />
  <circle cx="105" cy="80" r="2" fill="white" fill-opacity="0.6" />
  <circle cx="70" cy="90" r="2" fill="white" fill-opacity="0.5" />
</svg>